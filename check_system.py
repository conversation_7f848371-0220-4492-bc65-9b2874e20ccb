#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام والتحقق من جاهزيته
System Check and Readiness Verification
"""

import os
import sys
import subprocess
import importlib
from datetime import datetime

def print_header():
    """طباعة رأس التقرير"""
    print("=" * 70)
    print("🔍 فحص نظام إدارة قطع الغيار - AutoParts Manager V5.1")
    print("=" * 70)
    print(f"📅 تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    print()

def check_python():
    """فحص Python"""
    print("🐍 فحص Python...")
    try:
        version = sys.version_info
        version_str = f"{version.major}.{version.minor}.{version.micro}"
        print(f"   ✅ Python مثبت: الإصدار {version_str}")
        
        if version.major >= 3 and version.minor >= 8:
            print("   ✅ إصدار Python مناسب")
            return True
        else:
            print("   ⚠️  إصدار Python قديم (يُنصح بـ 3.8 أو أحدث)")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في فحص Python: {e}")
        return False

def check_pip():
    """فحص pip"""
    print("\n📦 فحص pip...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ pip متوفر: {result.stdout.strip()}")
            return True
        else:
            print("   ❌ pip غير متوفر")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في فحص pip: {e}")
        return False

def check_required_packages():
    """فحص الحزم المطلوبة"""
    print("\n📚 فحص الحزم المطلوبة...")
    
    required_packages = [
        ('flask', 'Flask'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('flask_login', 'Flask-Login'),
        ('werkzeug', 'Werkzeug'),
        ('sqlalchemy', 'SQLAlchemy'),
        ('jinja2', 'Jinja2'),
        ('reportlab', 'ReportLab'),
        ('openpyxl', 'OpenPyXL'),
        ('pandas', 'Pandas'),
        ('PIL', 'Pillow'),
        ('barcode', 'python-barcode'),
    ]
    
    installed = []
    missing = []
    
    for package_name, display_name in required_packages:
        try:
            importlib.import_module(package_name)
            print(f"   ✅ {display_name}")
            installed.append(display_name)
        except ImportError:
            print(f"   ❌ {display_name} - غير مثبت")
            missing.append(display_name)
    
    print(f"\n   📊 النتيجة: {len(installed)} مثبت، {len(missing)} مفقود")
    
    if missing:
        print("\n   🔧 لتثبيت الحزم المفقودة:")
        print("      pip install -r requirements.txt")
    
    return len(missing) == 0

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة...")
    
    required_files = [
        'app.py',
        'models.py',
        'utils.py',
        'requirements.txt',
        'init_db.py',
        'start.bat',
        'install.bat'
    ]
    
    required_dirs = [
        'templates',
        'static',
        'routes'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # فحص الملفات
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - مفقود")
            missing_files.append(file)
    
    # فحص المجلدات
    for dir in required_dirs:
        if os.path.exists(dir) and os.path.isdir(dir):
            print(f"   ✅ {dir}/")
        else:
            print(f"   ❌ {dir}/ - مفقود")
            missing_dirs.append(dir)
    
    return len(missing_files) == 0 and len(missing_dirs) == 0

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    if os.path.exists('autoparts.db'):
        size = os.path.getsize('autoparts.db')
        size_mb = size / (1024 * 1024)
        print(f"   ✅ قاعدة البيانات موجودة ({size_mb:.2f} MB)")
        
        # محاولة الاتصال بقاعدة البيانات
        try:
            import sqlite3
            conn = sqlite3.connect('autoparts.db')
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            
            print(f"   ✅ قاعدة البيانات تحتوي على {len(tables)} جدول")
            return True
        except Exception as e:
            print(f"   ⚠️  خطأ في قراءة قاعدة البيانات: {e}")
            return False
    else:
        print("   ⚠️  قاعدة البيانات غير موجودة")
        print("   💡 شغل: python init_db.py لإنشائها")
        return False

def check_directories():
    """فحص المجلدات المطلوبة"""
    print("\n📂 فحص المجلدات المطلوبة...")
    
    required_dirs = [
        'exports',
        'exports/reports',
        'exports/excel',
        'backups',
        'uploads',
        'static/uploads',
        'logs'
    ]
    
    missing_dirs = []
    
    for dir in required_dirs:
        if os.path.exists(dir):
            print(f"   ✅ {dir}/")
        else:
            print(f"   ⚠️  {dir}/ - غير موجود")
            missing_dirs.append(dir)
    
    if missing_dirs:
        print("\n   🔧 إنشاء المجلدات المفقودة...")
        for dir in missing_dirs:
            try:
                os.makedirs(dir, exist_ok=True)
                print(f"   ✅ تم إنشاء {dir}/")
            except Exception as e:
                print(f"   ❌ فشل في إنشاء {dir}/: {e}")
    
    return True

def check_permissions():
    """فحص الصلاحيات"""
    print("\n🔐 فحص الصلاحيات...")
    
    # فحص صلاحية الكتابة
    try:
        test_file = 'test_write_permission.tmp'
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        print("   ✅ صلاحية الكتابة متوفرة")
        write_permission = True
    except Exception as e:
        print(f"   ❌ صلاحية الكتابة غير متوفرة: {e}")
        write_permission = False
    
    # فحص صلاحية إنشاء المجلدات
    try:
        test_dir = 'test_dir_permission'
        os.makedirs(test_dir, exist_ok=True)
        os.rmdir(test_dir)
        print("   ✅ صلاحية إنشاء المجلدات متوفرة")
        dir_permission = True
    except Exception as e:
        print(f"   ❌ صلاحية إنشاء المجلدات غير متوفرة: {e}")
        dir_permission = False
    
    return write_permission and dir_permission

def test_import_app():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, os.getcwd())
        from app import app
        print("   ✅ تم استيراد التطبيق بنجاح")
        
        # اختبار إنشاء context
        with app.app_context():
            print("   ✅ تم إنشاء application context")
        
        return True
    except Exception as e:
        print(f"   ❌ فشل في استيراد التطبيق: {e}")
        return False

def generate_report(checks):
    """إنشاء تقرير النتائج"""
    print("\n" + "=" * 70)
    print("📊 تقرير النتائج النهائي")
    print("=" * 70)
    
    passed = sum(checks.values())
    total = len(checks)
    percentage = (passed / total) * 100
    
    print(f"✅ نجح: {passed}/{total} ({percentage:.1f}%)")
    
    if percentage == 100:
        print("\n🎉 النظام جاهز للتشغيل!")
        print("💡 لتشغيل النظام: python app.py أو start.bat")
    elif percentage >= 80:
        print("\n⚠️  النظام جاهز تقريباً مع بعض التحذيرات")
        print("💡 يمكن تشغيل النظام ولكن قد تواجه بعض المشاكل")
    else:
        print("\n❌ النظام غير جاهز للتشغيل")
        print("🔧 يرجى إصلاح المشاكل المذكورة أعلاه")
    
    print("\n📋 تفاصيل الفحص:")
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"   {status} {check_name}")
    
    print("\n💡 للمساعدة:")
    print("   📖 راجع ملف README.md")
    print("   📥 راجع ملف INSTALLATION_GUIDE.md")
    print("   🔧 شغل install.bat للتثبيت التلقائي")
    
    return percentage >= 80

def main():
    """الدالة الرئيسية"""
    print_header()
    
    checks = {}
    
    # تشغيل جميع الفحوصات
    checks['Python'] = check_python()
    checks['pip'] = check_pip()
    checks['الحزم المطلوبة'] = check_required_packages()
    checks['الملفات المطلوبة'] = check_files()
    checks['قاعدة البيانات'] = check_database()
    checks['المجلدات'] = check_directories()
    checks['الصلاحيات'] = check_permissions()
    checks['استيراد التطبيق'] = test_import_app()
    
    # إنشاء التقرير النهائي
    success = generate_report(checks)
    
    print("\n" + "=" * 70)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  تم إيقاف الفحص بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
