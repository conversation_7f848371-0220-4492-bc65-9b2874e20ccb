{% extends "base.html" %}

{% block title %}سجل المراجعة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-history me-2"></i>
                        سجل المراجعة
                    </h3>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-danger" onclick="clearAuditLog()">
                            <i class="fas fa-trash"></i> مسح السجل
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="exportAuditLog()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للإعدادات
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label for="user_filter" class="form-label">المستخدم</label>
                            <select class="form-select" id="user_filter" onchange="filterAuditLog()">
                                <option value="">جميع المستخدمين</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" {{ 'selected' if user.id|string == request.args.get('user_id') }}>
                                    {{ user.username }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="action_filter" class="form-label">نوع العملية</label>
                            <select class="form-select" id="action_filter" onchange="filterAuditLog()">
                                <option value="">جميع العمليات</option>
                                <option value="إضافة" {{ 'selected' if request.args.get('action') == 'إضافة' }}>إضافة</option>
                                <option value="تعديل" {{ 'selected' if request.args.get('action') == 'تعديل' }}>تعديل</option>
                                <option value="حذف" {{ 'selected' if request.args.get('action') == 'حذف' }}>حذف</option>
                                <option value="تسجيل دخول" {{ 'selected' if request.args.get('action') == 'تسجيل دخول' }}>تسجيل دخول</option>
                                <option value="تسجيل خروج" {{ 'selected' if request.args.get('action') == 'تسجيل خروج' }}>تسجيل خروج</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="from_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="from_date" 
                                   value="{{ request.args.get('from_date', '') }}" onchange="filterAuditLog()">
                        </div>
                        <div class="col-md-3">
                            <label for="to_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="to_date" 
                                   value="{{ request.args.get('to_date', '') }}" onchange="filterAuditLog()">
                        </div>
                    </div>

                    <!-- Audit Log Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستخدم</th>
                                    <th>العملية</th>
                                    <th>الجدول</th>
                                    <th>معرف السجل</th>
                                    <th>التفاصيل</th>
                                    <th>عنوان IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in audit_logs %}
                                <tr>
                                    <td>
                                        <small>{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ log.user.username if log.user else 'نظام' }}</span>
                                    </td>
                                    <td>
                                        {% if log.action == 'إضافة' %}
                                        <span class="badge bg-success">{{ log.action }}</span>
                                        {% elif log.action == 'تعديل' %}
                                        <span class="badge bg-warning">{{ log.action }}</span>
                                        {% elif log.action == 'حذف' %}
                                        <span class="badge bg-danger">{{ log.action }}</span>
                                        {% elif log.action == 'تسجيل دخول' %}
                                        <span class="badge bg-info">{{ log.action }}</span>
                                        {% elif log.action == 'تسجيل خروج' %}
                                        <span class="badge bg-dark">{{ log.action }}</span>
                                        {% else %}
                                        <span class="badge bg-primary">{{ log.action }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.table_name or '-' }}</td>
                                    <td>{{ log.record_id or '-' }}</td>
                                    <td>
                                        {% if log.old_values or log.new_values %}
                                        <button type="button" class="btn btn-outline-info btn-sm" 
                                                onclick="showAuditDetails({{ log.id }})">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ log.ip_address or '-' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if not audit_logs %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد سجلات</h5>
                        <p class="text-muted">لم يتم العثور على أي سجلات مراجعة</p>
                    </div>
                    {% endif %}

                    <!-- Pagination -->
                    {% if pagination %}
                    <nav aria-label="صفحات سجل المراجعة">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('settings.audit_log', page=pagination.prev_num, **request.args) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                            {% if page_num != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('settings.audit_log', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('settings.audit_log', page=pagination.next_num, **request.args) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audit Details Modal -->
<div class="modal fade" id="auditDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="auditDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function filterAuditLog() {
    const userId = document.getElementById('user_filter').value;
    const action = document.getElementById('action_filter').value;
    const fromDate = document.getElementById('from_date').value;
    const toDate = document.getElementById('to_date').value;
    
    const params = new URLSearchParams();
    if (userId) params.append('user_id', userId);
    if (action) params.append('action', action);
    if (fromDate) params.append('from_date', fromDate);
    if (toDate) params.append('to_date', toDate);
    
    window.location.href = `{{ url_for('settings.audit_log') }}?${params.toString()}`;
}

function showAuditDetails(logId) {
    fetch(`/settings/audit-details/${logId}`)
        .then(response => response.json())
        .then(data => {
            let content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات العملية</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الوقت:</strong></td><td>${data.timestamp}</td></tr>
                            <tr><td><strong>المستخدم:</strong></td><td>${data.user}</td></tr>
                            <tr><td><strong>العملية:</strong></td><td>${data.action}</td></tr>
                            <tr><td><strong>الجدول:</strong></td><td>${data.table_name || '-'}</td></tr>
                            <tr><td><strong>معرف السجل:</strong></td><td>${data.record_id || '-'}</td></tr>
                            <tr><td><strong>عنوان IP:</strong></td><td>${data.ip_address || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>تفاصيل التغييرات</h6>
            `;
            
            if (data.old_values) {
                content += `
                    <h6 class="text-danger">القيم القديمة:</h6>
                    <pre class="bg-light p-2 rounded">${JSON.stringify(data.old_values, null, 2)}</pre>
                `;
            }
            
            if (data.new_values) {
                content += `
                    <h6 class="text-success">القيم الجديدة:</h6>
                    <pre class="bg-light p-2 rounded">${JSON.stringify(data.new_values, null, 2)}</pre>
                `;
            }
            
            content += `
                    </div>
                </div>
            `;
            
            document.getElementById('auditDetailsContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('auditDetailsModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل تفاصيل العملية');
        });
}

function clearAuditLog() {
    if (confirm('هل أنت متأكد من مسح جميع سجلات المراجعة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('/settings/clear-audit-log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في مسح السجل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في مسح السجل');
        });
    }
}

function exportAuditLog() {
    const userId = document.getElementById('user_filter').value;
    const action = document.getElementById('action_filter').value;
    const fromDate = document.getElementById('from_date').value;
    const toDate = document.getElementById('to_date').value;
    
    const params = new URLSearchParams();
    if (userId) params.append('user_id', userId);
    if (action) params.append('action', action);
    if (fromDate) params.append('from_date', fromDate);
    if (toDate) params.append('to_date', toDate);
    params.append('export', 'excel');
    
    window.location.href = `{{ url_for('settings.audit_log') }}?${params.toString()}`;
}
</script>
{% endblock %}
