@echo off
title AutoParts Manager V5.1

echo ============================================================
echo AutoParts Manager V5.1 - Quick Start
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not installed
    echo Please install Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python: OK
python --version

REM Install basic requirements if needed
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Flask...
    pip install Flask Flask-SQLAlchemy Flask-Login
)

REM Create database if needed
if not exist "autoparts.db" (
    echo Creating database...
    python -c "from app import app, db; app.app_context().push(); db.create_all(); print('Database: OK')"
)

REM Create directories
if not exist "exports" mkdir exports
if not exist "backups" mkdir backups
if not exist "logs" mkdir logs

echo.
echo Starting AutoParts Manager...
echo URL: http://127.0.0.1:8888
echo Login: admin / admin123
echo.

python app.py

pause
