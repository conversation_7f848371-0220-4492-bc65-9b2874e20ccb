# -*- coding: utf-8 -*-
"""
Build script for AutoParts Manager
Creates executable file using PyInstaller with complete installer
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import zipfile

def create_installer_package():
    """Create complete installer package"""
    print("📦 إنشاء حزمة التثبيت الشاملة...")
    
    current_dir = Path(__file__).parent
    package_dir = current_dir / "AutoParts_Manager_Package"
    
    # Create package directory
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Files to include in package
    files_to_copy = [
        'app.py', 'auth.py', 'models.py', 'utils.py', 'installer.py',
        'requirements.txt'
    ]
    
    dirs_to_copy = ['routes', 'templates', 'static']
    
    # Copy files
    for file in files_to_copy:
        src = current_dir / file
        if src.exists():
            shutil.copy2(src, package_dir / file)
            print(f"✅ نسخ ملف: {file}")
    
    # Copy directories
    for dir_name in dirs_to_copy:
        src = current_dir / dir_name
        if src.exists():
            shutil.copytree(src, package_dir / dir_name)
            print(f"✅ نسخ مجلد: {dir_name}")
    
    # Create installation guide
    guide_content = """# دليل تثبيت نظام إدارة قطع الغيار
## AutoParts Manager Installation Guide

### متطلبات النظام:
- Windows 10 أو أحدث
- Python 3.8 أو أحدث (سيتم تثبيته تلقائياً إذا لم يكن موجوداً)
- 2 جيجابايت مساحة فارغة على القرص الصلب
- اتصال بالإنترنت (للتثبيت الأولي فقط)

### خطوات التثبيت:
1. انقر نقراً مزدوجاً على ملف `installer.py`
2. اتبع التعليمات على الشاشة
3. بعد انتهاء التثبيت، انقر على `تشغيل_النظام.bat`

### الوصول للنظام:
- المحلي: http://127.0.0.1:8888
- الشبكة: http://[IP_ADDRESS]:8888
- المستخدم: admin | كلمة المرور: admin123

### الميزات:
✅ نقطة البيع (POS)
✅ إدارة المنتجات والمخزون
✅ إدارة العملاء والموردين
✅ النظام المحاسبي الكامل
✅ التقارير المالية
✅ استيراد/تصدير Excel
✅ النسخ الاحتياطي والاستعادة
✅ إدارة المستخدمين والصلاحيات
✅ دعم الشبكة المحلية (Multi-IP)
✅ واجهة عربية كاملة مع دعم RTL
"""
    
    with open(package_dir / "دليل_التثبيت.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    # Create quick start batch file
    quick_start = """@echo off
chcp 65001 > nul
title تثبيت نظام إدارة قطع الغيار
color 0B

echo ===============================================================
echo 🚀 مرحباً بك في نظام إدارة قطع الغيار
echo    AutoParts Manager Installation
echo ===============================================================
echo.
echo 📋 سيتم الآن تثبيت النظام تلقائياً...
echo.

python installer.py

pause
"""
    
    with open(package_dir / "تثبيت_سريع.bat", 'w', encoding='utf-8') as f:
        f.write(quick_start)
    
    print(f"✅ تم إنشاء حزمة التثبيت في: {package_dir}")
    
    # Create ZIP file
    zip_file = current_dir / "AutoParts_Manager_Complete.zip"
    if zip_file.exists():
        zip_file.unlink()
    
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(package_dir)
                zipf.write(file_path, arc_name)
    
    print(f"📦 تم إنشاء ملف ZIP: {zip_file}")
    return True

def build_exe():
    """Build executable using PyInstaller"""
    print("🔨 بناء ملف تنفيذي...")
    
    current_dir = Path(__file__).parent
    
    # Clean previous builds
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("✅ تم حذف مجلد dist السابق")

    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✅ تم حذف مجلد build السابق")
    
    # PyInstaller command for complete app
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',  # Keep console for debugging
        '--name=AutoPartsManager',
        '--icon=static/favicon.ico',
        '--add-data=templates;templates',
        '--add-data=static;static',
        '--hidden-import=flask',
        '--hidden-import=flask_sqlalchemy',
        '--hidden-import=flask_login',
        '--hidden-import=werkzeug',
        '--hidden-import=jinja2',
        '--hidden-import=sqlalchemy',
        '--hidden-import=pandas',
        '--hidden-import=openpyxl',
        '--hidden-import=reportlab',
        '--hidden-import=barcode',
        '--hidden-import=PIL',
        '--hidden-import=socket',
        '--hidden-import=webbrowser',
        'app.py'
    ]
    
    try:
        # Run PyInstaller
        print("⚙️ تشغيل PyInstaller...")
        subprocess.run(cmd, check=True)
        
        print("✅ تم بناء الملف التنفيذي بنجاح!")
        
        dist_dir = current_dir / 'dist'
        if dist_dir.exists():
            exe_file = dist_dir / 'AutoPartsManager.exe'
            if exe_file.exists():
                print(f"📁 الملف التنفيذي: {exe_file}")
                
                # Create portable package
                portable_dir = current_dir / "AutoParts_Portable"
                if portable_dir.exists():
                    shutil.rmtree(portable_dir)
                portable_dir.mkdir()
                
                # Copy exe
                shutil.copy2(exe_file, portable_dir)
                
                # Create run script
                run_script = """@echo off
chcp 65001 > nul
title نظام إدارة قطع الغيار - AutoParts Manager
color 0A

echo ===============================================================
echo 🚀 نظام إدارة قطع الغيار - AutoParts Manager
echo    Portable Version
echo ===============================================================
echo.
echo 🌐 سيتم تشغيل النظام على جميع عناوين IP المتاحة
echo 💡 يمكن الوصول من أي جهاز في الشبكة المحلية
echo 🔐 المستخدم الافتراضي: admin | كلمة المرور: admin123
echo.
echo ⏳ جاري التشغيل...
echo.

AutoPartsManager.exe

pause
"""
                
                with open(portable_dir / "تشغيل_النظام.bat", 'w', encoding='utf-8') as f:
                    f.write(run_script)
                
                print(f"📦 تم إنشاء النسخة المحمولة: {portable_dir}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل البناء: {e}")
        return False
    except FileNotFoundError:
        print("❌ PyInstaller غير موجود. قم بتثبيته: pip install pyinstaller")
        return False

def main():
    print("=" * 60)
    print("🏗️ أداة بناء نظام إدارة قطع الغيار")
    print("   AutoParts Manager Build Tool")
    print("=" * 60)
    
    choice = input("""
اختر نوع البناء:
1. حزمة التثبيت الكاملة (مستحسن)
2. ملف تنفيذي (EXE)
3. كلاهما

أدخل اختيارك (1/2/3): """).strip()
    
    if choice in ['1', '3']:
        print("\n📦 إنشاء حزمة التثبيت...")
        create_installer_package()
    
    if choice in ['2', '3']:
        print("\n🔨 بناء الملف التنفيذي...")
        build_exe()
    
    print("\n✅ انتهت عملية البناء!")
    input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
