# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, session
from models import db, User, Setting, AuditLog
from utils import login_required, permission_required, log_audit, backup_database, restore_database
from datetime import datetime
import os
from sqlalchemy import text

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
@login_required
@permission_required('all')
def index():
    # Get basic statistics for settings page
    from models import Product, Customer, Supplier, Sale

    stats = {
        'total_products': Product.query.count(),
        'total_customers': Customer.query.count(),
        'total_suppliers': Supplier.query.count(),
        'total_sales': Sale.query.count()
    }

    return render_template('settings/index.html', stats=stats)

@settings_bp.route('/users')
@login_required
@permission_required('all')
def users():
    users = User.query.all()
    return render_template('settings/users.html', users=users)

@settings_bp.route('/add-user', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def add_user():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        full_name = request.form.get('full_name')
        role = request.form.get('role')
        password = request.form.get('password')

        if not all([username, email, full_name, role, password]):
            flash('يرجى ملء جميع الحقول', 'error')
            return render_template('settings/add_user.html')

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('settings/add_user.html')

        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود مسبقاً', 'error')
            return render_template('settings/add_user.html')

        user = User(
            username=username,
            email=email,
            full_name=full_name,
            role=role
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        # Log audit
        log_audit(session['user_id'], 'إضافة مستخدم', 'users', user.id,
                 new_values={'username': username, 'role': role})

        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('settings.users'))

    return render_template('settings/add_user.html')

@settings_bp.route('/edit-user/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def edit_user(id):
    user = User.query.get_or_404(id)

    if request.method == 'POST':
        old_values = {
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'role': user.role,
            'is_active': user.is_active
        }

        user.username = request.form.get('username')
        user.email = request.form.get('email')
        user.full_name = request.form.get('full_name')
        user.role = request.form.get('role')
        user.is_active = request.form.get('is_active') == 'on'

        # Update password if provided
        new_password = request.form.get('password')
        if new_password:
            user.set_password(new_password)

        db.session.commit()

        new_values = {
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'role': user.role,
            'is_active': user.is_active
        }

        # Log audit
        log_audit(session['user_id'], 'تعديل مستخدم', 'users', user.id,
                 old_values=old_values, new_values=new_values)

        flash('تم تحديث بيانات المستخدم بنجاح', 'success')
        return redirect(url_for('settings.users'))

    return render_template('settings/edit_user.html', user=user)

@settings_bp.route('/system-settings', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def system_settings():
    if request.method == 'POST':
        settings_data = {
            'company_name': request.form.get('company_name'),
            'company_address': request.form.get('company_address'),
            'company_phone': request.form.get('company_phone'),
            'company_email': request.form.get('company_email'),
            'tax_rate': request.form.get('tax_rate'),
            'currency': request.form.get('currency'),
            'invoice_prefix': request.form.get('invoice_prefix'),
            'receipt_prefix': request.form.get('receipt_prefix')
        }

        for key, value in settings_data.items():
            setting = Setting.query.filter_by(key=key).first()
            if setting:
                setting.value = value
                setting.updated_at = datetime.utcnow()
            else:
                setting = Setting(key=key, value=value)
                db.session.add(setting)

        db.session.commit()

        # Log audit
        log_audit(session['user_id'], 'تحديث إعدادات النظام', 'settings', None,
                 new_values=settings_data)

        flash('تم حفظ الإعدادات بنجاح', 'success')

    # Get current settings
    settings = {}
    for setting in Setting.query.all():
        settings[setting.key] = setting.value

    return render_template('settings/system_settings.html', settings=settings)

@settings_bp.route('/backup')
@login_required
@permission_required('all')
def backup():
    backup_path = backup_database()

    if backup_path:
        # Log audit
        log_audit(session['user_id'], 'إنشاء نسخة احتياطية', 'system', None,
                 new_values={'backup_path': backup_path})

        flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
        return send_file(backup_path, as_attachment=True)
    else:
        flash('حدث خطأ في إنشاء النسخة الاحتياطية', 'error')
        return redirect(url_for('settings.index'))

@settings_bp.route('/restore', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def restore():
    if request.method == 'POST':
        if 'backup_file' not in request.files:
            flash('يرجى اختيار ملف النسخة الاحتياطية', 'error')
            return render_template('settings/restore.html')

        file = request.files['backup_file']
        if file.filename == '':
            flash('يرجى اختيار ملف النسخة الاحتياطية', 'error')
            return render_template('settings/restore.html')

        if file and file.filename.endswith('.db'):
            # Save uploaded file temporarily
            temp_path = os.path.join('temp_restore.db')
            file.save(temp_path)

            # Restore database
            if restore_database(temp_path):
                # Log audit
                log_audit(session['user_id'], 'استعادة نسخة احتياطية', 'system', None,
                         new_values={'restored_file': file.filename})

                # Clean up temp file
                os.remove(temp_path)

                flash('تم استعادة النسخة الاحتياطية بنجاح', 'success')
                return redirect(url_for('settings.index'))
            else:
                # Clean up temp file
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                flash('حدث خطأ في استعادة النسخة الاحتياطية', 'error')
        else:
            flash('نوع الملف غير صحيح. يجب أن يكون ملف .db', 'error')

    return render_template('settings/restore.html')

@settings_bp.route('/audit-log')
@login_required
@permission_required('all')
def audit_log():
    page = request.args.get('page', 1, type=int)
    user_id = request.args.get('user_id', type=int)
    action = request.args.get('action')
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    query = AuditLog.query

    if user_id:
        query = query.filter_by(user_id=user_id)
    if action:
        query = query.filter(AuditLog.action.contains(action))
    if from_date:
        query = query.filter(AuditLog.timestamp >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        query = query.filter(AuditLog.timestamp <= datetime.strptime(to_date, '%Y-%m-%d'))

    logs = query.order_by(AuditLog.timestamp.desc()).paginate(
        page=page, per_page=50, error_out=False
    )

    users = User.query.all()

    return render_template('settings/audit_log.html',
                         logs=logs,
                         users=users,
                         user_id=user_id,
                         action=action,
                         from_date=from_date,
                         to_date=to_date)

@settings_bp.route('/database-info')
@login_required
@permission_required('all')
def database_info():
    # Get database statistics
    from models import Product, Customer, Supplier, Sale, Purchase

    stats = {
        'products': Product.query.count(),
        'customers': Customer.query.count(),
        'suppliers': Supplier.query.count(),
        'sales': Sale.query.count(),
        'purchases': Purchase.query.count(),
        'users': User.query.count(),
        'audit_logs': AuditLog.query.count()
    }

    # Get database file size
    db_paths = ['autoparts.db', 'instance/autoparts.db', os.path.join('instance', 'autoparts.db')]
    db_size_mb = 0
    db_path = None

    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            db_size = os.path.getsize(path)
            db_size_mb = db_size / (1024 * 1024)
            break

    # Get backup files
    backup_files = []
    backup_dir = 'backups'
    if os.path.exists(backup_dir):
        for filename in os.listdir(backup_dir):
            if filename.endswith('.db'):
                filepath = os.path.join(backup_dir, filename)
                file_size = os.path.getsize(filepath)
                file_date = datetime.fromtimestamp(os.path.getmtime(filepath))
                backup_files.append({
                    'filename': filename,
                    'size': file_size / (1024 * 1024),
                    'date': file_date
                })

    backup_files.sort(key=lambda x: x['date'], reverse=True)

    # Create db_info object for template
    db_info = {
        'size': f'{db_size_mb:.2f} MB',
        'tables_count': len(stats),
        'total_records': sum(stats.values()),
        'last_backup': backup_files[0]['date'].strftime('%Y-%m-%d %H:%M') if backup_files else None,
        'type': 'SQLite',
        'version': '3.x',
        'path': 'autoparts.db',
        'created_date': 'غير محدد',
        'modified_date': 'غير محدد',
        'encoding': 'UTF-8',
        'tables': [
            {
                'name': table_name,
                'records': count,
                'size': f'{count * 0.001:.2f} KB'  # تقدير تقريبي
            }
            for table_name, count in stats.items()
        ],
        'avg_query_time': '< 1',
        'queries_today': 'غير محدد',
        'index_status': 'جيد',
        'fragmentation': '0',
        'recent_activity': []
    }

    return render_template('settings/database_info.html', db_info=db_info)

# Database maintenance routes
@settings_bp.route('/analyze-database', methods=['POST'])
@login_required
@permission_required('admin')
def analyze_database():
    """Analyze database performance"""
    try:
        # Run ANALYZE command
        db.session.execute(text('ANALYZE'))
        db.session.commit()

        log_audit(session['user_id'], 'تحليل قاعدة البيانات', 'database', None)
        flash('تم تحليل قاعدة البيانات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في تحليل قاعدة البيانات: {str(e)}', 'error')

    return redirect(url_for('settings.database_info'))

@settings_bp.route('/rebuild-indexes', methods=['POST'])
@login_required
@permission_required('admin')
def rebuild_indexes():
    """Rebuild database indexes"""
    try:
        # Rebuild indexes
        db.session.execute(text('REINDEX'))
        db.session.commit()

        log_audit(session['user_id'], 'إعادة بناء الفهارس', 'database', None)
        flash('تم إعادة بناء الفهارس بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في إعادة بناء الفهارس: {str(e)}', 'error')

    return redirect(url_for('settings.database_info'))

@settings_bp.route('/vacuum-database', methods=['POST'])
@login_required
@permission_required('admin')
def vacuum_database():
    """Clean up database (VACUUM)"""
    try:
        # Run VACUUM command
        db.session.execute(text('VACUUM'))
        db.session.commit()

        log_audit(session['user_id'], 'تنظيف قاعدة البيانات', 'database', None)
        flash('تم تنظيف قاعدة البيانات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في تنظيف قاعدة البيانات: {str(e)}', 'error')

    return redirect(url_for('settings.database_info'))

@settings_bp.route('/check-integrity', methods=['POST'])
@login_required
@permission_required('admin')
def check_integrity():
    """Check database integrity"""
    try:
        # Run integrity check
        result = db.session.execute(text('PRAGMA integrity_check')).fetchone()

        if result and result[0] == 'ok':
            log_audit(session['user_id'], 'فحص سلامة البيانات', 'database', None)
            flash('فحص سلامة البيانات: النتيجة سليمة', 'success')
        else:
            flash(f'فحص سلامة البيانات: {result[0] if result else "خطأ غير معروف"}', 'warning')
    except Exception as e:
        flash(f'حدث خطأ في فحص سلامة البيانات: {str(e)}', 'error')

    return redirect(url_for('settings.database_info'))

@settings_bp.route('/compact-database', methods=['POST'])
@login_required
@permission_required('admin')
def compact_database():
    """Compact database"""
    try:
        # Run VACUUM and ANALYZE
        db.session.execute(text('VACUUM'))
        db.session.execute(text('ANALYZE'))
        db.session.commit()

        log_audit(session['user_id'], 'ضغط قاعدة البيانات', 'database', None)
        flash('تم ضغط قاعدة البيانات بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في ضغط قاعدة البيانات: {str(e)}', 'error')

    return redirect(url_for('settings.database_info'))

@settings_bp.route('/reset-database', methods=['POST'])
@login_required
@permission_required('admin')
def reset_database():
    """Reset database (WARNING: This will delete all data!)"""
    try:
        # This is a dangerous operation - require confirmation
        confirmation = request.form.get('confirmation')
        if confirmation != 'RESET':
            flash('يجب كتابة "RESET" للتأكيد', 'error')
            return redirect(url_for('settings.database_info'))

        # Create backup before reset
        backup_path = backup_database()
        if not backup_path:
            flash('فشل في إنشاء نسخة احتياطية قبل الإعادة تعيين', 'error')
            return redirect(url_for('settings.database_info'))

        # Drop all tables and recreate
        db.drop_all()
        db.create_all()

        # Create default admin user
        from models import User
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        db.session.commit()

        log_audit(1, 'إعادة تعيين قاعدة البيانات', 'database', None)
        flash(f'تم إعادة تعيين قاعدة البيانات بنجاح. تم إنشاء نسخة احتياطية: {backup_path}', 'success')
    except Exception as e:
        flash(f'حدث خطأ في إعادة تعيين قاعدة البيانات: {str(e)}', 'error')

    return redirect(url_for('settings.database_info'))

# User management routes
@settings_bp.route('/get-user/<int:user_id>')
@login_required
@permission_required('admin')
def get_user(user_id):
    """Get user data for editing"""
    try:
        user = User.query.get_or_404(user_id)
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'is_active': user.is_active
        }
    except Exception as e:
        return {'error': f'حدث خطأ في تحميل بيانات المستخدم: {str(e)}'}, 500

@settings_bp.route('/toggle-user-status/<int:user_id>', methods=['POST'])
@login_required
@permission_required('admin')
def toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        user = User.query.get_or_404(user_id)

        # Don't allow deactivating the current user
        if user.id == session['user_id']:
            flash('لا يمكن إلغاء تفعيل المستخدم الحالي', 'error')
            return redirect(url_for('settings.users'))

        user.is_active = not user.is_active
        db.session.commit()

        status = 'تفعيل' if user.is_active else 'إلغاء تفعيل'
        log_audit(session['user_id'], f'{status} المستخدم', 'users', user.id)
        flash(f'تم {status} المستخدم بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في تغيير حالة المستخدم: {str(e)}', 'error')

    return redirect(url_for('settings.users'))

@settings_bp.route('/delete-user/<int:user_id>', methods=['POST'])
@login_required
@permission_required('admin')
def delete_user(user_id):
    """Delete user"""
    try:
        user = User.query.get_or_404(user_id)

        # Don't allow deleting the current user
        if user.id == session['user_id']:
            flash('لا يمكن حذف المستخدم الحالي', 'error')
            return redirect(url_for('settings.users'))

        # Don't allow deleting the last admin
        if user.role == 'admin':
            admin_count = User.query.filter_by(role='admin', is_active=True).count()
            if admin_count <= 1:
                flash('لا يمكن حذف آخر مدير في النظام', 'error')
                return redirect(url_for('settings.users'))

        username = user.username
        db.session.delete(user)
        db.session.commit()

        log_audit(session['user_id'], 'حذف المستخدم', 'users', user_id,
                 old_values={'username': username})
        flash('تم حذف المستخدم بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في حذف المستخدم: {str(e)}', 'error')

    return redirect(url_for('settings.users'))