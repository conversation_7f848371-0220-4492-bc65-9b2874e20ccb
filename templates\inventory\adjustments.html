{% extends "base.html" %}

{% block title %}تعديلات المخزون{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-balance-scale me-2"></i>تعديلات المخزون</h2>
                <div class="btn-group">
                    <a href="{{ url_for('inventory.add_adjustment') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>تعديل جديد
                    </a>
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Adjustments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل تعديلات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if adjustments.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم التعديل</th>
                                    <th>المنتج</th>
                                    <th>الكمية السابقة</th>
                                    <th>الكمية الجديدة</th>
                                    <th>الفرق</th>
                                    <th>السبب</th>
                                    <th>التاريخ</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for adjustment in adjustments.items %}
                                <tr>
                                    <td>
                                        <strong>{{ adjustment.adjustment_number }}</strong>
                                    </td>
                                    <td>
                                        <strong>{{ adjustment.product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ adjustment.product.barcode }}</small>
                                    </td>
                                    <td>
                                        {{ "{:,.2f}".format(adjustment.old_quantity) }}
                                        <small class="text-muted">{{ adjustment.product.unit }}</small>
                                    </td>
                                    <td>
                                        {{ "{:,.2f}".format(adjustment.new_quantity) }}
                                        <small class="text-muted">{{ adjustment.product.unit }}</small>
                                    </td>
                                    <td>
                                        {% if adjustment.adjustment_quantity > 0 %}
                                            <span class="text-success">
                                                <i class="fas fa-arrow-up me-1"></i>
                                                +{{ "{:,.2f}".format(adjustment.adjustment_quantity) }}
                                            </span>
                                        {% elif adjustment.adjustment_quantity < 0 %}
                                            <span class="text-danger">
                                                <i class="fas fa-arrow-down me-1"></i>
                                                {{ "{:,.2f}".format(adjustment.adjustment_quantity) }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">0.00</span>
                                        {% endif %}
                                        <small class="text-muted">{{ adjustment.product.unit }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ adjustment.reason }}</span>
                                        {% if adjustment.notes %}
                                        <br>
                                        <small class="text-muted">{{ adjustment.notes[:50] }}{% if adjustment.notes|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ adjustment.adjustment_date.strftime('%Y-%m-%d') }}
                                        <br>
                                        <small class="text-muted">{{ adjustment.adjustment_date.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>{{ adjustment.user.full_name }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="showAdjustmentDetails({{ adjustment.id }})"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if adjustments.pages > 1 %}
                    <nav aria-label="صفحات تعديلات المخزون">
                        <ul class="pagination justify-content-center">
                            {% if adjustments.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.adjustments', page=adjustments.prev_num) }}">السابق</a>
                            </li>
                            {% endif %}

                            {% for page_num in adjustments.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != adjustments.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory.adjustments', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if adjustments.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.adjustments', page=adjustments.next_num) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تعديلات مخزون</h5>
                        <p class="text-muted">لم يتم إجراء أي تعديلات على المخزون بعد</p>
                        <a href="{{ url_for('inventory.add_adjustment') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة تعديل جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Adjustment Details Modal -->
<div class="modal fade" id="adjustmentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل التعديل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="adjustmentDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function showAdjustmentDetails(adjustmentId) {
    // Show loading
    document.getElementById('adjustmentDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل التفاصيل...</p>
        </div>
    `;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('adjustmentDetailsModal'));
    modal.show();
    
    // Load details (you can implement this with AJAX if needed)
    // For now, show a placeholder
    setTimeout(() => {
        document.getElementById('adjustmentDetailsContent').innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                تفاصيل التعديل رقم: ${adjustmentId}
            </div>
            <p>يمكن إضافة المزيد من التفاصيل هنا حسب الحاجة.</p>
        `;
    }, 1000);
}
</script>
{% endblock %}
