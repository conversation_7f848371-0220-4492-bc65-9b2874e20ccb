{% extends "base.html" %}

{% block title %}دفتر الأستاذ العام - النظام المحاسبي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-file-alt me-2"></i>
        دفتر الأستاذ العام
    </h1>
    <div>
        <a href="{{ url_for('accounting.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمحاسبة
        </a>
        <a href="{{ url_for('accounting.general_ledger', export='excel') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>
            تصدير Excel
        </a>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            فلترة الحسابات
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">الحساب</label>
                <select class="form-select" name="account_id">
                    <option value="">جميع الحسابات</option>
                    {% for account in accounts %}
                    <option value="{{ account.id }}" 
                            {% if account.id == account_id %}selected{% endif %}>
                        {{ account.code }} - {{ account.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="from_date" 
                       value="{{ request.args.get('from_date', '') }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="to_date" 
                       value="{{ request.args.get('to_date', '') }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        عرض
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Ledger Entries -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            {% if selected_account %}
                حساب: {{ selected_account.code }} - {{ selected_account.name }}
            {% else %}
                جميع الحسابات
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        {% if ledger_entries %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>رقم القيد</th>
                        <th>الوصف</th>
                        <th>الحساب</th>
                        <th>المدين</th>
                        <th>الدائن</th>
                        <th>الرصيد</th>
                    </tr>
                </thead>
                <tbody>
                    {% set running_balance = 0 %}
                    {% for entry in ledger_entries %}
                    {% set running_balance = running_balance + (entry.debit_amount or 0) - (entry.credit_amount or 0) %}
                    <tr>
                        <td>{{ entry.date.strftime('%Y-%m-%d') if entry.date else 'غير محدد' }}</td>
                        <td>
                            <a href="{{ url_for('accounting.view_journal_entry', entry_id=entry.journal_entry_id) }}" 
                               class="text-decoration-none">
                                {{ entry.journal_entry_id }}
                            </a>
                        </td>
                        <td>{{ entry.description or 'غير محدد' }}</td>
                        <td>
                            <small>{{ entry.account_code }} - {{ entry.account_name }}</small>
                        </td>
                        <td>
                            {% if entry.debit_amount %}
                                <span class="text-success">{{ entry.debit_amount|currency }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if entry.credit_amount %}
                                <span class="text-danger">{{ entry.credit_amount|currency }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <strong class="{% if running_balance > 0 %}text-success{% elif running_balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                                {{ running_balance|currency }}
                            </strong>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="4">الإجمالي</th>
                        <th class="text-success">{{ total_debits|currency }}</th>
                        <th class="text-danger">{{ total_credits|currency }}</th>
                        <th class="text-info">{{ final_balance|currency }}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قيود</h5>
            <p class="text-muted">لم يتم العثور على قيود للحساب المحدد في الفترة المطلوبة</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Account Summary -->
{% if selected_account %}
<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    ملخص الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الحساب:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الكود:</strong> {{ selected_account.code }}</li>
                            <li><strong>الاسم:</strong> {{ selected_account.name }}</li>
                            <li><strong>النوع:</strong> {{ selected_account.type }}</li>
                            <li><strong>الوصف:</strong> {{ selected_account.description or 'غير محدد' }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الأرصدة:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الرصيد الافتتاحي:</strong> {{ opening_balance|currency }}</li>
                            <li><strong>إجمالي المدين:</strong> <span class="text-success">{{ total_debits|currency }}</span></li>
                            <li><strong>إجمالي الدائن:</strong> <span class="text-danger">{{ total_credits|currency }}</span></li>
                            <li><strong>الرصيد الختامي:</strong> 
                                <span class="{% if final_balance > 0 %}text-success{% elif final_balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                                    {{ final_balance|currency }}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الحركات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="accountChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- All Accounts Summary -->
{% if not selected_account %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    ملخص جميع الحسابات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>المدين</th>
                                <th>الدائن</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in account_balances %}
                            <tr>
                                <td>{{ account.code }}</td>
                                <td>
                                    <a href="{{ url_for('accounting.general_ledger', account_id=account.id) }}" 
                                       class="text-decoration-none">
                                        {{ account.name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ account.type }}</span>
                                </td>
                                <td class="text-success">{{ account.total_debits|currency }}</td>
                                <td class="text-danger">{{ account.total_credits|currency }}</td>
                                <td>
                                    <strong class="{% if account.balance > 0 %}text-success{% elif account.balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {{ account.balance|currency }}
                                    </strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if selected_account %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Account Chart
const accountCtx = document.getElementById('accountChart').getContext('2d');
new Chart(accountCtx, {
    type: 'doughnut',
    data: {
        labels: ['المدين', 'الدائن'],
        datasets: [{
            data: [{{ total_debits }}, {{ total_credits }}],
            backgroundColor: ['#28a745', '#dc3545'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
