<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة قطع الغيار</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 60px 40px;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        .login-image i {
            font-size: 5rem;
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            color: white;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .brand-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .brand-subtitle {
            color: #666;
            margin-bottom: 40px;
        }
        
        .default-users {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            font-size: 0.9rem;
        }
        
        .default-users h6 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .user-credential {
            background: white;
            border-radius: 5px;
            padding: 8px 12px;
            margin-bottom: 8px;
            border-right: 3px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Image -->
                <div class="col-lg-5 login-image">
                    <div>
                        <i class="fas fa-car"></i>
                        <h2 class="fw-bold">نظام إدارة قطع الغيار</h2>
                        <p class="lead">نظام متكامل لإدارة المخزون والمبيعات والحسابات</p>
                        <div class="mt-4">
                            <i class="fas fa-shield-alt me-2"></i>
                            <span>آمن وموثوق</span>
                        </div>
                        <div class="mt-2">
                            <i class="fas fa-clock me-2"></i>
                            <span>يعمل 24/7</span>
                        </div>
                        <div class="mt-2">
                            <i class="fas fa-chart-line me-2"></i>
                            <span>تقارير مفصلة</span>
                        </div>
                    </div>
                </div>
                
                <!-- Login Form -->
                <div class="col-lg-7 login-form">
                    <h1 class="brand-title">مرحباً بك</h1>
                    <p class="brand-subtitle">يرجى تسجيل الدخول للمتابعة</p>
                    
                    <!-- Flash messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST">
                        <div class="mb-4">
                            <label for="username" class="form-label fw-bold">اسم المستخدم</label>
                            <div class="input-group">
                                <input type="text" class="form-control with-icon" id="username" name="username" required>
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label fw-bold">كلمة المرور</label>
                            <div class="input-group">
                                <input type="password" class="form-control with-icon" id="password" name="password" required>
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>
                    
                    <!-- Default Users Info -->
                    <div class="default-users">
                        <h6><i class="fas fa-info-circle me-2"></i>المستخدمين الافتراضيين</h6>
                        
                        <div class="user-credential">
                            <strong>المدير:</strong> admin / admin123
                            <small class="text-muted d-block">صلاحيات كاملة على النظام</small>
                        </div>
                        
                        <div class="user-credential">
                            <strong>المحاسب:</strong> accountant / acc123
                            <small class="text-muted d-block">الحسابات والتقارير المالية</small>
                        </div>
                        
                        <div class="user-credential">
                            <strong>الكاشير:</strong> cashier / cash123
                            <small class="text-muted d-block">نقطة البيع فقط</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    </script>
</body>
</html>
