{% extends "base.html" %}

{% block title %}باركود المنتج - {{ product.name }}{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-barcode me-2"></i>باركود المنتج</h2>
                <div>
                    <a href="{{ url_for('products.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                    </a>
                    <button onclick="printBarcode()" class="btn btn-primary">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%"><strong>اسم المنتج:</strong></td>
                                    <td>{{ product.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الباركود:</strong></td>
                                    <td><code>{{ product.barcode }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td>{{ product.category.name if product.category else 'غير محدد' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="30%"><strong>سعر البيع:</strong></td>
                                    <td>{{ "%.2f"|format(product.selling_price) }} ريال</td>
                                </tr>
                                <tr>
                                    <td><strong>الكمية:</strong></td>
                                    <td>{{ product.quantity }} {{ product.unit }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الموقع:</strong></td>
                                    <td>{{ product.location or 'غير محدد' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Display -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-barcode me-2"></i>
                        الباركود
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div id="barcode-container" class="mb-4">
                        <!-- Barcode will be generated here -->
                        <div id="barcode"></div>
                    </div>
                    
                    <!-- Barcode Options -->
                    <div class="row mb-4">
                        <div class="col-md-6 mx-auto">
                            <div class="card border-secondary">
                                <div class="card-header">
                                    <h6 class="mb-0">خيارات الباركود</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="barcodeWidth" class="form-label">العرض:</label>
                                            <input type="range" class="form-range" id="barcodeWidth" 
                                                   min="1" max="4" step="0.5" value="2" onchange="updateBarcode()">
                                            <small class="text-muted">2</small>
                                        </div>
                                        <div class="col-6">
                                            <label for="barcodeHeight" class="form-label">الارتفاع:</label>
                                            <input type="range" class="form-range" id="barcodeHeight" 
                                                   min="50" max="200" step="10" value="100" onchange="updateBarcode()">
                                            <small class="text-muted">100</small>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="showText" 
                                                       checked onchange="updateBarcode()">
                                                <label class="form-check-label" for="showText">
                                                    إظهار النص تحت الباركود
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Print Template -->
                    <div class="row">
                        <div class="col-md-8 mx-auto">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">معاينة الطباعة</h6>
                                </div>
                                <div class="card-body" id="print-template">
                                    <div class="text-center p-3" style="border: 2px dashed #ccc;">
                                        <h6 class="mb-2">{{ product.name }}</h6>
                                        <div id="print-barcode" class="mb-2"></div>
                                        <small class="text-muted">{{ product.barcode }}</small><br>
                                        <small class="text-success"><strong>{{ "%.2f"|format(product.selling_price) }} ريال</strong></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    body * {
        visibility: hidden;
    }
    
    #print-template, #print-template * {
        visibility: visible;
    }
    
    #print-template {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-header {
        display: none !important;
    }
}

.barcode-container {
    display: inline-block;
    padding: 10px;
    border: 1px solid #ddd;
    background: white;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
<script>
let barcodeValue = "{{ product.barcode }}";

function generateBarcode(elementId, options = {}) {
    const element = document.getElementById(elementId);
    if (element && barcodeValue) {
        try {
            JsBarcode(element, barcodeValue, {
                format: "CODE128",
                width: options.width || 2,
                height: options.height || 100,
                displayValue: options.displayValue !== false,
                fontSize: 14,
                textAlign: "center",
                textPosition: "bottom",
                textMargin: 2,
                fontOptions: "",
                font: "monospace",
                fontColor: "#000000",
                lineColor: "#000000",
                background: "#ffffff",
                margin: 10
            });
        } catch (error) {
            element.innerHTML = '<div class="alert alert-danger">خطأ في إنشاء الباركود: ' + error.message + '</div>';
        }
    }
}

function updateBarcode() {
    const width = parseFloat(document.getElementById('barcodeWidth').value);
    const height = parseInt(document.getElementById('barcodeHeight').value);
    const showText = document.getElementById('showText').checked;
    
    // Update range labels
    document.querySelector('label[for="barcodeWidth"] + .text-muted').textContent = width;
    document.querySelector('label[for="barcodeHeight"] + .text-muted').textContent = height;
    
    // Generate main barcode
    generateBarcode('barcode', {
        width: width,
        height: height,
        displayValue: showText
    });
    
    // Generate print barcode
    generateBarcode('print-barcode', {
        width: 1.5,
        height: 60,
        displayValue: false
    });
}

function printBarcode() {
    window.print();
}

// Initialize barcodes when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBarcode();
});
</script>
{% endblock %}
