{% extends "base.html" %}

{% block title %}تقارير المخزون{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-chart-bar me-2"></i>تقارير المخزون</h2>
                <div class="btn-group">
                    <button onclick="exportToExcel()" class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i>تصدير Excel
                    </button>
                    <button onclick="printReport()" class="btn btn-info">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-boxes fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">إجمالي المنتجات</h5>
                    <h3 class="text-primary">{{ inventory_data|length }}</h3>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                    <h5 class="card-title">قيمة المخزون</h5>
                    <h3 class="text-success">{{ "{:,.2f}".format(total_value) }} ر.س</h3>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">مخزون منخفض</h5>
                    <h3 class="text-warning">
                        {{ inventory_data | selectattr('status', 'equalto', 'منخفض') | list | length }}
                    </h3>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-chart-pie fa-2x text-info mb-2"></i>
                    <h5 class="card-title">متوسط القيمة</h5>
                    <h3 class="text-info">
                        {% if inventory_data %}
                        {{ "{:,.2f}".format(total_value / inventory_data|length) }} ر.س
                        {% else %}
                        0.00 ر.س
                        {% endif %}
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Valuation Report -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        تقرير تقييم المخزون
                    </h5>
                </div>
                <div class="card-body" id="reportContent">
                    {% if inventory_data %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="inventoryTable">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الباركود</th>
                                    <th>الفئة</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>سعر التكلفة</th>
                                    <th>إجمالي القيمة</th>
                                    <th>الحالة</th>
                                    <th>الموقع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inventory_data %}
                                <tr class="{% if item.status == 'منخفض' %}table-warning{% endif %}">
                                    <td>
                                        <strong>{{ item.product.name }}</strong>
                                        {% if item.product.description %}
                                        <br>
                                        <small class="text-muted">{{ item.product.description[:50] }}{% if item.product.description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.product.barcode }}</td>
                                    <td>
                                        {% if item.product.category %}
                                        <span class="badge bg-light text-dark">{{ item.product.category.name }}</span>
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ "{:,.2f}".format(item.product.quantity) }}
                                        <small class="text-muted">{{ item.product.unit }}</small>
                                    </td>
                                    <td>
                                        {{ "{:,.2f}".format(item.product.min_quantity) }}
                                        <small class="text-muted">{{ item.product.unit }}</small>
                                    </td>
                                    <td>{{ "{:,.2f}".format(item.product.cost_price) }} ر.س</td>
                                    <td>
                                        <strong>{{ "{:,.2f}".format(item.value) }} ر.س</strong>
                                    </td>
                                    <td>
                                        {% if item.status == 'منخفض' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>منخفض
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>طبيعي
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.product.location %}
                                        {{ item.product.location }}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <th colspan="6" class="text-end">الإجمالي:</th>
                                    <th>{{ "{:,.2f}".format(total_value) }} ر.س</th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد منتجات في المخزون</h5>
                        <p class="text-muted">لم يتم العثور على أي منتجات لإنشاء التقرير</p>
                        <a href="{{ url_for('products.add') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    {% if inventory_data %}
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع قيمة المخزون حسب الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        حالة المخزون
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Export to Excel function
function exportToExcel() {
    // Create a simple CSV export
    let csv = 'المنتج,الباركود,الفئة,الكمية الحالية,الحد الأدنى,سعر التكلفة,إجمالي القيمة,الحالة,الموقع\n';
    
    const table = document.getElementById('inventoryTable');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let rowData = [];
        cells.forEach(cell => {
            rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
        });
        csv += rowData.join(',') + '\n';
    });
    
    // Download CSV
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'تقرير_المخزون_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

// Print function
function printReport() {
    const printContent = document.getElementById('reportContent').innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="direction: rtl; font-family: Arial, sans-serif;">
            <h2 style="text-align: center; margin-bottom: 20px;">تقرير تقييم المخزون</h2>
            <p style="text-align: center; margin-bottom: 30px;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
}

// Charts
{% if inventory_data %}
// Category Chart
const categoryData = {};
{% for item in inventory_data %}
const category{{ loop.index }} = '{% if item.product.category %}{{ item.product.category.name }}{% else %}غير محدد{% endif %}';
if (!categoryData[category{{ loop.index }}]) {
    categoryData[category{{ loop.index }}] = 0;
}
categoryData[category{{ loop.index }}] += {{ item.value }};
{% endfor %}

const categoryChart = new Chart(document.getElementById('categoryChart'), {
    type: 'pie',
    data: {
        labels: Object.keys(categoryData),
        datasets: [{
            data: Object.values(categoryData),
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Status Chart
const normalCount = {{ inventory_data | selectattr('status', 'equalto', 'طبيعي') | list | length }};
const lowCount = {{ inventory_data | selectattr('status', 'equalto', 'منخفض') | list | length }};

const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: {
        labels: ['طبيعي', 'منخفض'],
        datasets: [{
            data: [normalCount, lowCount],
            backgroundColor: ['#28a745', '#ffc107']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
