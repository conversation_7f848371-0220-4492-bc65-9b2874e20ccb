{% extends "base.html" %}

{% block title %}استيراد الموردين من Excel{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-excel me-2"></i>
                        استيراد الموردين من Excel
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Instructions -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h5>
                        <ul class="mb-0">
                            <li>يجب أن يحتوي الملف على عمود "الاسم" كحد أدنى</li>
                            <li>الأعمدة المدعومة: الاسم، الشخص المسؤول، الهاتف، البريد الإلكتروني، العنوان</li>
                            <li>يمكنك تحميل قالب Excel جاهز للاستخدام</li>
                        </ul>
                    </div>

                    <!-- Download Template -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-3x text-success mb-3"></i>
                                    <h5>تحميل القالب</h5>
                                    <p class="text-muted">احصل على قالب Excel جاهز للاستخدام</p>
                                    <a href="{{ url_for('suppliers.download_template') }}" class="btn btn-success">
                                        <i class="fas fa-download me-2"></i>
                                        تحميل القالب
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                                    <h5>رفع الملف</h5>
                                    <p class="text-muted">ارفع ملف Excel المحتوي على بيانات الموردين</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-8 mx-auto">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="file" class="form-label">
                                                <i class="fas fa-file-excel me-2"></i>
                                                اختر ملف Excel
                                            </label>
                                            <input type="file" 
                                                   class="form-control" 
                                                   id="file" 
                                                   name="file" 
                                                   accept=".xlsx,.xls"
                                                   required>
                                            <div class="invalid-feedback">
                                                يرجى اختيار ملف Excel
                                            </div>
                                            <div class="form-text">
                                                الملفات المدعومة: .xlsx, .xls
                                            </div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-upload me-2"></i>
                                                استيراد البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Back Button -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <a href="{{ url_for('suppliers.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة إلى قائمة الموردين
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample Data Modal -->
<div class="modal fade" id="sampleDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">مثال على البيانات المطلوبة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>الاسم</th>
                                <th>الشخص المسؤول</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>العنوان</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>شركة قطع الغيار المتقدمة</td>
                                <td>أحمد محمد</td>
                                <td>0501234567</td>
                                <td><EMAIL></td>
                                <td>الرياض، المملكة العربية السعودية</td>
                            </tr>
                            <tr>
                                <td>مؤسسة الأجزاء الأصلية</td>
                                <td>سارة أحمد</td>
                                <td>0112345678</td>
                                <td><EMAIL></td>
                                <td>جدة، المملكة العربية السعودية</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// File input preview
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'alert alert-success mt-2';
        fileInfo.innerHTML = `
            <i class="fas fa-file-excel me-2"></i>
            <strong>الملف المحدد:</strong> ${fileName} (${fileSize} MB)
        `;
        
        // Remove existing file info
        const existingInfo = document.querySelector('.file-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        fileInfo.classList.add('file-info');
        e.target.parentNode.appendChild(fileInfo);
    }
});
</script>
{% endblock %}
