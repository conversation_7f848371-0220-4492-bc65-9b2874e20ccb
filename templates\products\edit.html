{% extends "base.html" %}

{% block title %}تعديل المنتج{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-edit text-primary"></i>
                    تعديل المنتج
                </h2>
                <a href="{{ url_for('products.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمنتجات
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-box me-2"></i>
                                معلومات المنتج
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">
                                                اسم المنتج <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="{{ product.name }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">التصنيف</label>
                                            <select class="form-select" id="category_id" name="category_id">
                                                <option value="">اختر التصنيف</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}" 
                                                        {% if product.category_id == category.id %}selected{% endif %}>
                                                    {{ category.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ product.description or '' }}</textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="cost_price" class="form-label">سعر التكلفة</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                                       step="0.01" min="0" value="{{ product.cost_price }}">
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="selling_price" class="form-label">
                                                سعر البيع <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                                       step="0.01" min="0" value="{{ product.selling_price }}" required>
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="quantity" class="form-label">الكمية الحالية</label>
                                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                                   min="0" value="{{ product.quantity }}">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="min_quantity" class="form-label">الحد الأدنى</label>
                                            <input type="number" class="form-control" id="min_quantity" name="min_quantity" 
                                                   min="0" value="{{ product.min_quantity }}">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="unit" class="form-label">الوحدة</label>
                                            <select class="form-select" id="unit" name="unit">
                                                <option value="قطعة" {% if product.unit == 'قطعة' %}selected{% endif %}>قطعة</option>
                                                <option value="كيلو" {% if product.unit == 'كيلو' %}selected{% endif %}>كيلو</option>
                                                <option value="متر" {% if product.unit == 'متر' %}selected{% endif %}>متر</option>
                                                <option value="لتر" {% if product.unit == 'لتر' %}selected{% endif %}>لتر</option>
                                                <option value="علبة" {% if product.unit == 'علبة' %}selected{% endif %}>علبة</option>
                                                <option value="كرتون" {% if product.unit == 'كرتون' %}selected{% endif %}>كرتون</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="location" class="form-label">الموقع في المخزن</label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           value="{{ product.location or '' }}" placeholder="مثال: A1-01">
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التغييرات
                                    </button>
                                    <a href="{{ url_for('products.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Product Info Card -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات إضافية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">الباركود</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ product.barcode }}" readonly>
                                    <a href="{{ url_for('products.barcode', id=product.id) }}" 
                                       class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-barcode"></i>
                                    </a>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تاريخ الإنشاء</label>
                                <input type="text" class="form-control" 
                                       value="{{ product.created_at.strftime('%Y-%m-%d %H:%M') if product.created_at else 'غير محدد' }}" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">آخر تحديث</label>
                                <input type="text" class="form-control" 
                                       value="{{ product.updated_at.strftime('%Y-%m-%d %H:%M') if product.updated_at else 'غير محدد' }}" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">حالة المخزون</label>
                                <div>
                                    {% if product.quantity == 0 %}
                                        <span class="badge bg-danger">نفد المخزون</span>
                                    {% elif product.is_low_stock %}
                                        <span class="badge bg-warning">مخزون منخفض</span>
                                    {% else %}
                                        <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">قيمة المخزون</label>
                                <input type="text" class="form-control" 
                                       value="{{ (product.quantity * product.cost_price)|currency }}" readonly>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('products.barcode', id=product.id) }}" 
                                   class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="fas fa-barcode me-2"></i>
                                    عرض الباركود
                                </a>
                                
                                <button type="button" class="btn btn-outline-success btn-sm" 
                                        onclick="printBarcode({{ product.id }})">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة الباركود
                                </button>
                                
                                <a href="{{ url_for('inventory.add_adjustment') }}?product_id={{ product.id }}" 
                                   class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    تعديل المخزون
                                </a>
                                
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        onclick="confirmDelete({{ product.id }}, '{{ product.name }}')">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف المنتج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function printBarcode(productId) {
    const count = prompt('كم نسخة تريد طباعتها؟', '1');
    
    if (count && !isNaN(count) && parseInt(count) > 0) {
        const copies = Math.min(parseInt(count), 100);
        const url = `/products/print-barcode/${productId}?count=${copies}&auto_print=true`;
        
        const printWindow = window.open(url, '_blank', 'width=800,height=600');
        if (printWindow) {
            printWindow.focus();
        }
    }
}

function confirmDelete(productId, productName) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
        window.location.href = `/products/delete/${productId}`;
    }
}

// Auto-calculate profit margin
document.getElementById('cost_price').addEventListener('input', calculateMargin);
document.getElementById('selling_price').addEventListener('input', calculateMargin);

function calculateMargin() {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (costPrice > 0 && sellingPrice > 0) {
        const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
        const profit = (sellingPrice - costPrice).toFixed(2);
        
        // You can add a margin display element if needed
        console.log(`Profit: ${profit} SAR, Margin: ${margin}%`);
    }
}
</script>
{% endblock %}
