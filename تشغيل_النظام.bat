@echo off
chcp 65001 >nul
title AutoParts Manager V5.1 - نظام إدارة قطع الغيار

echo ========================================
echo    AutoParts Manager V5.1
echo    نظام إدارة قطع الغيار المتكامل
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت
echo.

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo ✅ البيئة الافتراضية موجودة
    echo جاري تفعيل البيئة الافتراضية...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  البيئة الافتراضية غير موجودة
    echo جاري إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    call venv\Scripts\activate.bat
    echo ✅ تم إنشاء البيئة الافتراضية
)

echo.

REM Install requirements
echo جاري تثبيت المتطلبات...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

REM Check if database exists
if not exist "autoparts.db" (
    echo ⚠️  قاعدة البيانات غير موجودة
    echo سيتم إنشاء قاعدة بيانات جديدة
    echo.
)

echo 🚀 جاري تشغيل النظام...
echo.
echo 📋 بيانات الدخول الافتراضية:
echo    المدير: admin / admin123
echo    المحاسب: accountant / acc123
echo    الكاشير: cashier / cash123
echo.
echo 🌐 سيتم فتح النظام في المتصفح على العنوان:
echo    http://127.0.0.1:8888
echo.
echo ⏹️  لإيقاف النظام: اضغط Ctrl+C في هذه النافذة
echo ========================================
echo.

REM Start the application
python run.py

echo.
echo 🛑 تم إيقاف النظام
echo.
pause
