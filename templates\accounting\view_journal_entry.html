{% extends "base.html" %}

{% block title %}عرض القيد اليومي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-eye text-primary"></i>
                    عرض القيد اليومي
                </h2>
                <a href="{{ url_for('accounting.journal_entries') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقيود اليومية
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-file-invoice me-2"></i>
                                تفاصيل القيد اليومي
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Entry Header Info -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="info-box">
                                        <label class="form-label fw-bold">رقم القيد:</label>
                                        <p class="text-primary fs-5">{{ entry.entry_number }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-box">
                                        <label class="form-label fw-bold">تاريخ القيد:</label>
                                        <p>{{ entry.transaction_date.strftime('%Y-%m-%d') if entry.transaction_date else 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="info-box">
                                        <label class="form-label fw-bold">الوصف:</label>
                                        <p>{{ entry.description }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Journal Entries Table -->
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-list me-2"></i>
                                تفاصيل القيد
                            </h6>
                            
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th style="width: 5%">#</th>
                                            <th style="width: 15%">رقم الحساب</th>
                                            <th style="width: 35%">اسم الحساب</th>
                                            <th style="width: 20%">مدين</th>
                                            <th style="width: 20%">دائن</th>
                                            <th style="width: 5%">الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for related_entry in related_entries %}
                                        <tr>
                                            <td class="text-center">{{ loop.index }}</td>
                                            <td>{{ related_entry.account.code }}</td>
                                            <td>
                                                <strong>{{ related_entry.account.name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ related_entry.account.account_type }}</small>
                                            </td>
                                            <td class="text-end">
                                                {% if related_entry.debit_amount > 0 %}
                                                    <span class="text-success fw-bold">{{ related_entry.debit_amount|currency }}</span>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-end">
                                                {% if related_entry.credit_amount > 0 %}
                                                    <span class="text-danger fw-bold">{{ related_entry.credit_amount|currency }}</span>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-center">
                                                {% if related_entry.is_approved %}
                                                    <span class="badge bg-success">معتمد</span>
                                                {% else %}
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <th colspan="3" class="text-end">الإجمالي:</th>
                                            <th class="text-end">
                                                <span class="text-success fw-bold">{{ total_debits|currency }}</span>
                                            </th>
                                            <th class="text-end">
                                                <span class="text-danger fw-bold">{{ total_credits|currency }}</span>
                                            </th>
                                            <th></th>
                                        </tr>
                                        <tr>
                                            <th colspan="3" class="text-end">الفرق:</th>
                                            <th colspan="2" class="text-center">
                                                {% set difference = total_debits - total_credits %}
                                                {% if difference == 0 %}
                                                    <span class="badge bg-success fs-6">متوازن</span>
                                                {% else %}
                                                    <span class="badge bg-danger fs-6">غير متوازن ({{ difference|currency }})</span>
                                                {% endif %}
                                            </th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Entry Info Card -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات القيد
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">المستخدم:</label>
                                <p class="mb-0">{{ entry.user.username if entry.user else 'غير محدد' }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تاريخ الإنشاء:</label>
                                <p class="mb-0">{{ entry.created_at.strftime('%Y-%m-%d %H:%M') if entry.created_at else 'غير محدد' }}</p>
                            </div>

                            {% if entry.reference_type %}
                            <div class="mb-3">
                                <label class="form-label">نوع المرجع:</label>
                                <p class="mb-0">
                                    {% if entry.reference_type == 'sale' %}
                                        <span class="badge bg-success">مبيعات</span>
                                    {% elif entry.reference_type == 'purchase' %}
                                        <span class="badge bg-primary">مشتريات</span>
                                    {% elif entry.reference_type == 'payment' %}
                                        <span class="badge bg-warning">دفع</span>
                                    {% elif entry.reference_type == 'expense' %}
                                        <span class="badge bg-danger">مصروف</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ entry.reference_type }}</span>
                                    {% endif %}
                                </p>
                            </div>
                            {% endif %}

                            {% if entry.reference_id %}
                            <div class="mb-3">
                                <label class="form-label">رقم المرجع:</label>
                                <p class="mb-0">{{ entry.reference_id }}</p>
                            </div>
                            {% endif %}

                            <div class="mb-3">
                                <label class="form-label">حالة الاعتماد:</label>
                                <p class="mb-0">
                                    {% if entry.is_approved %}
                                        <span class="badge bg-success">معتمد</span>
                                    {% else %}
                                        <span class="badge bg-warning">في الانتظار</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Card -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>
                                الإجراءات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="printEntry()">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة القيد
                                </button>
                                
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportEntry()">
                                    <i class="fas fa-file-excel me-2"></i>
                                    تصدير Excel
                                </button>
                                
                                {% if not entry.is_approved %}
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="approveEntry()">
                                    <i class="fas fa-check me-2"></i>
                                    اعتماد القيد
                                </button>
                                {% endif %}
                                
                                <a href="{{ url_for('accounting.journal_entries') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-list me-2"></i>
                                    جميع القيود
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.info-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.info-box label {
    color: #495057;
    margin-bottom: 5px;
}

.info-box p {
    margin-bottom: 0;
    font-weight: 500;
}

@media print {
    .no-print { display: none !important; }
    .card { border: none !important; box-shadow: none !important; }
    .card-header { background: #f8f9fa !important; }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function printEntry() {
    window.print();
}

function exportEntry() {
    // Implement export functionality
    alert('سيتم تنفيذ تصدير Excel قريباً');
}

function approveEntry() {
    if (confirm('هل أنت متأكد من اعتماد هذا القيد؟')) {
        // Implement approval functionality
        alert('سيتم تنفيذ اعتماد القيد قريباً');
    }
}
</script>
{% endblock %}
