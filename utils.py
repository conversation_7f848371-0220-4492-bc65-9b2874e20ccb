# -*- coding: utf-8 -*-
import os
import uuid
import json
import shutil
from datetime import datetime, timedelta
from functools import wraps
from flask import session, redirect, url_for, flash

# Try to import optional dependencies
try:
    import barcode
    from barcode.writer import ImageWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from hijri_converter import Hij<PERSON>, <PERSON>ian
    HIJRI_AVAILABLE = True
except ImportError:
    HIJRI_AVAILABLE = False

def login_required(f):
    """Decorator to require login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يجب تسجيل الدخول أولاً', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                flash('يجب تسجيل الدخول أولاً', 'error')
                return redirect(url_for('login'))

            from models import User
            user = User.query.get(session['user_id'])
            if not user or not user.has_permission(permission):
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
                return redirect(url_for('dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def generate_barcode(product_id, product_name):
    """Generate barcode for product"""
    # Generate barcode number
    barcode_number = f"AP{product_id:06d}"

    if not BARCODE_AVAILABLE:
        print("Barcode library not available")
        return barcode_number, None

    try:
        # Create barcode directory if it doesn't exist
        barcode_dir = os.path.join('static', 'barcodes')
        os.makedirs(barcode_dir, exist_ok=True)

        # Create barcode with better options
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(barcode_number, writer=ImageWriter())

        # Save barcode image with custom options
        filename = f"barcode_{product_id}"
        filepath = os.path.join(barcode_dir, filename)

        # Custom options for better barcode appearance
        options = {
            'module_width': 0.2,
            'module_height': 15.0,
            'quiet_zone': 6.5,
            'font_size': 10,
            'text_distance': 5.0,
            'background': 'white',
            'foreground': 'black',
        }

        barcode_instance.save(filepath, options=options)
        print(f"Barcode generated successfully: {filepath}.png")

        return barcode_number, f"static/barcodes/{filename}.png"
    except Exception as e:
        print(f"Error generating barcode: {e}")
        return barcode_number, None

def generate_invoice_number(prefix="INV"):
    """Generate unique invoice number with microseconds and UUID for absolute uniqueness"""
    import uuid
    now = datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    microseconds = now.microsecond // 1000  # Convert to milliseconds (3 digits)
    # Add short UUID for absolute uniqueness
    short_uuid = str(uuid.uuid4())[:8]
    return f"{prefix}-{timestamp}{microseconds:03d}-{short_uuid}"

def format_currency(amount):
    """Format currency with Arabic locale"""
    return f"{amount:,.2f} ريال"

def hijri_date(gregorian_date=None):
    """Convert Gregorian date to Hijri"""
    if gregorian_date is None:
        gregorian_date = datetime.now().date()

    if HIJRI_AVAILABLE:
        try:
            hijri = Gregorian(gregorian_date.year, gregorian_date.month, gregorian_date.day).to_hijri()
            return f"{hijri.day}/{hijri.month}/{hijri.year} هـ"
        except:
            return ""
    else:
        return ""

def export_to_excel(data, filename, sheet_name="البيانات"):
    """Export data to Excel file"""
    if not PANDAS_AVAILABLE:
        print("Pandas not available for Excel export")
        return None

    try:
        # Create exports directory if it doesn't exist
        exports_dir = os.path.join('static', 'exports')
        os.makedirs(exports_dir, exist_ok=True)

        # Create DataFrame
        df = pd.DataFrame(data)

        # Save to Excel
        filepath = os.path.join(exports_dir, f"{filename}.xlsx")
        df.to_excel(filepath, sheet_name=sheet_name, index=False, engine='openpyxl')

        return filepath
    except Exception as e:
        print(f"Error exporting to Excel: {e}")
        return None

def create_pdf_report(data, title, filename, headers=None):
    """Create PDF report"""
    if not REPORTLAB_AVAILABLE:
        print("ReportLab not available for PDF export")
        return None

    try:
        # Create reports directory if it doesn't exist
        reports_dir = os.path.join('static', 'reports')
        os.makedirs(reports_dir, exist_ok=True)

        filepath = os.path.join(reports_dir, f"{filename}.pdf")

        # Create PDF document
        doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        # Container for the 'Flowable' objects
        elements = []

        # Define styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # Center alignment
        )

        # Add title
        title_para = Paragraph(title, title_style)
        elements.append(title_para)
        elements.append(Spacer(1, 12))

        # Add table if data exists
        if data:
            if headers:
                table_data = [headers] + data
            else:
                table_data = data

            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(table)

        # Build PDF
        doc.build(elements)

        return filepath
    except Exception as e:
        print(f"Error creating PDF report: {e}")
        return None

def backup_database():
    """Create database backup"""
    try:
        # Create backups directory if it doesn't exist
        backups_dir = os.path.join('backups')
        os.makedirs(backups_dir, exist_ok=True)

        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"autoparts_backup_{timestamp}.db"
        backup_path = os.path.join(backups_dir, backup_filename)

        # Find database file - check multiple possible locations
        db_locations = [
            'autoparts.db',
            'instance/autoparts.db',
            os.path.join('instance', 'autoparts.db')
        ]

        source_db = None
        for location in db_locations:
            if os.path.exists(location):
                source_db = location
                break

        if not source_db:
            print("Database file not found in any expected location")
            return None

        # Copy database file
        shutil.copy2(source_db, backup_path)
        print(f"Database backup created: {backup_path}")

        return backup_path
    except Exception as e:
        print(f"Error creating backup: {e}")
        return None

def restore_database(backup_path):
    """Restore database from backup"""
    try:
        if os.path.exists(backup_path):
            # Create a backup of current database before restore
            current_backup = backup_database()

            # Find target database location
            target_locations = [
                'autoparts.db',
                'instance/autoparts.db',
                os.path.join('instance', 'autoparts.db')
            ]

            target_db = None
            for location in target_locations:
                if os.path.exists(location):
                    target_db = location
                    break

            # If no existing database found, use instance/autoparts.db as default
            if not target_db:
                target_db = os.path.join('instance', 'autoparts.db')
                # Create instance directory if it doesn't exist
                os.makedirs('instance', exist_ok=True)

            # Restore from backup
            shutil.copy2(backup_path, target_db)
            print(f"Database restored to: {target_db}")

            return True
        return False
    except Exception as e:
        print(f"Error restoring database: {e}")
        return False

def log_audit(user_id, action, table_name, record_id=None, old_values=None, new_values=None, ip_address=None):
    """Log audit trail"""
    try:
        from models import db, AuditLog
        audit_log = AuditLog(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None,
            ip_address=ip_address
        )
        db.session.add(audit_log)
        db.session.commit()
    except Exception as e:
        print(f"Error logging audit: {e}")

def calculate_tax(amount, tax_rate=0.15):
    """Calculate tax amount"""
    return amount * tax_rate

def get_low_stock_products():
    """Get products with low stock"""
    from models import Product
    return Product.query.filter(Product.quantity <= Product.min_quantity, Product.is_active == True).all()

def get_dashboard_stats():
    """Get dashboard statistics"""
    from models import Product, Customer, Supplier, Sale, Purchase

    stats = {
        'total_products': Product.query.filter_by(is_active=True).count(),
        'total_customers': Customer.query.filter_by(is_active=True).count(),
        'total_suppliers': Supplier.query.filter_by(is_active=True).count(),
        'low_stock_count': len(get_low_stock_products()),
        'today_sales': Sale.query.filter(
            Sale.sale_date >= datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        ).count(),
        'monthly_sales': Sale.query.filter(
            Sale.sale_date >= datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        ).count()
    }

    return stats

def create_import_template(headers, sample_data, filename, sheet_name="Sheet1"):
    """Create Excel template for import with sample data"""
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = sheet_name

        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        required_font = Font(bold=True, color="FF0000")
        sample_fill = PatternFill(start_color="E7F3FF", end_color="E7F3FF", fill_type="solid")

        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Add headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border

            # Mark required fields
            if header.endswith('*'):
                cell.font = Font(bold=True, color="FFFFFF")

        # Add sample data
        for row_idx, data in enumerate(sample_data, 2):
            for col_idx, header in enumerate(headers, 1):
                value = data.get(header, '')
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.fill = sample_fill
                cell.border = border

                # Format numbers
                if isinstance(value, (int, float)):
                    cell.number_format = '#,##0.00'

        # Add instructions sheet
        instructions_ws = wb.create_sheet("تعليمات الاستيراد")
        instructions = [
            ["تعليمات استيراد البيانات", ""],
            ["", ""],
            ["الحقول المطلوبة (مميزة بـ *):", ""],
            ["• يجب ملء جميع الحقول المطلوبة", ""],
            ["• لا تترك خلايا فارغة في الأعمدة المطلوبة", ""],
            ["• تأكد من صحة البيانات قبل الاستيراد", ""],
            ["", ""],
            ["نصائح مهمة:", ""],
            ["• لا تغير أسماء الأعمدة", ""],
            ["• استخدم أرقام صحيحة للكميات والأسعار", ""],
            ["• تأكد من عدم تكرار الباركود", ""],
            ["• احفظ الملف بصيغة Excel (.xlsx)", ""],
            ["", ""],
            ["في حالة وجود أخطاء:", ""],
            ["• راجع رسائل الخطأ بعناية", ""],
            ["• تأكد من تنسيق البيانات", ""],
            ["• جرب استيراد ملف صغير أولاً", ""]
        ]

        for row_idx, (instruction, _) in enumerate(instructions, 1):
            cell = instructions_ws.cell(row=row_idx, column=1, value=instruction)
            if row_idx == 1:
                cell.font = Font(bold=True, size=14, color="366092")
            elif instruction.startswith("•") or instruction.endswith(":"):
                cell.font = Font(bold=True)

        # Adjust column widths
        for ws_item in [ws, instructions_ws]:
            for column in ws_item.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws_item.column_dimensions[column_letter].width = adjusted_width

        # Create exports directory if it doesn't exist
        exports_dir = os.path.join('exports', 'templates')
        os.makedirs(exports_dir, exist_ok=True)

        # Save file
        filepath = os.path.join(exports_dir, f"{filename}.xlsx")
        wb.save(filepath)

        return filepath

    except Exception as e:
        print(f"Error creating import template: {e}")
        return None

def create_pdf_report(data, title, filename, headers=None):
    """Create PDF report using ReportLab"""
    try:
        # Import ReportLab
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT

        # Create exports directory if it doesn't exist
        exports_dir = os.path.join('exports', 'reports')
        os.makedirs(exports_dir, exist_ok=True)

        # Create PDF file
        filepath = os.path.join(exports_dir, f"{filename}.pdf")
        doc = SimpleDocTemplate(
            filepath,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )

        # Build story
        story = []
        styles = getSampleStyleSheet()

        # Title with Arabic support
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=20,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        )

        # Add title
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 20))

        # Add date
        date_style = ParagraphStyle(
            'DateStyle',
            parent=styles['Normal'],
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=20
        )
        story.append(Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", date_style))
        story.append(Spacer(1, 20))

        # Create table
        if headers:
            table_data = [headers] + data
        else:
            table_data = data

        # Create table with proper styling
        table = Table(table_data, repeatRows=1)

        # Table style
        table_style = [
            # Header row styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#007bff')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('TOPPADDING', (0, 0), (-1, 0), 12),

            # Data rows styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
            ('TOPPADDING', (0, 1), (-1, -1), 8),

            # Grid and borders
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),

            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')])
        ]

        table.setStyle(TableStyle(table_style))

        story.append(table)

        # Add footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'FooterStyle',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph("نظام إدارة قطع الغيار - AutoParts Manager", footer_style))

        # Build PDF
        doc.build(story)

        print(f"PDF report created successfully: {filepath}")
        return filepath

    except ImportError as e:
        print(f"ReportLab not available for PDF export: {e}")
        return None

    except Exception as e:
        print(f"Error creating PDF report: {e}")
        return None
