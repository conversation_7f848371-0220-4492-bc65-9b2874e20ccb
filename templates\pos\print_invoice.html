<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة فاتورة رقم {{ sale.invoice_number }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
            .container { max-width: none; margin: 0; padding: 0; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .invoice-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }
        
        .invoice-body {
            padding: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .detail-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            flex: 1;
            margin: 0 5px;
            min-width: 200px;
        }
        
        .items-table {
            margin: 30px 0;
        }
        
        .table th {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 15px 10px;
        }
        
        .table td {
            padding: 12px 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .totals-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .total-row.final {
            border-top: 2px solid #007bff;
            padding-top: 15px;
            margin-top: 15px;
            font-weight: bold;
            font-size: 1.2em;
            color: #007bff;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .invoice-details {
                flex-direction: column;
            }
            .detail-box {
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button onclick="window.print()" class="btn btn-primary print-button no-print">
        <i class="fas fa-print me-2"></i>
        طباعة
    </button>

    <div class="invoice-container">
        <!-- Invoice Header -->
        <div class="invoice-header">
            <h1 class="mb-0">فاتورة مبيعات</h1>
            <h3 class="mt-2">رقم {{ sale.invoice_number }}</h3>
        </div>

        <!-- Invoice Body -->
        <div class="invoice-body">
            <!-- Company Info -->
            <div class="company-info">
                <h2 class="text-primary">نظام إدارة قطع الغيار</h2>
                <p class="text-muted mb-0">AutoParts Manager</p>
                <p class="text-muted">إدارة شاملة لقطع الغيار والمبيعات</p>
            </div>

            <!-- Invoice Details -->
            <div class="invoice-details">
                <div class="detail-box">
                    <h6 class="text-primary mb-2">معلومات الفاتورة</h6>
                    <p class="mb-1"><strong>رقم الفاتورة:</strong> {{ sale.invoice_number }}</p>
                    <p class="mb-1"><strong>التاريخ:</strong> {{ sale.sale_date.strftime('%Y-%m-%d') if sale.sale_date else 'غير محدد' }}</p>
                    <p class="mb-0"><strong>الوقت:</strong> {{ sale.sale_date.strftime('%H:%M') if sale.sale_date else 'غير محدد' }}</p>
                </div>

                <div class="detail-box">
                    <h6 class="text-primary mb-2">معلومات العميل</h6>
                    <p class="mb-1"><strong>العميل:</strong> {{ sale.customer.name if sale.customer else 'عميل نقدي' }}</p>
                    {% if sale.customer %}
                    <p class="mb-1"><strong>الهاتف:</strong> {{ sale.customer.phone or 'غير محدد' }}</p>
                    <p class="mb-0"><strong>العنوان:</strong> {{ sale.customer.address or 'غير محدد' }}</p>
                    {% endif %}
                </div>

                <div class="detail-box">
                    <h6 class="text-primary mb-2">طريقة الدفع</h6>
                    <p class="mb-1"><strong>الطريقة:</strong> 
                        {% if sale.payment_method == 'cash' %}نقدي
                        {% elif sale.payment_method == 'card' %}بطاقة
                        {% elif sale.payment_method == 'bank' %}تحويل بنكي
                        {% else %}{{ sale.payment_method }}
                        {% endif %}
                    </p>
                    <p class="mb-0"><strong>الحالة:</strong> 
                        <span class="badge bg-success">مدفوعة</span>
                    </p>
                </div>
            </div>

            <!-- Items Table -->
            <div class="items-table">
                <h5 class="text-primary mb-3">تفاصيل المنتجات</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 5%">#</th>
                                <th style="width: 40%">المنتج</th>
                                <th style="width: 15%">الكمية</th>
                                <th style="width: 20%">سعر الوحدة</th>
                                <th style="width: 20%">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in sale.items %}
                            <tr>
                                <td class="text-center">{{ loop.index }}</td>
                                <td>
                                    <strong>{{ item.product.name }}</strong>
                                    {% if item.product.description %}
                                    <br><small class="text-muted">{{ item.product.description }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ item.quantity }} {{ item.product.unit }}</td>
                                <td class="text-end">{{ "{:,.2f}".format(item.unit_price) }} ر.س</td>
                                <td class="text-end">{{ "{:,.2f}".format(item.total_price) }} ر.س</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Totals Section -->
            <div class="totals-section">
                <div class="row">
                    <div class="col-md-6">
                        {% if sale.notes %}
                        <h6 class="text-primary">ملاحظات:</h6>
                        <p class="text-muted">{{ sale.notes }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span>{{ "{:,.2f}".format(sale.subtotal) }} ر.س</span>
                        </div>
                        
                        {% if sale.discount_amount > 0 %}
                        <div class="total-row">
                            <span>الخصم:</span>
                            <span class="text-danger">- {{ "{:,.2f}".format(sale.discount_amount) }} ر.س</span>
                        </div>
                        {% endif %}
                        
                        {% if sale.tax_amount > 0 %}
                        <div class="total-row">
                            <span>ضريبة القيمة المضافة (15%):</span>
                            <span>{{ "{:,.2f}".format(sale.tax_amount) }} ر.س</span>
                        </div>
                        {% endif %}
                        
                        <div class="total-row final">
                            <span>المجموع الكلي:</span>
                            <span>{{ "{:,.2f}".format(sale.total_amount) }} ر.س</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-4 pt-4 border-top">
                <p class="text-muted mb-1">شكراً لتعاملكم معنا</p>
                <p class="text-muted mb-0">نتطلع لخدمتكم مرة أخرى</p>
                <small class="text-muted">تم إنشاء هذه الفاتورة بواسطة نظام إدارة قطع الغيار</small>
            </div>
        </div>
    </div>

    <!-- Auto Print Script -->
    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print function
        function printInvoice() {
            window.print();
        }
        
        // Close window after printing (optional)
        window.onafterprint = function() {
            // window.close();
        }
    </script>

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</body>
</html>
