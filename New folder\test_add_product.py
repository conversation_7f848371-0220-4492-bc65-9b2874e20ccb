#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة منتج جديد
Test Add Product Functionality
"""

import requests
import time
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class AddProductTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_add_product_page(self):
        """اختبار صفحة إضافة منتج"""
        print("\n🔄 اختبار صفحة إضافة منتج...")
        try:
            response = self.session.get(f"{BASE_URL}/products/add")
            if response.status_code == 200:
                print("✅ صفحة إضافة منتج تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة إضافة منتج: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في صفحة إضافة منتج: {e}")
            return False
    
    def test_add_product_submission(self):
        """اختبار إضافة منتج جديد"""
        print("\n🔄 اختبار إضافة منتج جديد...")
        try:
            # بيانات المنتج الجديد
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            product_data = {
                'name': f'منتج اختبار {timestamp}',
                'description': 'وصف منتج اختبار',
                'category_id': 1,  # افتراض وجود فئة برقم 1
                'cost_price': 10.50,
                'selling_price': 15.75,
                'quantity': 100,
                'min_quantity': 10,
                'unit': 'قطعة',
                'location': 'A1-01'
            }
            
            response = self.session.post(f"{BASE_URL}/products/add", data=product_data)
            
            if response.status_code == 200:
                # تحقق من وجود رسالة نجاح أو إعادة توجيه
                if 'تم إضافة المنتج بنجاح' in response.text or response.url.endswith('/products/'):
                    print("✅ تم إضافة المنتج بنجاح")
                    return True
                else:
                    print("❌ لم يتم إضافة المنتج - لا توجد رسالة نجاح")
                    print(f"📄 جزء من الاستجابة: {response.text[:500]}...")
                    return False
            elif response.status_code == 302:
                # إعادة توجيه - عادة تعني نجاح
                print("✅ تم إضافة المنتج بنجاح (إعادة توجيه)")
                return True
            else:
                print(f"❌ فشل في إضافة المنتج: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:500]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إضافة المنتج: {e}")
            return False
    
    def test_products_list(self):
        """اختبار صفحة قائمة المنتجات"""
        print("\n🔄 اختبار صفحة قائمة المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/")
            if response.status_code == 200:
                print("✅ صفحة قائمة المنتجات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة قائمة المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في صفحة قائمة المنتجات: {e}")
            return False
    
    def test_product_search(self):
        """اختبار البحث عن منتج"""
        print("\n🔄 اختبار البحث عن منتج...")
        try:
            response = self.session.get(f"{BASE_URL}/products/search?q=اختبار")
            if response.status_code == 200:
                print("✅ البحث عن منتج يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في البحث عن منتج: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في البحث عن منتج: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات المنتجات"""
        print("🛠️ بدء اختبار وظائف المنتجات")
        print("=" * 50)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("صفحة إضافة منتج", self.test_add_product_page),
            ("إضافة منتج جديد", self.test_add_product_submission),
            ("صفحة قائمة المنتجات", self.test_products_list),
            ("البحث عن منتج", self.test_product_search),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار وظائف المنتجات:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف المنتجات تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️  يوجد {failed} مشكلة في وظائف المنتجات")
            return False

def main():
    """الدالة الرئيسية"""
    print("🛠️ اختبار وظائف المنتجات - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = AddProductTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
