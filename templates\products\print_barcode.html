<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الباركود - {{ product.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            
            .barcode-item {
                page-break-inside: avoid;
                margin: 5px;
                padding: 10px;
                border: 1px solid #ddd;
                text-align: center;
                width: 200px;
                height: 120px;
                display: inline-block;
                vertical-align: top;
            }
            
            .barcode-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;
                align-items: flex-start;
            }
            
            body {
                margin: 0;
                padding: 10px;
            }
        }
        
        .barcode-item {
            margin: 10px;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            text-align: center;
            width: 220px;
            height: 140px;
            display: inline-block;
            vertical-align: top;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .barcode-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: flex-start;
            gap: 10px;
            margin-top: 20px;
        }
        
        .barcode-image {
            max-width: 180px;
            height: auto;
            margin: 5px 0;
        }
        
        .product-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .product-price {
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
            margin-top: 5px;
        }
        
        .barcode-number {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }
        
        .print-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .count-input {
            max-width: 100px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Print Controls -->
        <div class="print-controls no-print">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">
                        <i class="fas fa-barcode"></i>
                        طباعة الباركود - {{ product.name }}
                    </h4>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="window.close()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-md-4">
                    <label for="count" class="form-label">عدد النسخ:</label>
                    <div class="input-group">
                        <input type="number" class="form-control count-input" id="count" 
                               value="{{ count }}" min="1" max="100">
                        <button class="btn btn-outline-primary" type="button" onclick="updateCount()">
                            تحديث
                        </button>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="alert alert-info mb-0">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            سيتم طباعة {{ count }} نسخة من الباركود. يمكنك تغيير العدد وتحديث الصفحة.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Barcode Container -->
        <div class="barcode-container">
            {% for i in range(count) %}
            <div class="barcode-item">
                <div class="product-name">{{ product.name }}</div>
                
                {% if product.barcode and product.barcode.startswith('AP') %}
                    <!-- Try to show actual barcode image if it exists -->
                    {% set barcode_path = "static/barcodes/barcode_" + product.id|string + ".png" %}
                    <img src="{{ url_for('static', filename='barcodes/barcode_' + product.id|string + '.png') }}" 
                         alt="Barcode" class="barcode-image"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    
                    <!-- Fallback text if image doesn't load -->
                    <div style="display: none; font-family: 'Courier New', monospace; font-size: 16px; font-weight: bold; margin: 10px 0;">
                        {{ product.barcode }}
                    </div>
                {% else %}
                    <!-- Show barcode as text if no proper barcode -->
                    <div style="font-family: 'Courier New', monospace; font-size: 16px; font-weight: bold; margin: 10px 0;">
                        {{ product.barcode or 'لا يوجد باركود' }}
                    </div>
                {% endif %}
                
                <div class="product-price">{{ "%.2f"|format(product.selling_price) }} ريال</div>
                <div class="barcode-number">{{ product.barcode }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <script>
        function updateCount() {
            const count = document.getElementById('count').value;
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('count', count);
            window.location.href = currentUrl.toString();
        }
        
        // Auto-focus on print when page loads
        window.addEventListener('load', function() {
            // Auto-print if requested
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auto_print') === 'true') {
                setTimeout(function() {
                    window.print();
                }, 500);
            }
        });
        
        // Handle Enter key in count input
        document.getElementById('count').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                updateCount();
            }
        });
    </script>
</body>
</html>
