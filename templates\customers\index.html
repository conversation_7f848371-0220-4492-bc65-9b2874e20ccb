{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-users me-2"></i>
        إدارة العملاء
    </h1>
    <div>
        {% if has_permission('all') %}
        <a href="{{ url_for('customers.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة عميل جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search"
                       value="{{ request.args.get('search', '') }}"
                       placeholder="اسم العميل أو رقم الهاتف">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع العميل</label>
                <select class="form-select" name="customer_type">
                    <option value="">جميع الأنواع</option>
                    <option value="individual" {% if request.args.get('customer_type') == 'individual' %}selected{% endif %}>
                        فردي
                    </option>
                    <option value="company" {% if request.args.get('customer_type') == 'company' %}selected{% endif %}>
                        شركة
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة الرصيد</label>
                <select class="form-select" name="balance_status">
                    <option value="">جميع الحالات</option>
                    <option value="positive" {% if request.args.get('balance_status') == 'positive' %}selected{% endif %}>
                        رصيد موجب
                    </option>
                    <option value="negative" {% if request.args.get('balance_status') == 'negative' %}selected{% endif %}>
                        رصيد سالب
                    </option>
                    <option value="zero" {% if request.args.get('balance_status') == 'zero' %}selected{% endif %}>
                        رصيد صفر
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة العملاء ({{ customers.total if customers.total else 0 }})</h5>
    </div>
    <div class="card-body">
        {% if customers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>نوع العميل</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>العنوان</th>
                        <th>الرصيد</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in (customers.items if customers.items else customers) %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.company_name %}
                                    <br><small class="text-muted">{{ customer.company_name }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if customer.customer_type == 'individual' %}
                                <span class="badge bg-info">فردي</span>
                            {% else %}
                                <span class="badge bg-success">شركة</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ customer.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.address %}
                                <small>{{ customer.address[:30] }}{% if customer.address|length > 30 %}...{% endif %}</small>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.balance > 0 %}
                                <span class="text-success fw-bold">{{ customer.balance|currency }}</span>
                            {% elif customer.balance < 0 %}
                                <span class="text-danger fw-bold">{{ customer.balance|currency }}</span>
                            {% else %}
                                <span class="text-muted">{{ customer.balance|currency }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ customer.created_at.strftime('%Y-%m-%d') if customer.created_at else 'غير محدد' }}</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('customers.view', customer_id=customer.id) }}"
                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>

                                {% if has_permission('all') %}
                                <a href="{{ url_for('customers.edit', customer_id=customer.id) }}"
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>

                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="deleteCustomer({{ customer.id }}, '{{ customer.name }}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Summary -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ customers.total if customers.total else 0 }}</h4>
                        <p class="mb-0">إجمالي العملاء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ customers.items|selectattr('balance', 'greaterthan', 0)|list|length if customers.items else 0 }}</h4>
                        <p class="mb-0">رصيد موجب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>{{ customers.items|selectattr('balance', 'lessthan', 0)|list|length if customers.items else 0 }}</h4>
                        <p class="mb-0">رصيد سالب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ (customers.items|sum(attribute='balance'))|currency if customers.items else '0.00 ريال' }}</h4>
                        <p class="mb-0">إجمالي الأرصدة</p>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد عملاء</h5>
            <p class="text-muted">لم يتم العثور على عملاء مطابقين لمعايير البحث</p>
            {% if has_permission('all') %}
            <a href="{{ url_for('customers.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة عميل جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العميل <strong id="customerName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteCustomer(customerId, customerName) {
    document.getElementById('customerName').textContent = customerName;
    document.getElementById('deleteForm').action = `/customers/${customerId}/delete`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Auto-hide alerts
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>
{% endblock %}
