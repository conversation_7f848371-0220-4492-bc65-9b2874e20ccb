# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from models import db, Supplier, Purchase, Payment
from utils import login_required, permission_required, log_audit, export_to_excel
from datetime import datetime
from sqlalchemy import func
import pandas as pd
import os
from werkzeug.utils import secure_filename

suppliers_bp = Blueprint('suppliers', __name__)

@suppliers_bp.route('/')
@login_required
@permission_required('all')
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Supplier.query.filter_by(is_active=True)

    if search:
        query = query.filter(Supplier.name.contains(search))

    suppliers = query.order_by(Supplier.name).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('suppliers/index.html',
                         suppliers=suppliers,
                         search=search)

@suppliers_bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def add():
    if request.method == 'POST':
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')

        if not name:
            flash('يرجى إدخال اسم المورد', 'error')
            return render_template('suppliers/add.html')

        supplier = Supplier(
            name=name,
            contact_person=contact_person,
            phone=phone,
            email=email,
            address=address
        )

        db.session.add(supplier)
        db.session.commit()

        # Log audit
        log_audit(session['user_id'], 'إضافة مورد', 'suppliers', supplier.id,
                 new_values={'name': name, 'phone': phone})

        flash('تم إضافة المورد بنجاح', 'success')
        return redirect(url_for('suppliers.index'))

    return render_template('suppliers/add.html')

@suppliers_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def edit(id):
    supplier = Supplier.query.get_or_404(id)

    if request.method == 'POST':
        old_values = {
            'name': supplier.name,
            'phone': supplier.phone,
            'email': supplier.email
        }

        supplier.name = request.form.get('name')
        supplier.contact_person = request.form.get('contact_person')
        supplier.phone = request.form.get('phone')
        supplier.email = request.form.get('email')
        supplier.address = request.form.get('address')

        db.session.commit()

        new_values = {
            'name': supplier.name,
            'phone': supplier.phone,
            'email': supplier.email
        }

        # Log audit
        log_audit(session['user_id'], 'تعديل مورد', 'suppliers', supplier.id,
                 old_values=old_values, new_values=new_values)

        flash('تم تحديث بيانات المورد بنجاح', 'success')
        return redirect(url_for('suppliers.index'))

    return render_template('suppliers/edit.html', supplier=supplier)

@suppliers_bp.route('/delete/<int:id>')
@login_required
@permission_required('all')
def delete(id):
    supplier = Supplier.query.get_or_404(id)
    supplier.is_active = False
    db.session.commit()

    # Log audit
    log_audit(session['user_id'], 'حذف مورد', 'suppliers', supplier.id,
             old_values={'name': supplier.name, 'is_active': True})

    flash('تم حذف المورد بنجاح', 'success')
    return redirect(url_for('suppliers.index'))

@suppliers_bp.route('/view/<int:id>')
@login_required
@permission_required('all')
def view(id):
    supplier = Supplier.query.get_or_404(id)

    # Get supplier purchases
    purchases = Purchase.query.filter_by(supplier_id=id).order_by(Purchase.purchase_date.desc()).limit(20).all()

    # Get supplier payments
    payments = Payment.query.filter_by(supplier_id=id).order_by(Payment.payment_date.desc()).limit(20).all()

    # Calculate totals
    total_purchases = db.session.query(func.sum(Purchase.total_amount)).filter_by(supplier_id=id).scalar() or 0
    total_payments = db.session.query(func.sum(Payment.amount)).filter(
        Payment.supplier_id == id,
        Payment.payment_type == 'payment_to_supplier'
    ).scalar() or 0

    return render_template('suppliers/view.html',
                         supplier=supplier,
                         purchases=purchases,
                         payments=payments,
                         total_purchases=total_purchases,
                         total_payments=total_payments)

@suppliers_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])

    suppliers = Supplier.query.filter(
        Supplier.is_active == True,
        Supplier.name.contains(query)
    ).limit(10).all()

    results = []
    for supplier in suppliers:
        results.append({
            'id': supplier.id,
            'name': supplier.name,
            'phone': supplier.phone,
            'balance': supplier.balance
        })

    return jsonify(results)

@suppliers_bp.route('/statement/<int:id>')
@login_required
@permission_required('all')
def statement(id):
    supplier = Supplier.query.get_or_404(id)

    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    # Build query for transactions
    transactions = []

    # Get purchases
    purchases_query = Purchase.query.filter_by(supplier_id=id)
    if from_date:
        purchases_query = purchases_query.filter(Purchase.purchase_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        purchases_query = purchases_query.filter(Purchase.purchase_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    purchases = purchases_query.order_by(Purchase.purchase_date).all()

    for purchase in purchases:
        transactions.append({
            'date': purchase.purchase_date,
            'type': 'purchase',
            'description': f'فاتورة شراء رقم {purchase.invoice_number}',
            'debit': purchase.total_amount,
            'credit': 0,
            'balance': 0  # Will be calculated
        })

    # Get payments
    payments_query = Payment.query.filter(
        Payment.supplier_id == id,
        Payment.payment_type == 'payment_to_supplier'
    )
    if from_date:
        payments_query = payments_query.filter(Payment.payment_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        payments_query = payments_query.filter(Payment.payment_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    payments = payments_query.order_by(Payment.payment_date).all()

    for payment in payments:
        transactions.append({
            'date': payment.payment_date,
            'type': 'payment',
            'description': f'دفعة رقم {payment.payment_number}',
            'debit': 0,
            'credit': payment.amount,
            'balance': 0  # Will be calculated
        })

    # Sort transactions by date
    transactions.sort(key=lambda x: x['date'])

    # Calculate running balance
    balance = 0
    for transaction in transactions:
        balance += transaction['debit'] - transaction['credit']
        transaction['balance'] = balance

    return render_template('suppliers/statement.html',
                         supplier=supplier,
                         transactions=transactions,
                         from_date=from_date,
                         to_date=to_date)

@suppliers_bp.route('/export')
@login_required
@permission_required('all')
def export():
    suppliers = Supplier.query.filter_by(is_active=True).all()

    data = []
    for supplier in suppliers:
        data.append({
            'الرقم': supplier.id,
            'الاسم': supplier.name,
            'الشخص المسؤول': supplier.contact_person,
            'الهاتف': supplier.phone,
            'البريد الإلكتروني': supplier.email,
            'العنوان': supplier.address,
            'الرصيد': supplier.balance,
            'تاريخ الإضافة': supplier.created_at.strftime('%Y-%m-%d')
        })

    filename = f"suppliers_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    filepath = export_to_excel(data, filename, "الموردين")

    if filepath:
        flash('تم تصدير البيانات بنجاح', 'success')
    else:
        flash('حدث خطأ في تصدير البيانات', 'error')

    return redirect(url_for('suppliers.index'))

@suppliers_bp.route('/import-excel', methods=['GET', 'POST'])
@login_required
@permission_required('suppliers')
def import_excel():
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('لم يتم اختيار ملف', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'error')
            return redirect(request.url)

        if file and file.filename.lower().endswith(('.xlsx', '.xls')):
            try:
                # Read Excel file
                df = pd.read_excel(file)

                # Check if required columns exist
                if 'الاسم' not in df.columns:
                    flash('الملف يجب أن يحتوي على عمود "الاسم" على الأقل', 'error')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if supplier already exists
                        existing_supplier = Supplier.query.filter_by(name=row['الاسم']).first()
                        if existing_supplier:
                            error_count += 1
                            errors.append(f'السطر {index + 2}: المورد "{row["الاسم"]}" موجود مسبقاً')
                            continue

                        # Create new supplier
                        supplier = Supplier(
                            name=row['الاسم'],
                            contact_person=row.get('الشخص المسؤول', ''),
                            phone=row.get('الهاتف', ''),
                            email=row.get('البريد الإلكتروني', ''),
                            address=row.get('العنوان', ''),
                            balance=0.0
                        )

                        db.session.add(supplier)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'السطر {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()

                    # Log audit
                    log_audit(session['user_id'], 'استيراد موردين من Excel', 'suppliers', None,
                             new_values={'imported_count': success_count})

                # Show results
                if success_count > 0:
                    flash(f'تم استيراد {success_count} مورد بنجاح', 'success')
                if error_count > 0:
                    flash(f'فشل في استيراد {error_count} مورد', 'warning')
                    for error in errors[:5]:  # Show first 5 errors
                        flash(error, 'error')

                return redirect(url_for('suppliers.index'))

            except Exception as e:
                flash(f'خطأ في قراءة الملف: {str(e)}', 'error')
                return redirect(request.url)
        else:
            flash('يرجى اختيار ملف Excel (.xlsx أو .xls)', 'error')
            return redirect(request.url)

    return render_template('suppliers/import_excel.html')

@suppliers_bp.route('/download-template')
@login_required
@permission_required('suppliers')
def download_template():
    """Download Excel template for supplier import"""
    try:
        from utils import create_import_template
        import os

        headers = [
            'الاسم*', 'الشخص المسؤول', 'الهاتف', 'البريد الإلكتروني', 'العنوان'
        ]

        sample_data = [
            {
                'الاسم*': 'شركة قطع الغيار المتقدمة',
                'الشخص المسؤول': 'أحمد محمد علي',
                'الهاتف': '0501234567',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'الرياض، المملكة العربية السعودية'
            },
            {
                'الاسم*': 'مؤسسة الأجزاء الأصلية',
                'الشخص المسؤول': 'سارة أحمد محمد',
                'الهاتف': '0112345678',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'جدة، المملكة العربية السعودية'
            }
        ]

        filename = "suppliers_import_template"
        filepath = create_import_template(headers, sample_data, filename, "قالب استيراد الموردين")

        if filepath and os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في إنشاء القالب', 'error')
            return redirect(url_for('suppliers.index'))

    except Exception as e:
        flash(f'حدث خطأ في إنشاء القالب: {str(e)}', 'error')
        return redirect(url_for('suppliers.index'))