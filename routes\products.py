# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from models import db, Product, Category
from utils import login_required, permission_required, generate_barcode, log_audit, export_to_excel
from datetime import datetime
import os

products_bp = Blueprint('products', __name__)

@products_bp.route('/')
@login_required
@permission_required('all')
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', type=int)

    query = Product.query.filter_by(is_active=True)

    if search:
        query = query.filter(Product.name.contains(search))

    if category_id:
        query = query.filter_by(category_id=category_id)

    products = query.order_by(Product.name).paginate(
        page=page, per_page=20, error_out=False
    )

    categories = Category.query.filter_by(is_active=True).all()

    return render_template('products/index.html',
                         products=products,
                         categories=categories,
                         search=search,
                         category_id=category_id)

@products_bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def add():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        category_id = request.form.get('category_id', type=int)
        cost_price = request.form.get('cost_price', type=float)
        selling_price = request.form.get('selling_price', type=float)
        quantity = request.form.get('quantity', type=int)
        min_quantity = request.form.get('min_quantity', type=int)
        unit = request.form.get('unit')
        location = request.form.get('location')

        if not all([name, category_id, selling_price]):
            flash('يرجى ملء الحقول المطلوبة', 'error')
            return render_template('products/add.html', categories=Category.query.filter_by(is_active=True).all())

        try:
            # Generate temporary barcode first
            temp_barcode = f"TEMP{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Create product with temporary barcode
            product = Product(
                name=name,
                description=description,
                barcode=temp_barcode,  # Temporary barcode
                category_id=category_id,
                cost_price=cost_price or 0,
                selling_price=selling_price,
                quantity=quantity or 0,
                min_quantity=min_quantity or 5,
                unit=unit or 'قطعة',
                location=location
            )

            db.session.add(product)
            db.session.flush()  # Get the ID

            # Generate proper barcode
            barcode_number, barcode_path = generate_barcode(product.id, product.name)
            if barcode_number:
                product.barcode = barcode_number
            else:
                # If barcode generation fails, use a simple format
                product.barcode = f"AP{product.id:06d}"

            db.session.commit()

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في إضافة المنتج: {str(e)}', 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('products/add.html', categories=categories)

        # Log audit
        log_audit(session['user_id'], 'إضافة منتج', 'products', product.id,
                 new_values={'name': name, 'barcode': product.barcode})

        flash('تم إضافة المنتج بنجاح', 'success')
        return redirect(url_for('products.index'))

    categories = Category.query.filter_by(is_active=True).all()
    return render_template('products/add.html', categories=categories)

@products_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def edit(id):
    product = Product.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Validate required fields
            name = request.form.get('name')
            selling_price = request.form.get('selling_price', type=float)

            if not name:
                flash('اسم المنتج مطلوب', 'error')
                categories = Category.query.filter_by(is_active=True).all()
                return render_template('products/edit.html', product=product, categories=categories)

            if not selling_price or selling_price <= 0:
                flash('سعر البيع مطلوب ويجب أن يكون أكبر من صفر', 'error')
                categories = Category.query.filter_by(is_active=True).all()
                return render_template('products/edit.html', product=product, categories=categories)

            old_values = {
                'name': product.name,
                'selling_price': product.selling_price,
                'quantity': product.quantity
            }

            product.name = name
            product.description = request.form.get('description')
            product.category_id = request.form.get('category_id', type=int)
            product.cost_price = request.form.get('cost_price', type=float) or 0
            product.selling_price = selling_price
            product.quantity = request.form.get('quantity', type=int) or 0
            product.min_quantity = request.form.get('min_quantity', type=int) or 5
            product.unit = request.form.get('unit') or 'قطعة'
            product.location = request.form.get('location')

            db.session.commit()

            new_values = {
                'name': product.name,
                'selling_price': product.selling_price,
                'quantity': product.quantity
            }

            # Log audit
            log_audit(session['user_id'], 'تعديل منتج', 'products', product.id,
                     old_values=old_values, new_values=new_values)

            flash('تم تحديث المنتج بنجاح', 'success')
            return redirect(url_for('products.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تحديث المنتج: {str(e)}', 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('products/edit.html', product=product, categories=categories)

    categories = Category.query.filter_by(is_active=True).all()
    return render_template('products/edit.html', product=product, categories=categories)

@products_bp.route('/delete/<int:id>')
@login_required
@permission_required('all')
def delete(id):
    product = Product.query.get_or_404(id)
    product.is_active = False
    db.session.commit()

    # Log audit
    log_audit(session['user_id'], 'حذف منتج', 'products', product.id,
             old_values={'name': product.name, 'is_active': True})

    flash('تم حذف المنتج بنجاح', 'success')
    return redirect(url_for('products.index'))

@products_bp.route('/categories')
@login_required
@permission_required('all')
def categories():
    categories = Category.query.filter_by(is_active=True).all()
    return render_template('products/categories.html', categories=categories)

@products_bp.route('/categories/add', methods=['POST'])
@login_required
@permission_required('all')
def add_category():
    name = request.form.get('name')
    description = request.form.get('description')
    parent_id = request.form.get('parent_id', type=int)

    if not name:
        flash('يرجى إدخال اسم التصنيف', 'error')
        return redirect(url_for('products.categories'))

    category = Category(
        name=name,
        description=description,
        parent_id=parent_id if parent_id else None
    )

    db.session.add(category)
    db.session.commit()

    # Log audit
    log_audit(session['user_id'], 'إضافة تصنيف', 'categories', category.id,
             new_values={'name': name})

    flash('تم إضافة التصنيف بنجاح', 'success')
    return redirect(url_for('products.categories'))

@products_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])

    products = Product.query.filter(
        Product.is_active == True,
        Product.name.contains(query)
    ).limit(10).all()

    results = []
    for product in products:
        results.append({
            'id': product.id,
            'name': product.name,
            'barcode': product.barcode,
            'price': product.selling_price,
            'quantity': product.quantity
        })

    return jsonify(results)

@products_bp.route('/barcode/<int:id>')
@login_required
def barcode(id):
    product = Product.query.get_or_404(id)
    return render_template('products/barcode.html', product=product)

@products_bp.route('/print-barcode/<int:id>')
@login_required
def print_barcode(id):
    """طباعة الباركود بعدد محدد"""
    product = Product.query.get_or_404(id)
    count = request.args.get('count', 1, type=int)

    # Limit count to reasonable number
    if count > 100:
        count = 100
    elif count < 1:
        count = 1

    return render_template('products/print_barcode.html', product=product, count=count)

@products_bp.route('/export')
@login_required
@permission_required('all')
def export():
    try:
        products = Product.query.filter_by(is_active=True).all()

        if not products:
            flash('لا توجد منتجات للتصدير', 'warning')
            return redirect(url_for('products.index'))

        data = []
        for product in products:
            data.append({
                'الرقم': product.id,
                'الاسم': product.name,
                'الوصف': product.description or '',
                'التصنيف': product.category.name if product.category else 'غير محدد',
                'الباركود': product.barcode,
                'سعر التكلفة': product.cost_price,
                'سعر البيع': product.selling_price,
                'الكمية': product.quantity,
                'الحد الأدنى': product.min_quantity,
                'الوحدة': product.unit,
                'الموقع': product.location or '',
                'تاريخ الإنشاء': product.created_at.strftime('%Y-%m-%d') if product.created_at else '',
                'الحالة': 'نشط' if product.is_active else 'غير نشط',
                'قيمة المخزون': product.quantity * product.cost_price
            })

        filename = f"products_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        filepath = export_to_excel(data, filename, "المنتجات")

        if filepath and os.path.exists(filepath):
            # Log the export action
            log_audit(session['user_id'], 'تصدير المنتجات', 'products', None,
                     new_values={'count': len(products), 'filename': filename})

            return send_file(filepath, as_attachment=True,
                           download_name=f"{filename}.xlsx",
                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        else:
            flash('حدث خطأ في إنشاء ملف التصدير', 'error')
            return redirect(url_for('products.index'))

    except Exception as e:
        flash(f'حدث خطأ في تصدير البيانات: {str(e)}', 'error')
        return redirect(url_for('products.index'))

@products_bp.route('/download-template')
@login_required
@permission_required('products')
def download_template():
    """تحميل قالب استيراد المنتجات"""
    from utils import create_import_template

    headers = [
        'الاسم*', 'الباركود*', 'الفئة', 'الوحدة*',
        'الكمية*', 'سعر التكلفة*', 'سعر البيع*',
        'الحد الأدنى*', 'الوصف', 'الموقع'
    ]

    sample_data = [
        {
            'الاسم*': 'فلتر زيت تويوتا',
            'الباركود*': '1234567890123',
            'الفئة': 'فلاتر',
            'الوحدة*': 'قطعة',
            'الكمية*': 50,
            'سعر التكلفة*': 25.00,
            'سعر البيع*': 35.00,
            'الحد الأدنى*': 10,
            'الوصف': 'فلتر زيت أصلي لسيارات تويوتا',
            'الموقع': 'A1-01'
        },
        {
            'الاسم*': 'إطار ميشلان 195/65R15',
            'الباركود*': '9876543210987',
            'الفئة': 'إطارات',
            'الوحدة*': 'قطعة',
            'الكمية*': 20,
            'سعر التكلفة*': 300.00,
            'سعر البيع*': 450.00,
            'الحد الأدنى*': 5,
            'الوصف': 'إطار ميشلان عالي الجودة',
            'الموقع': 'B2-05'
        }
    ]

    filename = "products_import_template"
    filepath = create_import_template(headers, sample_data, filename, "قالب استيراد المنتجات")

    if filepath:
        return send_file(filepath, as_attachment=True)
    else:
        flash('حدث خطأ في إنشاء القالب', 'error')
        return redirect(url_for('products.index'))

@products_bp.route('/import-excel', methods=['GET', 'POST'])
@login_required
@permission_required('products')
def import_excel():
    """استيراد المنتجات من ملف Excel"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('لم يتم اختيار ملف', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'error')
            return redirect(request.url)

        if file and file.filename.lower().endswith(('.xlsx', '.xls')):
            try:
                import pandas as pd
                from models import Category

                # قراءة الملف
                df = pd.read_excel(file)

                # التحقق من وجود الأعمدة المطلوبة
                required_columns = ['الاسم*', 'الباركود*', 'الوحدة*', 'الكمية*', 'سعر التكلفة*', 'سعر البيع*', 'الحد الأدنى*']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'الأعمدة التالية مفقودة: {", ".join(missing_columns)}', 'error')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # التحقق من البيانات المطلوبة
                        if pd.isna(row['الاسم*']) or pd.isna(row['الباركود*']):
                            errors.append(f'الصف {index + 2}: الاسم والباركود مطلوبان')
                            error_count += 1
                            continue

                        # التحقق من عدم وجود باركود مكرر
                        existing_product = Product.query.filter_by(barcode=str(row['الباركود*'])).first()
                        if existing_product:
                            errors.append(f'الصف {index + 2}: الباركود {row["الباركود*"]} موجود مسبقاً')
                            error_count += 1
                            continue

                        # البحث عن الفئة أو إنشاؤها
                        category = None
                        if not pd.isna(row.get('الفئة', '')):
                            category = Category.query.filter_by(name=str(row['الفئة'])).first()
                            if not category:
                                category = Category(name=str(row['الفئة']))
                                db.session.add(category)
                                db.session.flush()

                        # إنشاء المنتج الجديد
                        product = Product(
                            name=str(row['الاسم*']),
                            barcode=str(row['الباركود*']),
                            category_id=category.id if category else None,
                            unit=str(row['الوحدة*']) if not pd.isna(row['الوحدة*']) else 'قطعة',
                            quantity=float(row['الكمية*']) if not pd.isna(row['الكمية*']) else 0,
                            cost_price=float(row['سعر التكلفة*']) if not pd.isna(row['سعر التكلفة*']) else 0,
                            selling_price=float(row['سعر البيع*']) if not pd.isna(row['سعر البيع*']) else 0,
                            min_quantity=float(row['الحد الأدنى*']) if not pd.isna(row['الحد الأدنى*']) else 0,
                            description=str(row['الوصف']) if not pd.isna(row.get('الوصف', '')) else None,
                            location=str(row['الموقع']) if not pd.isna(row.get('الموقع', '')) else None,
                            is_active=True
                        )

                        db.session.add(product)
                        success_count += 1

                    except Exception as e:
                        errors.append(f'الصف {index + 2}: {str(e)}')
                        error_count += 1

                if success_count > 0:
                    db.session.commit()
                    flash(f'تم استيراد {success_count} منتج بنجاح', 'success')
                else:
                    db.session.rollback()

                if error_count > 0:
                    flash(f'فشل في استيراد {error_count} منتج', 'warning')
                    for error in errors[:10]:  # عرض أول 10 أخطاء فقط
                        flash(error, 'error')

                return redirect(url_for('products.index'))

            except Exception as e:
                flash(f'حدث خطأ في قراءة الملف: {str(e)}', 'error')
                return redirect(request.url)
        else:
            flash('يجب أن يكون الملف من نوع Excel (.xlsx أو .xls)', 'error')
            return redirect(request.url)

    return render_template('products/import_excel.html')

@products_bp.route('/add-category-new', methods=['POST'])
@login_required
@permission_required('products')
def add_category_new():
    """إضافة فئة جديدة"""
    name = request.form.get('name')
    description = request.form.get('description')

    if not name:
        flash('اسم الفئة مطلوب', 'error')
        return redirect(url_for('products.categories'))

    # التحقق من عدم وجود فئة بنفس الاسم
    existing_category = Category.query.filter_by(name=name).first()
    if existing_category:
        flash('يوجد فئة بنفس هذا الاسم مسبقاً', 'error')
        return redirect(url_for('products.categories'))

    try:
        category = Category(name=name, description=description)
        db.session.add(category)
        db.session.commit()

        log_audit('إضافة فئة', f'تم إضافة فئة جديدة: {name}')
        flash('تم إضافة الفئة بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إضافة الفئة: {str(e)}', 'error')

    return redirect(url_for('products.categories'))

@products_bp.route('/edit-category/<int:category_id>', methods=['POST'])
@login_required
@permission_required('products')
def edit_category(category_id):
    """تعديل فئة"""
    category = Category.query.get_or_404(category_id)

    name = request.form.get('name')
    description = request.form.get('description')

    if not name:
        flash('اسم الفئة مطلوب', 'error')
        return redirect(url_for('products.categories'))

    # التحقق من عدم وجود فئة أخرى بنفس الاسم
    existing_category = Category.query.filter(Category.name == name, Category.id != category_id).first()
    if existing_category:
        flash('يوجد فئة أخرى بنفس هذا الاسم', 'error')
        return redirect(url_for('products.categories'))

    try:
        old_name = category.name
        category.name = name
        category.description = description
        db.session.commit()

        log_audit('تعديل فئة', f'تم تعديل الفئة من {old_name} إلى {name}')
        flash('تم تحديث الفئة بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في تحديث الفئة: {str(e)}', 'error')

    return redirect(url_for('products.categories'))

@products_bp.route('/delete-category/<int:category_id>', methods=['POST'])
@login_required
@permission_required('products')
def delete_category(category_id):
    """حذف فئة"""
    category = Category.query.get_or_404(category_id)

    # التحقق من عدم وجود منتجات في هذه الفئة
    if category.products:
        flash('لا يمكن حذف فئة تحتوي على منتجات', 'error')
        return redirect(url_for('products.categories'))

    try:
        category_name = category.name
        db.session.delete(category)
        db.session.commit()

        log_audit('حذف فئة', f'تم حذف الفئة: {category_name}')
        flash('تم حذف الفئة بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في حذف الفئة: {str(e)}', 'error')

    return redirect(url_for('products.categories'))