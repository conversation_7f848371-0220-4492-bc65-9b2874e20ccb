# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, session, redirect, url_for
from models import db, Product, Customer, Supplier, Sale, Purchase, CashTransaction
from utils import login_required, get_dashboard_stats, get_low_stock_products
from datetime import datetime, timedelta
from sqlalchemy import func

main_bp = Blueprint('main', __name__)

@main_bp.route('/dashboard')
@login_required
def dashboard():
    # Get basic statistics
    stats = get_dashboard_stats()

    # Get low stock products
    low_stock_products = get_low_stock_products()

    # Get recent sales (last 7 days)
    week_ago = datetime.now() - timedelta(days=7)
    recent_sales = Sale.query.filter(Sale.sale_date >= week_ago).order_by(Sale.sale_date.desc()).limit(10).all()

    # Get cash balance
    cash_in = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type.in_(['sale', 'collection', 'deposit'])
    ).scalar() or 0

    cash_out = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type.in_(['purchase', 'expense', 'payment', 'withdrawal'])
    ).scalar() or 0

    cash_balance = cash_in - cash_out

    # Get monthly sales and expenses
    current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    # Monthly sales
    monthly_sales_amount = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.sale_date >= current_month
    ).scalar() or 0

    # Monthly expenses (from cash transactions)
    monthly_expenses_amount = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type == 'expense',
        CashTransaction.transaction_date >= current_month
    ).scalar() or 0

    # Get monthly sales chart data
    monthly_sales = []
    for i in range(12):
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(days=30*i)
        month_end = month_start + timedelta(days=30)

        sales_amount = db.session.query(func.sum(Sale.total_amount)).filter(
            Sale.sale_date >= month_start,
            Sale.sale_date < month_end
        ).scalar() or 0

        monthly_sales.append({
            'month': month_start.strftime('%Y-%m'),
            'amount': float(sales_amount)
        })

    monthly_sales.reverse()

    # Get top selling products
    top_products = db.session.query(
        Product.name,
        func.sum(Sale.total_amount).label('total_sales')
    ).join(
        Sale, Product.id == Sale.id  # This needs to be fixed with proper join
    ).group_by(Product.id).order_by(func.sum(Sale.total_amount).desc()).limit(5).all()

    return render_template('dashboard.html',
                         stats=stats,
                         low_stock_products=low_stock_products,
                         recent_sales=recent_sales,
                         cash_balance=cash_balance,
                         monthly_sales=monthly_sales,
                         monthly_sales_amount=monthly_sales_amount,
                         monthly_expenses=monthly_expenses_amount,
                         top_products=top_products)

@main_bp.route('/profile')
@login_required
def profile():
    from models import User
    user = User.query.get(session['user_id'])
    return render_template('profile.html', user=user)
