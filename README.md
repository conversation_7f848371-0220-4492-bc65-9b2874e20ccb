# 🚗 نظام إدارة قطع الغيار المتكامل - AutoParts Manager V5.1

نظام شامل ومتكامل لإدارة قطع الغيار يعمل محلياً (Offline) بدون الحاجة للإنترنت، مطور باستخدام Python Flask مع دعم كامل للغة العربية واتجاه RTL.

## ✨ المميزات الرئيسية

### 🔐 نظام المستخدمين والصلاحيات
- **مدير**: صلاحيات كاملة على جميع أجزاء النظام
- **محاسب**: صلاحيات الحسابات، المدفوعات، التحصيلات، التقارير فقط
- **كاشير**: صلاحية البيع من شاشة نقطة البيع فقط
- شاشة تسجيل دخول آمنة حسب نوع المستخدم

### 📦 إدارة المنتجات والمخازن
- إدارة الأصناف الرئيسية والفرعية
- إضافة/تعديل/حذف المنتجات
- توليد باركود تلقائي لكل منتج
- تتبع الكميات والمخزون
- تنبيهات عند انخفاض الكمية (حد إعادة الطلب)
- طباعة باركود المنتجات
- استيراد المنتجات من ملف Excel

### 👥 إدارة العملاء والموردين
- إدارة بيانات العملاء والموردين (اسم، رقم، عنوان، رصيد)
- سجل تعاملات مفصل لكل عميل/مورد
- استيراد بيانات العملاء والموردين من Excel
- فلترة حسب الاسم أو الرصيد أو التاريخ
- كشف حساب زمني لكل عميل ومورد

### 💳 نقطة البيع (POS)
- شاشة بيع سهلة وسريعة مخصصة للكاشير
- إدخال المنتجات بالباركود أو البحث اليدوي
- اختيار نوع العميل (نقدي / مسجل)
- حساب تلقائي للإجمالي، الضريبة، الخصم
- طباعة الفاتورة أو حفظها كـ PDF
- تحديث المخزون تلقائياً بعد البيع

### 💰 النظام المحاسبي المتكامل

#### 🔁 الحسابات العامة (General Ledger)
- لوحة تحكم مالية شاملة
- إجمالي المبيعات، المشتريات، المصروفات
- الربح/الخسارة خلال فترة معينة
- التدفقات النقدية
- دليل حسابات (Chart of Accounts) ديناميكي

#### 📊 القيود اليومية (Journal Entries)
- تسجيل يدوي وتلقائي للقيود المحاسبية
- قيود تلقائية لعمليات البيع، الشراء، الدفع، التحصيل

#### 📘 دفتر الأستاذ العام
- حركة كل حساب بشكل مفصل
- فلترة بالحساب أو التاريخ
- رصيد ختامي دقيق

#### 📈 ميزان المراجعة (Trial Balance)
- عرض الأرصدة المدينة والدائنة
- مقارنة بين الحسابات حسب الفترة

#### 📑 القوائم المالية
- قائمة الدخل (الأرباح والخسائر)
- الميزانية العمومية
- التدفقات النقدية

### 💳 المدفوعات والتحصيلات
- تسجيل دفعات الموردين (نقدي/بنكي) مع إيصال دفع
- تحصيلات العملاء مع إيصال استلام
- ربط الدفعات بالفواتير
- سجل كامل حسب التاريخ أو العميل/المورد

### 📚 إدارة الخزنة (Cashbox)
- سجل كامل لحركات الصندوق
- عرض الرصيد اليومي
- طباعة كشف مفصل

### 📂 إدارة المصروفات
- تصنيف أنواع المصروفات
- تسجيل التفاصيل (القيمة – التاريخ – ملاحظات)
- رفع مرفقات
- تقارير حسب النوع والفترة

### 📊 التقارير المتقدمة
- تقرير المبيعات التفصيلي
- تقرير المخزون والمنتجات
- تقرير الأرباح والخسائر
- تقرير التدفق النقدي
- تصدير جميع التقارير إلى Excel أو PDF

### 💾 النسخ الاحتياطي والاستعادة
- إنشاء نسخة احتياطية تلقائياً باسم وتاريخ
- استعادة نسخة احتياطية بسهولة
- دعم النقل الخارجي (USB/Drive)

### 🔒 الأمان والتدقيق
- سجل تدقيق لكل عملية (Audit Log)
- منع تعديل القيود بعد اعتمادها (اختياري)
- صلاحيات منفصلة للمحاسبين

## 🛠️ التقنيات المستخدمة

- **Backend**: Python 3.8+ مع Flask Framework
- **Database**: SQLite (قاعدة بيانات محلية)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5 RTL
- **UI Framework**: Bootstrap 5 مع دعم كامل للعربية
- **Charts**: Chart.js للرسوم البيانية
- **PDF Generation**: ReportLab
- **Excel Export**: OpenPyXL, Pandas
- **Barcode**: Python-Barcode
- **Date Conversion**: Hijri-Converter (التاريخ الهجري)

## 📋 متطلبات النظام

- **نظام التشغيل**: Windows 7/8/10/11
- **الذاكرة**: 2 جيجابايت RAM على الأقل
- **مساحة القرص**: 500 ميجابايت
- **متصفح ويب**: Chrome, Firefox, Edge (أي متصفح حديث)
- **Python**: 3.8 أو أحدث (للتطوير فقط)

## 🚀 التثبيت والتشغيل

### الطريقة الأولى: التشغيل السريع

1. **تثبيت المتطلبات:**
   ```bash
   python install_simple.py
   ```

2. **تشغيل النظام:**
   ```bash
   python run_simple.py
   ```

3. **فتح المتصفح:**
   اذهب إلى: `http://127.0.0.1:8888`

### الطريقة الثانية: استخدام ملفات Batch (Windows)

1. **تثبيت المتطلبات:**
   انقر نقراً مزدوجاً على `install_requirements.bat`

2. **تشغيل النظام:**
   انقر نقراً مزدوجاً على `start.bat`

### الطريقة الثالثة: من الكود المصدري (للمطورين)

```bash
# 1. تثبيت المتطلبات الأساسية
pip install Flask Flask-SQLAlchemy Flask-Login

# 2. تشغيل النظام
python app.py

# 3. فتح المتصفح
# اذهب إلى: http://127.0.0.1:8888
```

## 👤 بيانات الدخول الافتراضية

### المدير (صلاحيات كاملة)
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### المحاسب (الحسابات والتقارير)
- **اسم المستخدم**: `accountant`
- **كلمة المرور**: `acc123`

### الكاشير (نقطة البيع فقط)
- **اسم المستخدم**: `cashier`
- **كلمة المرور**: `cash123`

## 🔧 بناء الملف التنفيذي

لإنشاء ملف تنفيذي من الكود المصدري:

```bash
# تثبيت PyInstaller
pip install pyinstaller

# تشغيل سكريبت البناء
python build_exe.py
```

سيتم إنشاء مجلد `AutoPartsManager_Distribution` يحتوي على:
- الملف التنفيذي
- قاعدة البيانات
- ملفات التشغيل
- دليل الاستخدام

## 📁 هيكل المشروع

```
AutoParts Manager V5.1/
├── app.py                 # الملف الرئيسي
├── models.py             # نماذج قاعدة البيانات
├── auth.py               # نظام المصادقة
├── utils.py              # الوظائف المساعدة
├── requirements.txt      # متطلبات Python
├── build_exe.py         # سكريبت بناء الملف التنفيذي
├── routes/              # ملفات الطرق
│   ├── main.py          # الصفحة الرئيسية
│   ├── products.py      # إدارة المنتجات
│   ├── customers.py     # إدارة العملاء
│   ├── suppliers.py     # إدارة الموردين
│   ├── pos.py           # نقطة البيع
│   ├── accounting.py    # النظام المحاسبي
│   ├── reports.py       # التقارير
│   └── settings.py      # الإعدادات
├── templates/           # قوالب HTML
│   ├── base.html        # القالب الأساسي
│   ├── dashboard.html   # لوحة التحكم
│   ├── auth/           # صفحات المصادقة
│   ├── pos/            # صفحات نقطة البيع
│   ├── products/       # صفحات المنتجات
│   ├── customers/      # صفحات العملاء
│   ├── suppliers/      # صفحات الموردين
│   ├── accounting/     # صفحات المحاسبة
│   ├── reports/        # صفحات التقارير
│   └── settings/       # صفحات الإعدادات
├── static/             # الملفات الثابتة
│   ├── css/           # ملفات CSS
│   ├── js/            # ملفات JavaScript
│   ├── images/        # الصور
│   ├── uploads/       # الملفات المرفوعة
│   ├── exports/       # ملفات التصدير
│   ├── reports/       # التقارير المولدة
│   └── barcodes/      # صور الباركود
├── backups/           # النسخ الاحتياطية
└── autoparts.db       # قاعدة البيانات
```

## 🔄 التحديثات والصيانة

### النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية يومياً
- استخدم ميزة النسخ الاحتياطي المدمجة في النظام
- احفظ النسخ في مكان آمن خارج جهاز الكمبيوتر

### التحديثات
- تحقق من التحديثات دورياً
- احفظ نسخة احتياطية قبل أي تحديث
- اتبع تعليمات التحديث المرفقة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**النظام لا يعمل:**
- تأكد من وجود جميع الملفات
- شغل البرنامج كمدير (Run as Administrator)
- تحقق من أن المنفذ 5000 غير مستخدم

**بطء في الأداء:**
- أغلق البرامج غير الضرورية
- تأكد من وجود مساحة كافية على القرص
- اعمل نسخة احتياطية وأعد تشغيل النظام

**مشاكل في عرض البيانات:**
- امسح ذاكرة التخزين المؤقت للمتصفح
- جرب متصفحاً آخر
- تأكد من تفعيل JavaScript

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567
- **الموقع**: www.autoparts.com

## 📄 الترخيص

هذا النظام محمي بحقوق الطبع والنشر. جميع الحقوق محفوظة © 2024

## 🤝 المساهمة

نرحب بالمساهمات لتطوير النظام:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

---

**تم تطوير النظام بواسطة فريق AutoParts Manager**
**الإصدار**: 5.1
**تاريخ الإصدار**: 2024
**آخر تحديث**: ديسمبر 2024
