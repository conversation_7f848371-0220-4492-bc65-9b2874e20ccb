{% extends "base.html" %}

{% block title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </h3>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للإعدادات
                    </a>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.system_settings') }}">
                        <div class="row">
                            <!-- Company Information -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">معلومات الشركة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="company_name" class="form-label">اسم الشركة</label>
                                            <input type="text" class="form-control" id="company_name" name="company_name"
                                                   value="{{ settings.get('company_name', '') }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="company_address" class="form-label">عنوان الشركة</label>
                                            <textarea class="form-control" id="company_address" name="company_address" rows="3">{{ settings.get('company_address', '') }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="company_phone" class="form-label">هاتف الشركة</label>
                                            <input type="text" class="form-control" id="company_phone" name="company_phone"
                                                   value="{{ settings.get('company_phone', '') }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="company_email" class="form-label">بريد الشركة الإلكتروني</label>
                                            <input type="email" class="form-control" id="company_email" name="company_email"
                                                   value="{{ settings.get('company_email', '') }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" id="tax_number" name="tax_number"
                                                   value="{{ settings.get('tax_number', '') }}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Settings -->
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">إعدادات النظام</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="currency" class="form-label">العملة</label>
                                            <select class="form-select" id="currency" name="currency">
                                                <option value="SAR" {{ 'selected' if settings.get('currency') == 'SAR' }}>ريال سعودي (SAR)</option>
                                                <option value="USD" {{ 'selected' if settings.get('currency') == 'USD' }}>دولار أمريكي (USD)</option>
                                                <option value="EUR" {{ 'selected' if settings.get('currency') == 'EUR' }}>يورو (EUR)</option>
                                                <option value="AED" {{ 'selected' if settings.get('currency') == 'AED' }}>درهم إماراتي (AED)</option>
                                                <option value="EGP" {{ 'selected' if settings.get('currency') == 'EGP' }}>جنيه مصري (EGP)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                            <input type="number" class="form-control" id="tax_rate" name="tax_rate"
                                                   step="0.01" min="0" max="100" value="{{ settings.get('tax_rate', 15) }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="language" class="form-label">لغة النظام</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="ar" {{ 'selected' if settings.get('language') == 'ar' }}>العربية</option>
                                                <option value="en" {{ 'selected' if settings.get('language') == 'en' }}>English</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                            <select class="form-select" id="timezone" name="timezone">
                                                <option value="Asia/Riyadh" {{ 'selected' if settings.get('timezone') == 'Asia/Riyadh' }}>الرياض</option>
                                                <option value="Asia/Dubai" {{ 'selected' if settings.get('timezone') == 'Asia/Dubai' }}>دبي</option>
                                                <option value="Africa/Cairo" {{ 'selected' if settings.get('timezone') == 'Africa/Cairo' }}>القاهرة</option>
                                                <option value="UTC" {{ 'selected' if settings.get('timezone') == 'UTC' }}>UTC</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="enable_audit_log" name="enable_audit_log"
                                                   {{ 'checked' if settings.get('enable_audit_log', True) }}>
                                            <label class="form-check-label" for="enable_audit_log">
                                                تفعيل سجل المراجعة
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup"
                                                   {{ 'checked' if settings.get('auto_backup', False) }}>
                                            <label class="form-check-label" for="auto_backup">
                                                النسخ الاحتياطي التلقائي
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <!-- Invoice Settings -->
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">إعدادات الفواتير</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="invoice_prefix" class="form-label">بادئة رقم الفاتورة</label>
                                            <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix"
                                                   value="{{ settings.get('invoice_prefix', 'INV') }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="invoice_start_number" class="form-label">رقم البداية للفواتير</label>
                                            <input type="number" class="form-control" id="invoice_start_number" name="invoice_start_number"
                                                   min="1" value="{{ settings.get('invoice_start_number', 1) }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="invoice_footer" class="form-label">تذييل الفاتورة</label>
                                            <textarea class="form-control" id="invoice_footer" name="invoice_footer" rows="3">{{ settings.get('invoice_footer', '') }}</textarea>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="print_after_sale" name="print_after_sale"
                                                   {{ 'checked' if settings.get('print_after_sale', True) }}>
                                            <label class="form-check-label" for="print_after_sale">
                                                طباعة الفاتورة تلقائياً بعد البيع
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Inventory Settings -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">إعدادات المخزون</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="low_stock_threshold" class="form-label">حد التنبيه للمخزون المنخفض</label>
                                            <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold"
                                                   min="0" value="{{ settings.get('low_stock_threshold', 10) }}">
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory"
                                                   {{ 'checked' if settings.get('track_inventory', True) }}>
                                            <label class="form-check-label" for="track_inventory">
                                                تتبع المخزون
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="allow_negative_stock" name="allow_negative_stock"
                                                   {{ 'checked' if settings.get('allow_negative_stock', False) }}>
                                            <label class="form-check-label" for="allow_negative_stock">
                                                السماح بالمخزون السالب
                                            </label>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto_reorder" name="auto_reorder"
                                                   {{ 'checked' if settings.get('auto_reorder', False) }}>
                                            <label class="form-check-label" for="auto_reorder">
                                                إعادة الطلب التلقائي
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0">إعدادات الأمان</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="session_timeout" class="form-label">انتهاء الجلسة (دقيقة)</label>
                                                    <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                                           min="5" max="1440" value="{{ settings.get('session_timeout', 60) }}">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="max_login_attempts" class="form-label">محاولات تسجيل الدخول القصوى</label>
                                                    <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts"
                                                           min="3" max="10" value="{{ settings.get('max_login_attempts', 5) }}">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="password_min_length" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                                    <input type="number" class="form-control" id="password_min_length" name="password_min_length"
                                                           min="6" max="20" value="{{ settings.get('password_min_length', 8) }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="require_strong_password" name="require_strong_password"
                                                           {{ 'checked' if settings.get('require_strong_password', True) }}>
                                                    <label class="form-check-label" for="require_strong_password">
                                                        طلب كلمة مرور قوية
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input class="form-check-input" type="checkbox" id="force_password_change" name="force_password_change"
                                                           {{ 'checked' if settings.get('force_password_change', False) }}>
                                                    <label class="form-check-label" for="force_password_change">
                                                        إجبار تغيير كلمة المرور كل 90 يوم
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ الإعدادات
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetToDefaults()">
                                    <i class="fas fa-undo"></i> استعادة الافتراضي
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetToDefaults() {
    if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
        fetch('/settings/reset-to-defaults', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم استعادة الإعدادات الافتراضية بنجاح');
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في استعادة الإعدادات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في استعادة الإعدادات');
        });
    }
}
</script>
{% endblock %}
