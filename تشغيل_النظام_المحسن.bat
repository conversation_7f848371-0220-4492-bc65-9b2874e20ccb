@echo off
chcp 65001 > nul
title نظام إدارة قطع الغيار - AutoParts Manager V5.1
color 0A

echo ===============================================================
echo 🚀 نظام إدارة قطع الغيار - AutoParts Manager V5.1
echo ===============================================================
echo.
echo 🔍 فحص متطلبات النظام...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
    echo 📥 يمكنك تحميله من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM Check if virtual environment exists and use it
if exist "venv\Scripts\python.exe" (
    echo ✅ استخدام البيئة الافتراضية...
    set PYTHON_CMD=venv\Scripts\python.exe
    set PIP_CMD=venv\Scripts\pip.exe
) else (
    echo ⚠️ البيئة الافتراضية غير موجودة، استخدام Python العام...
    set PYTHON_CMD=python
    set PIP_CMD=pip
)

REM Check if Flask is installed
%PYTHON_CMD% -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Flask غير مثبت، جاري التثبيت...
    %PIP_CMD% install flask flask-sqlalchemy flask-login pandas openpyxl
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المتطلبات
)

echo.
echo 🌐 النظام سيعمل على العناوين التالية:
echo    📍 المحلي: http://127.0.0.1:8888
echo    📍 الشبكة المحلية: http://[IP_ADDRESS]:8888
echo.
echo 💡 يمكن الوصول من أي جهاز في الشبكة المحلية
echo 🔐 المستخدم الافتراضي: admin ^| كلمة المرور: admin123
echo.
echo ⏳ جاري تشغيل النظام...
echo.

REM Start the application
%PYTHON_CMD% app.py

echo.
echo ⚠️ تم إيقاف النظام
pause
