# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from models import db, User
from utils import log_audit
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username, is_active=True).first()
        
        if user and user.check_password(password):
            # Update last login
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # Set session
            session['user_id'] = user.id
            session['username'] = user.username
            session['full_name'] = user.full_name
            session['role'] = user.role
            
            # Log audit
            log_audit(user.id, 'تسجيل دخول', 'users', user.id, ip_address=request.remote_addr)
            
            flash(f'مرحباً {user.full_name}', 'success')
            
            # Redirect based on role
            if user.role == 'cashier':
                return redirect(url_for('pos.index'))
            else:
                return redirect(url_for('main.dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
def logout():
    if 'user_id' in session:
        user_id = session['user_id']
        log_audit(user_id, 'تسجيل خروج', 'users', user_id, ip_address=request.remote_addr)
    
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/change-password', methods=['GET', 'POST'])
def change_password():
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if not all([current_password, new_password, confirm_password]):
            flash('يرجى ملء جميع الحقول', 'error')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('كلمة المرور الجديدة غير متطابقة', 'error')
            return render_template('auth/change_password.html')
        
        if len(new_password) < 6:
            flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
            return render_template('auth/change_password.html')
        
        user = User.query.get(session['user_id'])
        if not user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('auth/change_password.html')
        
        # Update password
        user.set_password(new_password)
        db.session.commit()
        
        # Log audit
        log_audit(user.id, 'تغيير كلمة المرور', 'users', user.id, ip_address=request.remote_addr)
        
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('main.dashboard'))
    
    return render_template('auth/change_password.html')

def init_default_users():
    """Initialize default users if none exist"""
    if User.query.count() == 0:
        # Create default admin user
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='manager'
        )
        admin.set_password('admin123')
        
        # Create default accountant
        accountant = User(
            username='accountant',
            email='<EMAIL>',
            full_name='المحاسب',
            role='accountant'
        )
        accountant.set_password('acc123')
        
        # Create default cashier
        cashier = User(
            username='cashier',
            email='<EMAIL>',
            full_name='الكاشير',
            role='cashier'
        )
        cashier.set_password('cash123')
        
        db.session.add_all([admin, accountant, cashier])
        db.session.commit()
        
        print("Default users created:")
        print("Admin: admin/admin123")
        print("Accountant: accountant/acc123")
        print("Cashier: cashier/cash123")
