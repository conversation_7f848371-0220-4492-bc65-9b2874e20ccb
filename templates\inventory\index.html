{% extends "base.html" %}

{% block title %}إدارة المخزن{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-warehouse me-2"></i>إدارة المخزن</h2>
                <div class="btn-group">
                    <a href="{{ url_for('inventory.add_adjustment') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>تعديل مخزون
                    </a>
                    <a href="{{ url_for('inventory.reports') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المنتجات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "{:,}".format(total_products) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                منتجات منخفضة المخزون
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                قيمة المخزون
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "{:,.2f}".format(total_value) }} ر.س
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col me-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                حركات اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ recent_transactions|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Transactions -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر حركات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>التاريخ</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>
                                        <strong>{{ transaction.product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ transaction.product.barcode }}</small>
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'in' %}
                                            <span class="badge bg-success">دخول</span>
                                        {% elif transaction.transaction_type == 'out' %}
                                            <span class="badge bg-danger">خروج</span>
                                        {% elif transaction.transaction_type == 'adjustment' %}
                                            <span class="badge bg-warning">تعديل</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ transaction.transaction_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'out' %}-{% endif %}{{ transaction.quantity }}
                                        <small class="text-muted">{{ transaction.product.unit }}</small>
                                    </td>
                                    <td>
                                        {{ transaction.transaction_date.strftime('%Y-%m-%d %H:%M') }}
                                    </td>
                                    <td>{{ transaction.user.full_name }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('inventory.transactions') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>عرض جميع الحركات
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد حركات مخزون حديثة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Low Stock Alerts -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات المخزون المنخفض
                    </h5>
                </div>
                <div class="card-body">
                    {% if low_stock_products %}
                    <div class="list-group list-group-flush">
                        {% for product in low_stock_products %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ product.name }}</strong>
                                <br>
                                <small class="text-muted">{{ product.barcode }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-danger">{{ product.quantity }}</span>
                                <br>
                                <small class="text-muted">الحد الأدنى: {{ product.min_quantity }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('inventory.low_stock') }}" class="btn btn-outline-warning">
                            <i class="fas fa-list me-2"></i>عرض جميع التنبيهات
                        </a>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-success">جميع المنتجات في المستوى الطبيعي</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('inventory.add_adjustment') }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>تعديل مخزون
                        </a>
                        <a href="{{ url_for('inventory.transactions') }}" class="btn btn-outline-primary">
                            <i class="fas fa-history me-2"></i>حركات المخزون
                        </a>
                        <a href="{{ url_for('inventory.adjustments') }}" class="btn btn-outline-warning">
                            <i class="fas fa-balance-scale me-2"></i>تعديلات المخزون
                        </a>
                        <a href="{{ url_for('inventory.reports') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>تقارير المخزون
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
