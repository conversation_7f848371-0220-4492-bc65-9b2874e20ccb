{% extends "base.html" %}

{% block title %}النظام المحاسبي - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calculator me-2"></i>
        النظام المحاسبي
    </h1>
</div>

<!-- Financial Overview -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_revenue|currency }}</h4>
                        <p class="mb-0">إجمالي الإيرادات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_expenses|currency }}</h4>
                        <p class="mb-0">إجمالي المصروفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line-down fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ net_profit|currency }}</h4>
                        <p class="mb-0">صافي الربح</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ cash_balance|currency }}</h4>
                        <p class="mb-0">رصيد الصندوق</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-wallet fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('accounting.chart_of_accounts') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list me-2"></i>
                            دليل الحسابات
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('accounting.journal_entries') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-book me-2"></i>
                            القيود اليومية
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('accounting.general_ledger') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-file-alt me-2"></i>
                            دفتر الأستاذ
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('accounting.trial_balance') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-balance-scale me-2"></i>
                            ميزان المراجعة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('reports.profit_loss') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-pie me-2"></i>
                            قائمة الدخل
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('reports.sales_report') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-file-invoice me-2"></i>
                            الميزانية العمومية
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('reports.cash_flow') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-exchange-alt me-2"></i>
                            التدفق النقدي
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('reports.sales_report') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-shopping-cart me-2"></i>
                            تقرير المبيعات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر المعاملات المالية
                </h5>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in recent_transactions %}
                            <tr>
                                <td>{{ transaction.date.strftime('%Y-%m-%d') if transaction.date else 'غير محدد' }}</td>
                                <td>{{ transaction.description or 'غير محدد' }}</td>
                                <td>
                                    {% if transaction.type == 'income' %}
                                        <span class="badge bg-success">إيراد</span>
                                    {% elif transaction.type == 'expense' %}
                                        <span class="badge bg-danger">مصروف</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ transaction.type }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if transaction.amount > 0 %}
                                        <span class="text-success">+{{ transaction.amount|currency }}</span>
                                    {% else %}
                                        <span class="text-danger">{{ transaction.amount|currency }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.amount|currency }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد معاملات</h5>
                    <p class="text-muted">لم يتم تسجيل أي معاملات مالية بعد</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المصروفات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="expensesChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Expenses Chart
const expensesCtx = document.getElementById('expensesChart').getContext('2d');
new Chart(expensesCtx, {
    type: 'doughnut',
    data: {
        labels: ['مصروفات التشغيل', 'مصروفات إدارية', 'مصروفات أخرى'],
        datasets: [{
            data: [40, 35, 25],
            backgroundColor: ['#dc3545', '#ffc107', '#6c757d'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
