{% extends "base.html" %}

{% block title %}سجل المبيعات - نقطة البيع{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-history me-2"></i>سجل المبيعات</h2>
                <div>
                    <a href="{{ url_for('pos.export_sales') }}{% if from_date %}?from_date={{ from_date }}{% endif %}{% if to_date %}{% if from_date %}&{% else %}?{% endif %}to_date={{ to_date }}{% endif %}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel me-2"></i>تصدير Excel
                    </a>
                    <a href="{{ url_for('pos.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة لنقطة البيع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        تصفية النتائج
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-lg-4 col-md-6">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="from_date" value="{{ from_date or '' }}">
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="to_date" value="{{ to_date or '' }}">
                        </div>
                        <div class="col-lg-4 col-md-12">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المبيعات
                        {% if sales.items %}
                        <span class="badge bg-light text-dark ms-2">{{ sales.total }} فاتورة</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if sales.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ والوقت</th>
                                    <th>العميل</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>طريقة الدفع</th>
                                    <th>الكاشير</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in sales.items %}
                                <tr>
                                    <td>
                                        <strong>{{ sale.invoice_number }}</strong>
                                    </td>
                                    <td>
                                        {{ sale.sale_date.strftime('%Y-%m-%d') }}
                                        <br>
                                        <small class="text-muted">{{ sale.sale_date.strftime('%H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        {% if sale.customer %}
                                            <i class="fas fa-user me-1"></i>{{ sale.customer.name }}
                                        {% else %}
                                            <i class="fas fa-money-bill me-1"></i>عميل نقدي
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success">{{ "{:,.2f}".format(sale.total_amount) }} ر.س</span>
                                        {% if sale.discount_amount > 0 %}
                                        <br>
                                        <small class="text-muted">خصم: {{ "{:,.2f}".format(sale.discount_amount) }} ر.س</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if sale.payment_method == 'cash' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-money-bill me-1"></i>نقدي
                                            </span>
                                        {% elif sale.payment_method == 'credit' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-credit-card me-1"></i>آجل
                                            </span>
                                        {% elif sale.payment_method == 'bank' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-university me-1"></i>بنكي
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ sale.payment_method }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ sale.user.full_name }}</td>
                                    <td>
                                        {% if sale.status == 'completed' %}
                                            <span class="badge bg-success">مكتملة</span>
                                        {% elif sale.status == 'pending' %}
                                            <span class="badge bg-warning">معلقة</span>
                                        {% elif sale.status == 'cancelled' %}
                                            <span class="badge bg-danger">ملغية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ sale.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('pos.invoice', sale_id=sale.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="عرض الفاتورة">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('pos.print_invoice', sale_id=sale.id) }}" 
                                               class="btn btn-sm btn-outline-info" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{{ url_for('pos.export_invoice_pdf', sale_id=sale.id) }}" 
                                               class="btn btn-sm btn-outline-danger" title="تصدير PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if sales.pages > 1 %}
                    <nav aria-label="تصفح الصفحات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if sales.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('pos.sales_history', page=sales.prev_num, from_date=from_date, to_date=to_date) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in sales.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != sales.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('pos.sales_history', page=page_num, from_date=from_date, to_date=to_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if sales.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('pos.sales_history', page=sales.next_num, from_date=from_date, to_date=to_date) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مبيعات</h5>
                        <p class="text-muted">لم يتم العثور على أي مبيعات في الفترة المحددة</p>
                        <a href="{{ url_for('pos.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    {% if sales.items %}
    <div class="row mt-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                    <h6 class="card-title">إجمالي الفواتير</h6>
                    <h5 class="text-primary">{{ sales.total }}</h5>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill fa-2x text-success mb-2"></i>
                    <h6 class="card-title">إجمالي المبيعات</h6>
                    <h5 class="text-success">
                        {% set total_sales = sales.items | sum(attribute='total_amount') %}
                        {{ "{:,.2f}".format(total_sales) }} ر.س
                    </h5>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x text-warning mb-2"></i>
                    <h6 class="card-title">إجمالي الخصومات</h6>
                    <h5 class="text-warning">
                        {% set total_discounts = sales.items | sum(attribute='discount_amount') %}
                        {{ "{:,.2f}".format(total_discounts) }} ر.س
                    </h5>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-2x text-info mb-2"></i>
                    <h6 class="card-title">متوسط الفاتورة</h6>
                    <h5 class="text-info">
                        {% if sales.total > 0 %}
                        {{ "{:,.2f}".format(total_sales / sales.total) }} ر.س
                        {% else %}
                        0.00 ر.س
                        {% endif %}
                    </h5>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
