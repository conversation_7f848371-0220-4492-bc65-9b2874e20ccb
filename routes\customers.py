# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from models import db, Customer, Sale, Payment
from utils import login_required, permission_required, log_audit, export_to_excel
from datetime import datetime
import pandas as pd
import os
from werkzeug.utils import secure_filename
from sqlalchemy import func

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/')
@login_required
@permission_required('all')
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Customer.query.filter_by(is_active=True)

    if search:
        query = query.filter(Customer.name.contains(search))

    customers = query.order_by(Customer.name).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('customers/index.html',
                         customers=customers,
                         search=search)

@customers_bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def add():
    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        customer_type = request.form.get('customer_type', 'regular')

        if not name:
            flash('يرجى إدخال اسم العميل', 'error')
            return render_template('customers/add.html')

        customer = Customer(
            name=name,
            phone=phone,
            email=email,
            address=address,
            customer_type=customer_type
        )

        db.session.add(customer)
        db.session.commit()

        # Log audit
        log_audit(session['user_id'], 'إضافة عميل', 'customers', customer.id,
                 new_values={'name': name, 'phone': phone})

        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers.index'))

    return render_template('customers/add.html')

@customers_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('all')
def edit(id):
    customer = Customer.query.get_or_404(id)

    if request.method == 'POST':
        old_values = {
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email
        }

        customer.name = request.form.get('name')
        customer.phone = request.form.get('phone')
        customer.email = request.form.get('email')
        customer.address = request.form.get('address')
        customer.customer_type = request.form.get('customer_type', 'regular')

        db.session.commit()

        new_values = {
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email
        }

        # Log audit
        log_audit(session['user_id'], 'تعديل عميل', 'customers', customer.id,
                 old_values=old_values, new_values=new_values)

        flash('تم تحديث بيانات العميل بنجاح', 'success')
        return redirect(url_for('customers.index'))

    return render_template('customers/edit.html', customer=customer)

@customers_bp.route('/delete/<int:id>')
@login_required
@permission_required('all')
def delete(id):
    customer = Customer.query.get_or_404(id)
    customer.is_active = False
    db.session.commit()

    # Log audit
    log_audit(session['user_id'], 'حذف عميل', 'customers', customer.id,
             old_values={'name': customer.name, 'is_active': True})

    flash('تم حذف العميل بنجاح', 'success')
    return redirect(url_for('customers.index'))

@customers_bp.route('/view/<int:id>')
@login_required
@permission_required('all')
def view(id):
    customer = Customer.query.get_or_404(id)

    # Get customer sales
    sales = Sale.query.filter_by(customer_id=id).order_by(Sale.sale_date.desc()).limit(20).all()

    # Get customer payments
    payments = Payment.query.filter_by(customer_id=id).order_by(Payment.payment_date.desc()).limit(20).all()

    # Calculate totals
    total_sales = db.session.query(func.sum(Sale.total_amount)).filter_by(customer_id=id).scalar() or 0
    total_payments = db.session.query(func.sum(Payment.amount)).filter(
        Payment.customer_id == id,
        Payment.payment_type == 'collection_from_customer'
    ).scalar() or 0

    return render_template('customers/view.html',
                         customer=customer,
                         sales=sales,
                         payments=payments,
                         total_sales=total_sales,
                         total_payments=total_payments)

@customers_bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])

    customers = Customer.query.filter(
        Customer.is_active == True,
        Customer.name.contains(query)
    ).limit(10).all()

    results = []
    for customer in customers:
        results.append({
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'balance': customer.balance
        })

    return jsonify(results)

@customers_bp.route('/statement/<int:id>')
@login_required
@permission_required('all')
def statement(id):
    customer = Customer.query.get_or_404(id)

    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    # Build query for transactions
    transactions = []

    # Get sales
    sales_query = Sale.query.filter_by(customer_id=id)
    if from_date:
        sales_query = sales_query.filter(Sale.sale_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        sales_query = sales_query.filter(Sale.sale_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    sales = sales_query.order_by(Sale.sale_date).all()

    for sale in sales:
        transactions.append({
            'date': sale.sale_date,
            'type': 'sale',
            'description': f'فاتورة رقم {sale.invoice_number}',
            'debit': sale.total_amount,
            'credit': 0,
            'balance': 0  # Will be calculated
        })

    # Get payments
    payments_query = Payment.query.filter(
        Payment.customer_id == id,
        Payment.payment_type == 'collection_from_customer'
    )
    if from_date:
        payments_query = payments_query.filter(Payment.payment_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        payments_query = payments_query.filter(Payment.payment_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    payments = payments_query.order_by(Payment.payment_date).all()

    for payment in payments:
        transactions.append({
            'date': payment.payment_date,
            'type': 'payment',
            'description': f'دفعة رقم {payment.payment_number}',
            'debit': 0,
            'credit': payment.amount,
            'balance': 0  # Will be calculated
        })

    # Sort transactions by date
    transactions.sort(key=lambda x: x['date'])

    # Calculate running balance
    balance = 0
    for transaction in transactions:
        balance += transaction['debit'] - transaction['credit']
        transaction['balance'] = balance

    return render_template('customers/statement.html',
                         customer=customer,
                         transactions=transactions,
                         from_date=from_date,
                         to_date=to_date)

@customers_bp.route('/export')
@login_required
@permission_required('all')
def export():
    customers = Customer.query.filter_by(is_active=True).all()

    data = []
    for customer in customers:
        data.append({
            'الرقم': customer.id,
            'الاسم': customer.name,
            'الهاتف': customer.phone,
            'البريد الإلكتروني': customer.email,
            'العنوان': customer.address,
            'الرصيد': customer.balance,
            'نوع العميل': customer.customer_type,
            'تاريخ الإضافة': customer.created_at.strftime('%Y-%m-%d')
        })

    filename = f"customers_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    filepath = export_to_excel(data, filename, "العملاء")

    if filepath:
        flash('تم تصدير البيانات بنجاح', 'success')
    else:
        flash('حدث خطأ في تصدير البيانات', 'error')

    return redirect(url_for('customers.index'))

@customers_bp.route('/import-excel', methods=['GET', 'POST'])
@login_required
@permission_required('customers')
def import_excel():
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('لم يتم اختيار ملف', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'error')
            return redirect(request.url)

        if file and file.filename.lower().endswith(('.xlsx', '.xls')):
            try:
                # Read Excel file
                df = pd.read_excel(file)

                # Expected columns (Arabic)
                expected_columns = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'نوع العميل']

                # Check if required columns exist
                if 'الاسم' not in df.columns:
                    flash('الملف يجب أن يحتوي على عمود "الاسم" على الأقل', 'error')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if customer already exists
                        existing_customer = Customer.query.filter_by(name=row['الاسم']).first()
                        if existing_customer:
                            error_count += 1
                            errors.append(f'السطر {index + 2}: العميل "{row["الاسم"]}" موجود مسبقاً')
                            continue

                        # Create new customer
                        customer = Customer(
                            name=row['الاسم'],
                            phone=row.get('الهاتف', ''),
                            email=row.get('البريد الإلكتروني', ''),
                            address=row.get('العنوان', ''),
                            customer_type=row.get('نوع العميل', 'regular'),
                            balance=0.0
                        )

                        db.session.add(customer)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'السطر {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()

                    # Log audit
                    log_audit(session['user_id'], 'استيراد عملاء من Excel', 'customers', None,
                             new_values={'imported_count': success_count})

                # Show results
                if success_count > 0:
                    flash(f'تم استيراد {success_count} عميل بنجاح', 'success')
                if error_count > 0:
                    flash(f'فشل في استيراد {error_count} عميل', 'warning')
                    for error in errors[:5]:  # Show first 5 errors
                        flash(error, 'error')

                return redirect(url_for('customers.index'))

            except Exception as e:
                flash(f'خطأ في قراءة الملف: {str(e)}', 'error')
                return redirect(request.url)
        else:
            flash('يرجى اختيار ملف Excel (.xlsx أو .xls)', 'error')
            return redirect(request.url)

    return render_template('customers/import_excel.html')

@customers_bp.route('/download-template')
@login_required
@permission_required('customers')
def download_template():
    """Download Excel template for customer import"""
    try:
        from utils import create_import_template

        headers = [
            'الاسم*', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'نوع العميل'
        ]

        sample_data = [
            {
                'الاسم*': 'أحمد محمد علي',
                'الهاتف': '0501234567',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'الرياض، المملكة العربية السعودية',
                'نوع العميل': 'regular'
            },
            {
                'الاسم*': 'شركة التقنية المتقدمة',
                'الهاتف': '0112345678',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'جدة، المملكة العربية السعودية',
                'نوع العميل': 'wholesale'
            }
        ]

        filename = "customers_import_template"
        filepath = create_import_template(headers, sample_data, filename, "قالب استيراد العملاء")

        if filepath and os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في إنشاء القالب', 'error')
            return redirect(url_for('customers.index'))

    except Exception as e:
        flash(f'حدث خطأ في إنشاء القالب: {str(e)}', 'error')
        return redirect(url_for('customers.index'))