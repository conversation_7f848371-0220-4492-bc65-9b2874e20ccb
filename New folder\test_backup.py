#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة النسخ الاحتياطي
Test Backup Functionality
"""

import requests
import time
import os
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class BackupTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_settings_page(self):
        """اختبار صفحة الإعدادات"""
        print("\n🔄 اختبار صفحة الإعدادات...")
        try:
            response = self.session.get(f"{BASE_URL}/settings/")
            if response.status_code == 200:
                print("✅ صفحة الإعدادات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة الإعدادات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في صفحة الإعدادات: {e}")
            return False
    
    def test_database_info_page(self):
        """اختبار صفحة معلومات قاعدة البيانات"""
        print("\n🔄 اختبار صفحة معلومات قاعدة البيانات...")
        try:
            response = self.session.get(f"{BASE_URL}/settings/database-info")
            if response.status_code == 200:
                print("✅ صفحة معلومات قاعدة البيانات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة معلومات قاعدة البيانات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في صفحة معلومات قاعدة البيانات: {e}")
            return False
    
    def test_backup_creation(self):
        """اختبار إنشاء نسخة احتياطية"""
        print("\n🔄 اختبار إنشاء نسخة احتياطية...")
        try:
            response = self.session.get(f"{BASE_URL}/settings/backup")
            
            if response.status_code == 200:
                # تحقق من نوع المحتوى
                content_type = response.headers.get('content-type', '')
                
                if 'application/octet-stream' in content_type or 'application/x-sqlite3' in content_type:
                    print("✅ تم إنشاء النسخة الاحتياطية بنجاح (ملف قاعدة بيانات)")
                    
                    # حفظ الملف للتحقق
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_filename = f'test_backup_{timestamp}.db'
                    with open(backup_filename, 'wb') as f:
                        f.write(response.content)
                    
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:  # ملف قاعدة البيانات يجب أن يكون أكبر من 1KB
                        print("✅ حجم الملف مناسب")
                        
                        # تنظيف ملف الاختبار
                        try:
                            os.remove(backup_filename)
                            print(f"🗑️ تم حذف ملف الاختبار: {backup_filename}")
                        except:
                            pass
                        
                        return True
                    else:
                        print("❌ حجم الملف صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ فشل في إنشاء النسخة الاحتياطية: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def test_backup_utils_function(self):
        """اختبار دالة النسخ الاحتياطي مباشرة"""
        print("\n🔄 اختبار دالة النسخ الاحتياطي مباشرة...")
        try:
            # استيراد الدالة مباشرة
            import sys
            sys.path.append('.')
            from utils import backup_database
            
            # تشغيل الدالة
            backup_path = backup_database()
            
            if backup_path and os.path.exists(backup_path):
                file_size = os.path.getsize(backup_path)
                print(f"✅ تم إنشاء النسخة الاحتياطية: {backup_path}")
                print(f"📁 حجم الملف: {file_size} بايت")
                
                # تنظيف ملف الاختبار
                try:
                    os.remove(backup_path)
                    print(f"🗑️ تم حذف ملف الاختبار: {backup_path}")
                except:
                    pass
                
                return True
            else:
                print("❌ فشل في إنشاء النسخة الاحتياطية")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار دالة النسخ الاحتياطي: {e}")
            return False
    
    def test_database_file_exists(self):
        """اختبار وجود ملف قاعدة البيانات"""
        print("\n🔄 اختبار وجود ملف قاعدة البيانات...")
        try:
            db_locations = [
                'autoparts.db',
                'instance/autoparts.db',
                os.path.join('instance', 'autoparts.db')
            ]
            
            found_db = None
            for location in db_locations:
                if os.path.exists(location):
                    found_db = location
                    break
            
            if found_db:
                file_size = os.path.getsize(found_db)
                print(f"✅ تم العثور على قاعدة البيانات: {found_db}")
                print(f"📁 حجم الملف: {file_size} بايت")
                return True
            else:
                print("❌ لم يتم العثور على ملف قاعدة البيانات")
                print("📍 المواقع المفحوصة:")
                for location in db_locations:
                    print(f"   - {location}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص ملف قاعدة البيانات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات النسخ الاحتياطي"""
        print("💾 بدء اختبار وظائف النسخ الاحتياطي")
        print("=" * 50)
        
        tests = [
            ("فحص ملف قاعدة البيانات", self.test_database_file_exists),
            ("دالة النسخ الاحتياطي المباشرة", self.test_backup_utils_function),
        ]
        
        # اختبارات تتطلب تسجيل دخول
        if self.login():
            tests.extend([
                ("صفحة الإعدادات", self.test_settings_page),
                ("صفحة معلومات قاعدة البيانات", self.test_database_info_page),
                ("إنشاء نسخة احتياطية", self.test_backup_creation),
            ])
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار النسخ الاحتياطي:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف النسخ الاحتياطي تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️  يوجد {failed} مشكلة في وظائف النسخ الاحتياطي")
            return False

def main():
    """الدالة الرئيسية"""
    print("💾 اختبار وظائف النسخ الاحتياطي - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = BackupTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
