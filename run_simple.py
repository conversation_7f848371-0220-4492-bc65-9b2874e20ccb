# -*- coding: utf-8 -*-
"""
Simple run script for AutoParts Manager
"""
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    
    print("🚗 AutoParts Manager V5.1")
    print("=" * 40)
    print("🚀 Starting system...")
    print("🌐 Open browser to: http://127.0.0.1:8888")
    print("⏹️  Press Ctrl+C to stop")
    print("=" * 40)
    
    app = create_app()
    app.run(debug=False, host='127.0.0.1', port=8888)
    
except ImportError as e:
    print(f"❌ Missing module: {e}")
    print("📦 Please install requirements:")
    print("   pip install Flask Flask-SQLAlchemy Flask-Login")
    input("Press Enter to exit...")
except Exception as e:
    print(f"❌ Error: {e}")
    input("Press Enter to exit...")
