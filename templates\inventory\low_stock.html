{% extends "base.html" %}

{% block title %}المخزون المنخفض{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-exclamation-triangle me-2 text-warning"></i>المخزون المنخفض</h2>
                <div class="btn-group">
                    <a href="{{ url_for('inventory.add_adjustment') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل مخزون
                    </a>
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                    <div>
                        <h5 class="alert-heading mb-1">تنبيه مخزون منخفض!</h5>
                        <p class="mb-0">
                            يوجد <strong>{{ products|length }}</strong> منتج وصل إلى الحد الأدنى أو أقل من المخزون المطلوب.
                            يرجى إعادة تعبئة هذه المنتجات في أقرب وقت ممكن.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        المنتجات التي تحتاج إعادة تعبئة
                    </h5>
                </div>
                <div class="card-body">
                    {% if products %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الباركود</th>
                                    <th>الفئة</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>النقص</th>
                                    <th>الموقع</th>
                                    <th>آخر حركة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr class="{% if product.quantity == 0 %}table-danger{% else %}table-warning{% endif %}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if product.quantity == 0 %}
                                            <i class="fas fa-times-circle text-danger me-2" title="نفد المخزون"></i>
                                            {% else %}
                                            <i class="fas fa-exclamation-triangle text-warning me-2" title="مخزون منخفض"></i>
                                            {% endif %}
                                            <div>
                                                <strong>{{ product.name }}</strong>
                                                {% if product.description %}
                                                <br>
                                                <small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code>{{ product.barcode }}</code>
                                    </td>
                                    <td>
                                        {% if product.category %}
                                        <span class="badge bg-light text-dark">{{ product.category.name }}</span>
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.quantity == 0 %}
                                        <span class="badge bg-danger">نفد المخزون</span>
                                        {% else %}
                                        <span class="text-warning fw-bold">{{ "{:,.2f}".format(product.quantity) }}</span>
                                        {% endif %}
                                        <small class="text-muted">{{ product.unit }}</small>
                                    </td>
                                    <td>
                                        {{ "{:,.2f}".format(product.min_quantity) }}
                                        <small class="text-muted">{{ product.unit }}</small>
                                    </td>
                                    <td>
                                        {% set shortage = product.min_quantity - product.quantity %}
                                        {% if shortage > 0 %}
                                        <span class="text-danger fw-bold">{{ "{:,.2f}".format(shortage) }}</span>
                                        <small class="text-muted">{{ product.unit }}</small>
                                        {% else %}
                                        <span class="text-success">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.location %}
                                        <span class="badge bg-info">{{ product.location }}</span>
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.updated_at %}
                                        {{ product.updated_at.strftime('%Y-%m-%d') }}
                                        <br>
                                        <small class="text-muted">{{ product.updated_at.strftime('%H:%M') }}</small>
                                        {% else %}
                                        <span class="text-muted">غير متوفر</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('inventory.add_adjustment') }}?product_id={{ product.id }}" 
                                               class="btn btn-outline-primary" title="تعديل المخزون">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('products.edit', id=product.id) }}" 
                                               class="btn btn-outline-info" title="تعديل المنتج">
                                                <i class="fas fa-cog"></i>
                                            </a>
                                            <a href="{{ url_for('inventory.product_history', product_id=product.id) }}" 
                                               class="btn btn-outline-secondary" title="سجل الحركات">
                                                <i class="fas fa-history"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <button onclick="exportLowStock()" class="btn btn-success">
                                        <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                                    </button>
                                    <button onclick="printLowStock()" class="btn btn-info">
                                        <i class="fas fa-print me-2"></i>طباعة القائمة
                                    </button>
                                </div>
                                <div>
                                    <a href="{{ url_for('inventory.add_adjustment') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>تعديل مخزون جماعي
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">ممتاز! جميع المنتجات في المستوى الطبيعي</h5>
                        <p class="text-muted">لا توجد منتجات تحتاج إعادة تعبئة في الوقت الحالي</p>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-primary">
                            <i class="fas fa-warehouse me-2"></i>العودة لإدارة المخزون
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    {% if products %}
    <div class="row mt-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                    <h6 class="card-title">نفد المخزون</h6>
                    <h4 class="text-danger">{{ products | selectattr('quantity', 'equalto', 0) | list | length }}</h4>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h6 class="card-title">مخزون منخفض</h6>
                    <h4 class="text-warning">{{ products | rejectattr('quantity', 'equalto', 0) | list | length }}</h4>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                    <h6 class="card-title">نسبة التأثر</h6>
                    <h4 class="text-info">
                        {% set total_products = products | length %}
                        {% if total_products > 0 %}
                        {{ "%.1f" | format((total_products / 100) * 100) }}%
                        {% else %}
                        0%
                        {% endif %}
                    </h4>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                    <h6 class="card-title">يحتاج إجراء فوري</h6>
                    <h4 class="text-primary">{{ products | selectattr('quantity', 'equalto', 0) | list | length }}</h4>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function exportLowStock() {
    // Create CSV content
    let csv = 'المنتج,الباركود,الفئة,الكمية الحالية,الحد الأدنى,النقص,الموقع\n';
    
    const table = document.querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let rowData = [];
        for (let i = 0; i < 7; i++) { // First 7 columns only
            if (cells[i]) {
                rowData.push('"' + cells[i].textContent.trim().replace(/"/g, '""') + '"');
            }
        }
        csv += rowData.join(',') + '\n';
    });
    
    // Download CSV
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'المخزون_المنخفض_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

function printLowStock() {
    const printContent = document.querySelector('.card-body').innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="direction: rtl; font-family: Arial, sans-serif;">
            <h2 style="text-align: center; margin-bottom: 20px;">تقرير المخزون المنخفض</h2>
            <p style="text-align: center; margin-bottom: 30px;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
}
</script>
{% endblock %}
