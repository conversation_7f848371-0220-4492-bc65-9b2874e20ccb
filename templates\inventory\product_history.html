{% extends "base.html" %}

{% block title %}سجل المنتج - {{ product.name }}{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-history me-2"></i>سجل المنتج</h2>
                <a href="{{ url_for('inventory.transactions') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للحركات
                </a>
            </div>
        </div>
    </div>

    <!-- Product Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        معلومات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>اسم المنتج:</strong>
                            <p class="mb-0">{{ product.name }}</p>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>الباركود:</strong>
                            <p class="mb-0"><code>{{ product.barcode }}</code></p>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>الفئة:</strong>
                            <p class="mb-0">
                                {% if product.category %}
                                <span class="badge bg-light text-dark">{{ product.category.name }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>الوحدة:</strong>
                            <p class="mb-0">{{ product.unit }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>الكمية الحالية:</strong>
                            <p class="mb-0">
                                <span class="{% if product.quantity <= product.min_quantity %}text-warning{% else %}text-success{% endif %} fw-bold">
                                    {{ "{:,.2f}".format(product.quantity) }} {{ product.unit }}
                                </span>
                            </p>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>الحد الأدنى:</strong>
                            <p class="mb-0">{{ "{:,.2f}".format(product.min_quantity) }} {{ product.unit }}</p>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>سعر التكلفة:</strong>
                            <p class="mb-0">{{ "{:,.2f}".format(product.cost_price) }} ر.س</p>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <strong>سعر البيع:</strong>
                            <p class="mb-0">{{ "{:,.2f}".format(product.selling_price) }} ر.س</p>
                        </div>
                    </div>
                    
                    {% if product.description %}
                    <div class="row">
                        <div class="col-12">
                            <strong>الوصف:</strong>
                            <p class="mb-0">{{ product.description }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل حركات المنتج
                        {% if transactions %}
                        <span class="badge bg-light text-dark ms-2">{{ transactions|length }} حركة</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ والوقت</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>الرصيد بعد الحركة</th>
                                    <th>المرجع</th>
                                    <th>ملاحظات</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>
                                        {{ transaction.transaction_date.strftime('%Y-%m-%d') }}
                                        <br>
                                        <small class="text-muted">{{ transaction.transaction_date.strftime('%H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'in' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-arrow-down me-1"></i>دخول
                                            </span>
                                        {% elif transaction.transaction_type == 'out' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-arrow-up me-1"></i>خروج
                                            </span>
                                        {% elif transaction.transaction_type == 'adjustment' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-balance-scale me-1"></i>تعديل
                                            </span>
                                        {% elif transaction.transaction_type == 'transfer' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-exchange-alt me-1"></i>تحويل
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ transaction.transaction_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'out' %}
                                            <span class="text-danger fw-bold">-{{ "{:,.2f}".format(transaction.quantity) }}</span>
                                        {% else %}
                                            <span class="text-success fw-bold">+{{ "{:,.2f}".format(transaction.quantity) }}</span>
                                        {% endif %}
                                        <small class="text-muted">{{ product.unit }}</small>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ "{:,.2f}".format(transaction.balance_after or 0) }}</span>
                                        <small class="text-muted">{{ product.unit }}</small>
                                    </td>
                                    <td>
                                        {% if transaction.reference_type and transaction.reference_id %}
                                            <span class="badge bg-light text-dark">
                                                {{ transaction.reference_type }} #{{ transaction.reference_id }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.notes %}
                                            <span title="{{ transaction.notes }}">
                                                {{ transaction.notes[:30] }}{% if transaction.notes|length > 30 %}...{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.user.full_name }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Export Options -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button onclick="exportHistory()" class="btn btn-success btn-sm">
                                        <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                                    </button>
                                    <button onclick="printHistory()" class="btn btn-info btn-sm">
                                        <i class="fas fa-print me-2"></i>طباعة السجل
                                    </button>
                                </div>
                                <div>
                                    <a href="{{ url_for('inventory.add_adjustment') }}?product_id={{ product.id }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-2"></i>تعديل المخزون
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد حركات لهذا المنتج</h5>
                        <p class="text-muted">لم يتم تسجيل أي حركات لهذا المنتج بعد</p>
                        <a href="{{ url_for('inventory.add_adjustment') }}?product_id={{ product.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة حركة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    {% if transactions %}
    <div class="row mt-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-arrow-down fa-2x text-success mb-2"></i>
                    <h6 class="card-title">إجمالي الدخول</h6>
                    <h5 class="text-success">
                        {% set total_in = transactions | selectattr('transaction_type', 'equalto', 'in') | sum(attribute='quantity') %}
                        {{ "{:,.2f}".format(total_in or 0) }} {{ product.unit }}
                    </h5>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <i class="fas fa-arrow-up fa-2x text-danger mb-2"></i>
                    <h6 class="card-title">إجمالي الخروج</h6>
                    <h5 class="text-danger">
                        {% set total_out = transactions | selectattr('transaction_type', 'equalto', 'out') | sum(attribute='quantity') %}
                        {{ "{:,.2f}".format(total_out or 0) }} {{ product.unit }}
                    </h5>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-balance-scale fa-2x text-warning mb-2"></i>
                    <h6 class="card-title">عدد التعديلات</h6>
                    <h5 class="text-warning">
                        {{ transactions | selectattr('transaction_type', 'equalto', 'adjustment') | list | length }}
                    </h5>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-list fa-2x text-info mb-2"></i>
                    <h6 class="card-title">إجمالي الحركات</h6>
                    <h5 class="text-info">{{ transactions | length }}</h5>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function exportHistory() {
    // Create CSV content
    let csv = 'التاريخ والوقت,نوع الحركة,الكمية,الرصيد بعد الحركة,المرجع,ملاحظات,المستخدم\n';
    
    const table = document.querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let rowData = [];
        cells.forEach(cell => {
            rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
        });
        csv += rowData.join(',') + '\n';
    });
    
    // Download CSV
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'سجل_المنتج_{{ product.name }}_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

function printHistory() {
    const printContent = document.querySelector('.container-fluid').innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="direction: rtl; font-family: Arial, sans-serif;">
            <h2 style="text-align: center; margin-bottom: 20px;">سجل المنتج: {{ product.name }}</h2>
            <p style="text-align: center; margin-bottom: 30px;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
}
</script>
{% endblock %}
