#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تقرير المبيعات
Test Sales Report
"""

import requests
import time

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class SalesReportTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_sales_report(self):
        """اختبار صفحة تقرير المبيعات"""
        print("\n🔄 اختبار صفحة تقرير المبيعات...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/sales-report")
            
            print(f"📄 كود الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                if "تقرير المبيعات" in response.text:
                    print("✅ صفحة تقرير المبيعات تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة تقرير المبيعات لا تحتوي على المحتوى المتوقع")
                    print(f"📄 بداية المحتوى: {response.text[:500]}...")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة تقرير المبيعات: {response.status_code}")
                print(f"📄 محتوى الخطأ: {response.text[:500]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة تقرير المبيعات: {e}")
            return False
    
    def test_sales_report_excel_export(self):
        """اختبار تصدير Excel لتقرير المبيعات"""
        print("\n🔄 اختبار تصدير Excel لتقرير المبيعات...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/sales-report?export=excel")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type or 'application/octet-stream' in content_type:
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:
                        print("✅ تصدير Excel لتقرير المبيعات يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم ملف Excel صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في تصدير Excel: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تصدير Excel: {e}")
            return False
    
    def test_sales_report_pdf_export(self):
        """اختبار تصدير PDF لتقرير المبيعات"""
        print("\n🔄 اختبار تصدير PDF لتقرير المبيعات...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/sales-report?export=pdf")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/pdf' in content_type or 'application/octet-stream' in content_type:
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:
                        print("✅ تصدير PDF لتقرير المبيعات يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم ملف PDF صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في تصدير PDF: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تصدير PDF: {e}")
            return False
    
    def test_reports_index(self):
        """اختبار صفحة التقارير الرئيسية"""
        print("\n🔄 اختبار صفحة التقارير الرئيسية...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/")
            
            if response.status_code == 200:
                if "التقارير" in response.text:
                    print("✅ صفحة التقارير الرئيسية تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة التقارير لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة التقارير: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة التقارير: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات تقرير المبيعات"""
        print("📊 بدء اختبار تقرير المبيعات")
        print("=" * 50)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("صفحة التقارير الرئيسية", self.test_reports_index),
            ("صفحة تقرير المبيعات", self.test_sales_report),
            ("تصدير Excel لتقرير المبيعات", self.test_sales_report_excel_export),
            ("تصدير PDF لتقرير المبيعات", self.test_sales_report_pdf_export),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(2)  # انتظار ثانيتين بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار تقرير المبيعات:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف تقرير المبيعات تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة في تقرير المبيعات")
            return False

def main():
    """الدالة الرئيسية"""
    print("📊 اختبار تقرير المبيعات - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = SalesReportTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
