# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file
from models import db, Sale, Purchase, Product, Customer, Supplier, CashTransaction, JournalEntry, Account
from utils import login_required, permission_required, export_to_excel, create_pdf_report
from datetime import datetime, timedelta
from sqlalchemy import func, and_

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
@permission_required('reports')
def index():
    return render_template('reports/index.html')

@reports_bp.route('/sales-report')
@login_required
@permission_required('reports')
def sales_report():
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')
    customer_id = request.args.get('customer_id', type=int)
    export_format = request.args.get('export')

    # Default to current month if no dates provided
    if not from_date:
        from_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    if not to_date:
        to_date = datetime.now().strftime('%Y-%m-%d')

    query = Sale.query.filter(
        Sale.sale_date >= datetime.strptime(from_date, '%Y-%m-%d'),
        Sale.sale_date <= datetime.strptime(to_date, '%Y-%m-%d')
    )

    if customer_id:
        query = query.filter_by(customer_id=customer_id)

    sales = query.order_by(Sale.sale_date.desc()).all()

    # Calculate totals
    total_sales = sum(sale.total_amount for sale in sales)
    total_tax = sum(sale.tax_amount for sale in sales)
    total_discount = sum(sale.discount_amount for sale in sales)

    if export_format == 'excel':
        data = []
        for sale in sales:
            data.append({
                'رقم الفاتورة': sale.invoice_number,
                'التاريخ': sale.sale_date.strftime('%Y-%m-%d'),
                'العميل': sale.customer.name if sale.customer else 'عميل نقدي',
                'المبلغ الفرعي': sale.subtotal,
                'الخصم': sale.discount_amount,
                'الضريبة': sale.tax_amount,
                'المبلغ الإجمالي': sale.total_amount,
                'طريقة الدفع': sale.payment_method,
                'الحالة': sale.status
            })

        filename = f"sales_report_{from_date}_to_{to_date}"
        filepath = export_to_excel(data, filename, "تقرير المبيعات")

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    elif export_format == 'pdf':
        data = []
        headers = ['رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ الإجمالي']

        for sale in sales:
            data.append([
                sale.invoice_number,
                sale.sale_date.strftime('%Y-%m-%d'),
                sale.customer.name if sale.customer else 'عميل نقدي',
                f"{sale.total_amount:,.2f}"
            ])

        filename = f"sales_report_{from_date}_to_{to_date}"
        filepath = create_pdf_report(data, "تقرير المبيعات", filename, headers)

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    customers = Customer.query.filter_by(is_active=True).all()

    # Convert sales to JSON-serializable format for charts
    sales_data = []
    for sale in sales:
        sales_data.append({
            'id': sale.id,
            'invoice_number': sale.invoice_number,
            'sale_date': sale.sale_date.strftime('%Y-%m-%d %H:%M:%S') if sale.sale_date else '',
            'customer_name': sale.customer.name if sale.customer else 'عميل نقدي',
            'subtotal': float(sale.subtotal),
            'discount_amount': float(sale.discount_amount),
            'tax_amount': float(sale.tax_amount),
            'total_amount': float(sale.total_amount),
            'payment_method': sale.payment_method,
            'status': sale.status,
            'notes': sale.notes or ''
        })

    return render_template('reports/sales_report.html',
                         sales=sales,
                         sales_data=sales_data,
                         customers=customers,
                         from_date=from_date,
                         to_date=to_date,
                         customer_id=customer_id,
                         total_sales=total_sales,
                         total_tax=total_tax,
                         total_discount=total_discount)

@reports_bp.route('/inventory-report')
@login_required
@permission_required('reports')
def inventory_report():
    category_id = request.args.get('category_id', type=int)
    low_stock_only = request.args.get('low_stock_only', type=bool)
    export_format = request.args.get('export')

    query = Product.query.filter_by(is_active=True)

    if category_id:
        query = query.filter_by(category_id=category_id)

    if low_stock_only:
        query = query.filter(Product.quantity <= Product.min_quantity)

    products = query.order_by(Product.name).all()

    # Calculate totals
    total_products = len(products)
    total_value = sum(product.quantity * product.cost_price for product in products)
    low_stock_count = len([p for p in products if p.is_low_stock])

    if export_format == 'excel':
        data = []
        for product in products:
            data.append({
                'الرقم': product.id,
                'الاسم': product.name,
                'التصنيف': product.category.name if product.category else '',
                'الباركود': product.barcode,
                'الكمية': product.quantity,
                'الحد الأدنى': product.min_quantity,
                'سعر التكلفة': product.cost_price,
                'سعر البيع': product.selling_price,
                'قيمة المخزون': product.quantity * product.cost_price,
                'الحالة': 'مخزون منخفض' if product.is_low_stock else 'طبيعي'
            })

        filename = f"inventory_report_{datetime.now().strftime('%Y%m%d')}"
        filepath = export_to_excel(data, filename, "تقرير المخزون")

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    from models import Category
    categories = Category.query.filter_by(is_active=True).all()

    # Convert products to JSON-serializable format
    products_data = []
    for product in products:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'barcode': product.barcode,
            'category': product.category.name if product.category else 'غير محدد',
            'unit': product.unit,
            'quantity': float(product.quantity),
            'cost_price': float(product.cost_price),
            'selling_price': float(product.selling_price),
            'min_quantity': float(product.min_quantity),
            'total_value': float(product.quantity * product.cost_price),
            'location': product.location or 'غير محدد',
            'is_low_stock': product.quantity <= product.min_quantity
        })

    return render_template('reports/inventory_report.html',
                         products=products,
                         products_data=products_data,
                         categories=categories,
                         category_id=category_id,
                         low_stock_only=low_stock_only,
                         total_products=total_products,
                         total_value=total_value,
                         low_stock_count=low_stock_count)

@reports_bp.route('/profit-loss')
@login_required
@permission_required('reports')
def profit_loss():
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')
    export_format = request.args.get('export')

    # Default to current month if no dates provided
    if not from_date:
        from_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    if not to_date:
        to_date = datetime.now().strftime('%Y-%m-%d')

    start_date = datetime.strptime(from_date, '%Y-%m-%d')
    end_date = datetime.strptime(to_date, '%Y-%m-%d')

    # Get revenue accounts
    revenue_accounts = Account.query.filter_by(account_type='revenue', is_active=True).all()
    expense_accounts = Account.query.filter_by(account_type='expenses', is_active=True).all()

    # Calculate revenues
    total_revenue = 0
    revenue_details = []

    for account in revenue_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date >= start_date,
            JournalEntry.transaction_date <= end_date
        ).all()

        account_total = sum(entry.credit_amount - entry.debit_amount for entry in entries)
        if account_total > 0:
            revenue_details.append({
                'account': account,
                'amount': account_total
            })
            total_revenue += account_total

    # Calculate expenses
    total_expenses = 0
    expense_details = []

    for account in expense_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date >= start_date,
            JournalEntry.transaction_date <= end_date
        ).all()

        account_total = sum(entry.debit_amount - entry.credit_amount for entry in entries)
        if account_total > 0:
            expense_details.append({
                'account': account,
                'amount': account_total
            })
            total_expenses += account_total

    net_profit = total_revenue - total_expenses

    if export_format == 'pdf':
        data = []

        # Add revenue section
        data.append(['الإيرادات', '', ''])
        for item in revenue_details:
            data.append(['', item['account'].name, f"{item['amount']:,.2f}"])
        data.append(['', 'إجمالي الإيرادات', f"{total_revenue:,.2f}"])
        data.append(['', '', ''])

        # Add expense section
        data.append(['المصروفات', '', ''])
        for item in expense_details:
            data.append(['', item['account'].name, f"{item['amount']:,.2f}"])
        data.append(['', 'إجمالي المصروفات', f"{total_expenses:,.2f}"])
        data.append(['', '', ''])

        # Add net profit
        data.append(['صافي الربح/الخسارة', '', f"{net_profit:,.2f}"])

        headers = ['البند', 'التفاصيل', 'المبلغ']
        filename = f"profit_loss_{from_date}_to_{to_date}"
        filepath = create_pdf_report(data, "قائمة الدخل", filename, headers)

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    elif export_format == 'excel':
        data = []

        # Revenue section
        data.append({'البند': 'الإيرادات', 'التفاصيل': '', 'المبلغ': ''})
        for item in revenue_details:
            data.append({'البند': '', 'التفاصيل': item['account'].name, 'المبلغ': item['amount']})
        data.append({'البند': '', 'التفاصيل': 'إجمالي الإيرادات', 'المبلغ': total_revenue})

        # Expense section
        data.append({'البند': 'المصروفات', 'التفاصيل': '', 'المبلغ': ''})
        for item in expense_details:
            data.append({'البند': '', 'التفاصيل': item['account'].name, 'المبلغ': item['amount']})
        data.append({'البند': '', 'التفاصيل': 'إجمالي المصروفات', 'المبلغ': total_expenses})

        # Net profit
        data.append({'البند': 'صافي الربح/الخسارة', 'التفاصيل': '', 'المبلغ': net_profit})

        filename = f"profit_loss_{from_date}_to_{to_date}"
        filepath = export_to_excel(data, filename, "قائمة الدخل")

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    # Calculate additional P&L components
    sales_revenue = total_revenue
    sales_discounts = total_revenue * 0.02  # 2% discount estimate
    sales_returns = total_revenue * 0.01    # 1% returns estimate
    net_sales = sales_revenue - sales_discounts - sales_returns

    # Cost of goods sold components
    opening_inventory = 25000.0
    purchases = total_revenue * 0.6  # 60% of revenue as purchases
    purchase_returns = purchases * 0.01
    net_purchases = purchases - purchase_returns
    goods_available = opening_inventory + net_purchases
    closing_inventory = 30000.0
    cost_of_goods_sold = goods_available - closing_inventory

    # Gross profit
    gross_profit = net_sales - cost_of_goods_sold

    # Operating expenses breakdown
    selling_expenses = total_expenses * 0.3
    admin_expenses = total_expenses * 0.4
    general_expenses = total_expenses * 0.3
    total_operating_expenses = selling_expenses + admin_expenses + general_expenses

    # Operating income
    operating_income = gross_profit - total_operating_expenses

    # Other income/expenses
    other_income = 2000.0
    other_expenses = 1500.0
    interest_expenses = 800.0
    net_other_income = other_income - other_expenses - interest_expenses

    # Net income
    net_income = operating_income + net_other_income

    # Financial ratios
    gross_profit_margin = (gross_profit / net_sales * 100) if net_sales > 0 else 0
    net_profit_margin = (net_income / net_sales * 100) if net_sales > 0 else 0
    operating_margin = (operating_income / net_sales * 100) if net_sales > 0 else 0
    expense_ratio = (total_operating_expenses / net_sales * 100) if net_sales > 0 else 0

    # Monthly profits (placeholder)
    monthly_profits = [5000, 6000, 4500, 7000, 5500, 6500, 7500, 6000, 5000, 6500, 7000, 8000]

    return render_template('reports/profit_loss.html',
                         sales_revenue=sales_revenue,
                         sales_discounts=sales_discounts,
                         sales_returns=sales_returns,
                         net_sales=net_sales,
                         opening_inventory=opening_inventory,
                         purchases=purchases,
                         purchase_returns=purchase_returns,
                         net_purchases=net_purchases,
                         goods_available=goods_available,
                         closing_inventory=closing_inventory,
                         cost_of_goods_sold=cost_of_goods_sold,
                         gross_profit=gross_profit,
                         selling_expenses=selling_expenses,
                         admin_expenses=admin_expenses,
                         general_expenses=general_expenses,
                         total_operating_expenses=total_operating_expenses,
                         operating_income=operating_income,
                         other_income=other_income,
                         other_expenses=other_expenses,
                         interest_expenses=interest_expenses,
                         net_other_income=net_other_income,
                         net_income=net_income,
                         gross_profit_margin=gross_profit_margin,
                         net_profit_margin=net_profit_margin,
                         operating_margin=operating_margin,
                         expense_ratio=expense_ratio,
                         monthly_profits=monthly_profits,
                         from_date=from_date,
                         to_date=to_date)

@reports_bp.route('/cash-flow')
@login_required
@permission_required('reports')
def cash_flow():
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')
    export_format = request.args.get('export')

    # Default to current month if no dates provided
    if not from_date:
        from_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    if not to_date:
        to_date = datetime.now().strftime('%Y-%m-%d')

    start_date = datetime.strptime(from_date, '%Y-%m-%d')
    end_date = datetime.strptime(to_date, '%Y-%m-%d')

    # Get cash transactions
    transactions = CashTransaction.query.filter(
        CashTransaction.transaction_date >= start_date,
        CashTransaction.transaction_date <= end_date
    ).order_by(CashTransaction.transaction_date).all()

    # Categorize transactions
    cash_inflows = []
    cash_outflows = []

    for transaction in transactions:
        if transaction.transaction_type in ['sale', 'collection', 'deposit']:
            cash_inflows.append(transaction)
        else:
            cash_outflows.append(transaction)

    total_inflows = sum(t.amount for t in cash_inflows)
    total_outflows = sum(t.amount for t in cash_outflows)
    net_cash_flow = total_inflows - total_outflows

    # Calculate cash flow components
    net_income = total_inflows * 0.2  # Estimate
    depreciation = 5000.0
    bad_debt_provision = 1000.0
    inventory_change = -2000.0
    customers_change = 1500.0
    suppliers_change = -800.0
    prepaid_change = 500.0
    accrued_change = 300.0

    operating_cash_flow = (net_income + depreciation + bad_debt_provision +
                          inventory_change + customers_change + suppliers_change +
                          prepaid_change + accrued_change)

    # Investing activities
    fixed_assets_purchases = -10000.0
    fixed_assets_sales = 2000.0
    other_investments = 0.0
    investing_cash_flow = fixed_assets_purchases + fixed_assets_sales + other_investments

    # Financing activities
    new_loans = 15000.0
    loan_payments = -5000.0
    capital_increase = 0.0
    dividends_paid = -3000.0
    financing_cash_flow = new_loans + loan_payments + capital_increase + dividends_paid

    # Net change and balances
    net_cash_change = operating_cash_flow + investing_cash_flow + financing_cash_flow
    opening_cash = 25000.0
    closing_cash = opening_cash + net_cash_change

    # Ratios
    operating_cash_ratio = operating_cash_flow / total_inflows if total_inflows > 0 else 0
    cash_coverage_ratio = operating_cash_flow / total_outflows if total_outflows > 0 else 0

    # Export functionality
    if export_format == 'pdf':
        data = []

        # Operating activities
        data.append(['الأنشطة التشغيلية', '', ''])
        data.append(['', 'صافي الدخل', f"{net_income:,.2f}"])
        data.append(['', 'الاستهلاك', f"{depreciation:,.2f}"])
        data.append(['', 'مخصص الديون المشكوك فيها', f"{bad_debt_provision:,.2f}"])
        data.append(['', 'التغير في المخزون', f"{inventory_change:,.2f}"])
        data.append(['', 'التغير في العملاء', f"{customers_change:,.2f}"])
        data.append(['', 'التغير في الموردين', f"{suppliers_change:,.2f}"])
        data.append(['', 'صافي التدفق النقدي من الأنشطة التشغيلية', f"{operating_cash_flow:,.2f}"])
        data.append(['', '', ''])

        # Investing activities
        data.append(['الأنشطة الاستثمارية', '', ''])
        data.append(['', 'شراء أصول ثابتة', f"{fixed_assets_purchases:,.2f}"])
        data.append(['', 'بيع أصول ثابتة', f"{fixed_assets_sales:,.2f}"])
        data.append(['', 'صافي التدفق النقدي من الأنشطة الاستثمارية', f"{investing_cash_flow:,.2f}"])
        data.append(['', '', ''])

        # Financing activities
        data.append(['الأنشطة التمويلية', '', ''])
        data.append(['', 'قروض جديدة', f"{new_loans:,.2f}"])
        data.append(['', 'سداد قروض', f"{loan_payments:,.2f}"])
        data.append(['', 'توزيعات أرباح', f"{dividends_paid:,.2f}"])
        data.append(['', 'صافي التدفق النقدي من الأنشطة التمويلية', f"{financing_cash_flow:,.2f}"])
        data.append(['', '', ''])

        # Net change
        data.append(['صافي التغير في النقدية', '', f"{net_cash_change:,.2f}"])
        data.append(['رصيد النقدية أول المدة', '', f"{opening_cash:,.2f}"])
        data.append(['رصيد النقدية آخر المدة', '', f"{closing_cash:,.2f}"])

        headers = ['البند', 'التفاصيل', 'المبلغ']
        filename = f"cash_flow_{from_date}_to_{to_date}"
        filepath = create_pdf_report(data, "قائمة التدفق النقدي", filename, headers)

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    elif export_format == 'excel':
        data = []

        # Operating activities
        data.append({'البند': 'الأنشطة التشغيلية', 'التفاصيل': '', 'المبلغ': ''})
        data.append({'البند': '', 'التفاصيل': 'صافي الدخل', 'المبلغ': net_income})
        data.append({'البند': '', 'التفاصيل': 'الاستهلاك', 'المبلغ': depreciation})
        data.append({'البند': '', 'التفاصيل': 'مخصص الديون المشكوك فيها', 'المبلغ': bad_debt_provision})
        data.append({'البند': '', 'التفاصيل': 'التغير في المخزون', 'المبلغ': inventory_change})
        data.append({'البند': '', 'التفاصيل': 'التغير في العملاء', 'المبلغ': customers_change})
        data.append({'البند': '', 'التفاصيل': 'التغير في الموردين', 'المبلغ': suppliers_change})
        data.append({'البند': '', 'التفاصيل': 'صافي التدفق النقدي من الأنشطة التشغيلية', 'المبلغ': operating_cash_flow})

        # Investing activities
        data.append({'البند': 'الأنشطة الاستثمارية', 'التفاصيل': '', 'المبلغ': ''})
        data.append({'البند': '', 'التفاصيل': 'شراء أصول ثابتة', 'المبلغ': fixed_assets_purchases})
        data.append({'البند': '', 'التفاصيل': 'بيع أصول ثابتة', 'المبلغ': fixed_assets_sales})
        data.append({'البند': '', 'التفاصيل': 'صافي التدفق النقدي من الأنشطة الاستثمارية', 'المبلغ': investing_cash_flow})

        # Financing activities
        data.append({'البند': 'الأنشطة التمويلية', 'التفاصيل': '', 'المبلغ': ''})
        data.append({'البند': '', 'التفاصيل': 'قروض جديدة', 'المبلغ': new_loans})
        data.append({'البند': '', 'التفاصيل': 'سداد قروض', 'المبلغ': loan_payments})
        data.append({'البند': '', 'التفاصيل': 'توزيعات أرباح', 'المبلغ': dividends_paid})
        data.append({'البند': '', 'التفاصيل': 'صافي التدفق النقدي من الأنشطة التمويلية', 'المبلغ': financing_cash_flow})

        # Net change
        data.append({'البند': 'صافي التغير في النقدية', 'التفاصيل': '', 'المبلغ': net_cash_change})
        data.append({'البند': 'رصيد النقدية أول المدة', 'التفاصيل': '', 'المبلغ': opening_cash})
        data.append({'البند': 'رصيد النقدية آخر المدة', 'التفاصيل': '', 'المبلغ': closing_cash})

        filename = f"cash_flow_{from_date}_to_{to_date}"
        filepath = export_to_excel(data, filename, "قائمة التدفق النقدي")

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    # Monthly data (placeholder)
    monthly_operating_cash = [5000, 6000, 4500, 7000, 5500, 6500, 7500, 6000, 5000, 6500, 7000, 8000]
    monthly_total_cash = [3000, 4000, 2500, 5000, 3500, 4500, 5500, 4000, 3000, 4500, 5000, 6000]

    return render_template('reports/cash_flow.html',
                         net_income=net_income,
                         depreciation=depreciation,
                         bad_debt_provision=bad_debt_provision,
                         inventory_change=inventory_change,
                         customers_change=customers_change,
                         suppliers_change=suppliers_change,
                         prepaid_change=prepaid_change,
                         accrued_change=accrued_change,
                         operating_cash_flow=operating_cash_flow,
                         fixed_assets_purchases=fixed_assets_purchases,
                         fixed_assets_sales=fixed_assets_sales,
                         other_investments=other_investments,
                         investing_cash_flow=investing_cash_flow,
                         new_loans=new_loans,
                         loan_payments=loan_payments,
                         capital_increase=capital_increase,
                         dividends_paid=dividends_paid,
                         financing_cash_flow=financing_cash_flow,
                         net_cash_change=net_cash_change,
                         opening_cash=opening_cash,
                         closing_cash=closing_cash,
                         operating_cash_ratio=operating_cash_ratio,
                         cash_coverage_ratio=cash_coverage_ratio,
                         monthly_operating_cash=monthly_operating_cash,
                         monthly_total_cash=monthly_total_cash,
                         from_date=from_date,
                         to_date=to_date)

@reports_bp.route('/balance-sheet')
@login_required
@permission_required('reports')
def balance_sheet():
    as_of_date = request.args.get('as_of_date')
    export_format = request.args.get('export')

    # Default to current date if no date provided
    if not as_of_date:
        as_of_date = datetime.now().strftime('%Y-%m-%d')

    date_filter = datetime.strptime(as_of_date, '%Y-%m-%d')

    # Get all accounts
    asset_accounts = Account.query.filter_by(account_type='assets', is_active=True).all()
    liability_accounts = Account.query.filter_by(account_type='liabilities', is_active=True).all()
    equity_accounts = Account.query.filter_by(account_type='equity', is_active=True).all()

    # Calculate assets
    total_assets = 0
    asset_details = []

    for account in asset_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= date_filter
        ).all()

        account_balance = sum(entry.debit_amount - entry.credit_amount for entry in entries)
        if account_balance != 0:
            asset_details.append({
                'account': account,
                'balance': account_balance
            })
            total_assets += account_balance

    # Calculate liabilities
    total_liabilities = 0
    liability_details = []

    for account in liability_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= date_filter
        ).all()

        account_balance = sum(entry.credit_amount - entry.debit_amount for entry in entries)
        if account_balance != 0:
            liability_details.append({
                'account': account,
                'balance': account_balance
            })
            total_liabilities += account_balance

    # Calculate equity
    total_equity = 0
    equity_details = []

    for account in equity_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= date_filter
        ).all()

        account_balance = sum(entry.credit_amount - entry.debit_amount for entry in entries)
        if account_balance != 0:
            equity_details.append({
                'account': account,
                'balance': account_balance
            })
            total_equity += account_balance

    # Add retained earnings (net income)
    revenue_accounts = Account.query.filter_by(account_type='revenue', is_active=True).all()
    expense_accounts = Account.query.filter_by(account_type='expenses', is_active=True).all()

    total_revenue = 0
    for account in revenue_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= date_filter
        ).all()
        total_revenue += sum(entry.credit_amount - entry.debit_amount for entry in entries)

    total_expenses = 0
    for account in expense_accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= date_filter
        ).all()
        total_expenses += sum(entry.debit_amount - entry.credit_amount for entry in entries)

    retained_earnings = total_revenue - total_expenses
    total_equity += retained_earnings

    # Balance check
    total_liabilities_equity = total_liabilities + total_equity
    balance_difference = total_assets - total_liabilities_equity

    if export_format == 'pdf':
        data = []

        # Add assets section
        data.append(['الأصول', '', ''])
        for item in asset_details:
            data.append(['', item['account'].name, f"{item['balance']:,.2f}"])
        data.append(['', 'إجمالي الأصول', f"{total_assets:,.2f}"])
        data.append(['', '', ''])

        # Add liabilities section
        data.append(['الخصوم', '', ''])
        for item in liability_details:
            data.append(['', item['account'].name, f"{item['balance']:,.2f}"])
        data.append(['', 'إجمالي الخصوم', f"{total_liabilities:,.2f}"])
        data.append(['', '', ''])

        # Add equity section
        data.append(['حقوق الملكية', '', ''])
        for item in equity_details:
            data.append(['', item['account'].name, f"{item['balance']:,.2f}"])
        data.append(['', 'الأرباح المحتجزة', f"{retained_earnings:,.2f}"])
        data.append(['', 'إجمالي حقوق الملكية', f"{total_equity:,.2f}"])
        data.append(['', '', ''])

        # Add total
        data.append(['إجمالي الخصوم وحقوق الملكية', '', f"{total_liabilities_equity:,.2f}"])

        headers = ['البند', 'التفاصيل', 'المبلغ']
        filename = f"balance_sheet_{as_of_date}"
        filepath = create_pdf_report(data, "الميزانية العمومية", filename, headers)

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    elif export_format == 'excel':
        data = []

        # Assets section
        data.append({'البند': 'الأصول', 'التفاصيل': '', 'المبلغ': ''})
        for item in asset_details:
            data.append({'البند': '', 'التفاصيل': item['account'].name, 'المبلغ': item['balance']})
        data.append({'البند': '', 'التفاصيل': 'إجمالي الأصول', 'المبلغ': total_assets})

        # Liabilities section
        data.append({'البند': 'الخصوم', 'التفاصيل': '', 'المبلغ': ''})
        for item in liability_details:
            data.append({'البند': '', 'التفاصيل': item['account'].name, 'المبلغ': item['balance']})
        data.append({'البند': '', 'التفاصيل': 'إجمالي الخصوم', 'المبلغ': total_liabilities})

        # Equity section
        data.append({'البند': 'حقوق الملكية', 'التفاصيل': '', 'المبلغ': ''})
        for item in equity_details:
            data.append({'البند': '', 'التفاصيل': item['account'].name, 'المبلغ': item['balance']})
        data.append({'البند': '', 'التفاصيل': 'الأرباح المحتجزة', 'المبلغ': retained_earnings})
        data.append({'البند': '', 'التفاصيل': 'إجمالي حقوق الملكية', 'المبلغ': total_equity})

        # Total
        data.append({'البند': 'إجمالي الخصوم وحقوق الملكية', 'التفاصيل': '', 'المبلغ': total_liabilities_equity})

        filename = f"balance_sheet_{as_of_date}"
        filepath = export_to_excel(data, filename, "الميزانية العمومية")

        if filepath:
            return send_file(filepath, as_attachment=True)
        else:
            flash('حدث خطأ في تصدير التقرير', 'error')

    # Additional balance sheet components for display
    current_assets = total_assets * 0.6
    fixed_assets = total_assets * 0.4
    current_liabilities = total_liabilities * 0.7
    long_term_liabilities = total_liabilities * 0.3

    # Financial ratios
    current_ratio = current_assets / current_liabilities if current_liabilities > 0 else 0
    debt_to_equity = total_liabilities / total_equity if total_equity > 0 else 0
    debt_ratio = total_liabilities / total_assets if total_assets > 0 else 0
    equity_ratio = total_equity / total_assets if total_assets > 0 else 0

    return render_template('reports/balance_sheet.html',
                         asset_details=asset_details,
                         liability_details=liability_details,
                         equity_details=equity_details,
                         total_assets=total_assets,
                         total_liabilities=total_liabilities,
                         total_equity=total_equity,
                         retained_earnings=retained_earnings,
                         total_liabilities_equity=total_liabilities_equity,
                         balance_difference=balance_difference,
                         current_assets=current_assets,
                         fixed_assets=fixed_assets,
                         current_liabilities=current_liabilities,
                         long_term_liabilities=long_term_liabilities,
                         current_ratio=current_ratio,
                         debt_to_equity=debt_to_equity,
                         debt_ratio=debt_ratio,
                         equity_ratio=equity_ratio,
                         as_of_date=as_of_date)