#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف إدارة قاعدة البيانات
Test Database Management Functions
"""

import requests
import time

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class DatabaseFunctionsTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_database_info(self):
        """اختبار صفحة معلومات قاعدة البيانات"""
        print("\n🔄 اختبار صفحة معلومات قاعدة البيانات...")
        try:
            response = self.session.get(f"{BASE_URL}/settings/database-info")
            
            if response.status_code == 200:
                if "معلومات قاعدة البيانات" in response.text or "Database" in response.text:
                    print("✅ صفحة معلومات قاعدة البيانات تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة معلومات قاعدة البيانات لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة معلومات قاعدة البيانات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة معلومات قاعدة البيانات: {e}")
            return False
    
    def test_analyze_database(self):
        """اختبار تحليل قاعدة البيانات"""
        print("\n🔄 اختبار تحليل قاعدة البيانات...")
        try:
            response = self.session.post(f"{BASE_URL}/settings/analyze-database")
            
            if response.status_code == 200 or response.status_code == 302:
                print("✅ تحليل قاعدة البيانات يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تحليل قاعدة البيانات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحليل قاعدة البيانات: {e}")
            return False
    
    def test_rebuild_indexes(self):
        """اختبار إعادة بناء الفهارس"""
        print("\n🔄 اختبار إعادة بناء الفهارس...")
        try:
            response = self.session.post(f"{BASE_URL}/settings/rebuild-indexes")
            
            if response.status_code == 200 or response.status_code == 302:
                print("✅ إعادة بناء الفهارس يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في إعادة بناء الفهارس: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إعادة بناء الفهارس: {e}")
            return False
    
    def test_vacuum_database(self):
        """اختبار تنظيف قاعدة البيانات"""
        print("\n🔄 اختبار تنظيف قاعدة البيانات...")
        try:
            response = self.session.post(f"{BASE_URL}/settings/vacuum-database")
            
            if response.status_code == 200 or response.status_code == 302:
                print("✅ تنظيف قاعدة البيانات يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تنظيف قاعدة البيانات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تنظيف قاعدة البيانات: {e}")
            return False
    
    def test_check_integrity(self):
        """اختبار فحص سلامة البيانات"""
        print("\n🔄 اختبار فحص سلامة البيانات...")
        try:
            response = self.session.post(f"{BASE_URL}/settings/check-integrity")
            
            if response.status_code == 200 or response.status_code == 302:
                print("✅ فحص سلامة البيانات يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في فحص سلامة البيانات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص سلامة البيانات: {e}")
            return False
    
    def test_compact_database(self):
        """اختبار ضغط قاعدة البيانات"""
        print("\n🔄 اختبار ضغط قاعدة البيانات...")
        try:
            response = self.session.post(f"{BASE_URL}/settings/compact-database")
            
            if response.status_code == 200 or response.status_code == 302:
                print("✅ ضغط قاعدة البيانات يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في ضغط قاعدة البيانات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في ضغط قاعدة البيانات: {e}")
            return False
    
    def test_backup_database(self):
        """اختبار إنشاء نسخة احتياطية"""
        print("\n🔄 اختبار إنشاء نسخة احتياطية...")
        try:
            response = self.session.get(f"{BASE_URL}/settings/backup")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/octet-stream' in content_type or 'application/x-sqlite3' in content_type:
                    file_size = len(response.content)
                    print(f"📁 حجم النسخة الاحتياطية: {file_size} بايت")
                    
                    if file_size > 10000:  # على الأقل 10KB
                        print("✅ إنشاء نسخة احتياطية يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم النسخة الاحتياطية صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في إنشاء نسخة احتياطية: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء نسخة احتياطية: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات وظائف قاعدة البيانات"""
        print("📊 بدء اختبار وظائف إدارة قاعدة البيانات")
        print("=" * 60)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("صفحة معلومات قاعدة البيانات", self.test_database_info),
            ("تحليل قاعدة البيانات", self.test_analyze_database),
            ("إعادة بناء الفهارس", self.test_rebuild_indexes),
            ("تنظيف قاعدة البيانات", self.test_vacuum_database),
            ("فحص سلامة البيانات", self.test_check_integrity),
            ("ضغط قاعدة البيانات", self.test_compact_database),
            ("إنشاء نسخة احتياطية", self.test_backup_database),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(2)  # انتظار ثانيتين بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print("📊 نتائج اختبار وظائف إدارة قاعدة البيانات:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف إدارة قاعدة البيانات تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة في وظائف قاعدة البيانات")
            return False

def main():
    """الدالة الرئيسية"""
    print("📊 اختبار وظائف إدارة قاعدة البيانات - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = DatabaseFunctionsTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
