#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوظائف استيراد المنتجات من Excel
Test Products Import Functions for AutoParts Manager
"""

import os
import sys
import requests
import time
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class ProductsImportTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_products_page(self):
        """اختبار صفحة المنتجات الرئيسية"""
        print("\n🔄 اختبار صفحة المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/")
            if response.status_code == 200:
                print("✅ صفحة المنتجات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في الوصول لصفحة المنتجات: {e}")
            return False
    
    def test_download_template(self):
        """اختبار تحميل قالب استيراد المنتجات"""
        print("\n🔄 اختبار تحميل قالب استيراد المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/download-template")
            if response.status_code == 200:
                print("✅ تحميل قالب استيراد المنتجات يعمل بنجاح")
                
                # حفظ القالب للاختبار
                with open('products_template_test.xlsx', 'wb') as f:
                    f.write(response.content)
                print("✅ تم حفظ القالب للاختبار")
                return True
            else:
                print(f"❌ فشل في تحميل قالب المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تحميل قالب المنتجات: {e}")
            return False
    
    def test_import_page(self):
        """اختبار صفحة استيراد المنتجات"""
        print("\n🔄 اختبار صفحة استيراد المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/import-excel")
            if response.status_code == 200:
                print("✅ صفحة استيراد المنتجات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة الاستيراد: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في الوصول لصفحة الاستيراد: {e}")
            return False
    
    def create_test_excel_file(self):
        """إنشاء ملف Excel للاختبار"""
        print("\n🔄 إنشاء ملف Excel للاختبار...")
        try:
            import pandas as pd
            
            # بيانات اختبار
            test_data = [
                {
                    'الاسم*': 'فلتر زيت تويوتا - اختبار',
                    'الباركود*': 'TEST001',
                    'الفئة': 'فلاتر',
                    'الوحدة*': 'قطعة',
                    'الكمية*': 100,
                    'سعر التكلفة*': 25.50,
                    'سعر البيع*': 35.00,
                    'الحد الأدنى*': 10,
                    'الوصف': 'فلتر زيت أصلي لسيارات تويوتا - اختبار',
                    'الموقع': 'A1-TEST'
                },
                {
                    'الاسم*': 'إطار ميشلان - اختبار',
                    'الباركود*': 'TEST002',
                    'الفئة': 'إطارات',
                    'الوحدة*': 'قطعة',
                    'الكمية*': 50,
                    'سعر التكلفة*': 300.00,
                    'سعر البيع*': 450.00,
                    'الحد الأدنى*': 5,
                    'الوصف': 'إطار ميشلان عالي الجودة - اختبار',
                    'الموقع': 'B2-TEST'
                }
            ]
            
            df = pd.DataFrame(test_data)
            df.to_excel('test_products_import.xlsx', index=False)
            print("✅ تم إنشاء ملف Excel للاختبار")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف الاختبار: {e}")
            return False
    
    def test_import_excel_file(self):
        """اختبار استيراد ملف Excel"""
        print("\n🔄 اختبار استيراد ملف Excel...")
        try:
            # التأكد من وجود الملف
            if not os.path.exists('test_products_import.xlsx'):
                print("❌ ملف الاختبار غير موجود")
                return False
            
            # رفع الملف
            with open('test_products_import.xlsx', 'rb') as f:
                files = {'file': ('test_products_import.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                response = self.session.post(f"{BASE_URL}/products/import-excel", files=files)
            
            if response.status_code == 200 or response.status_code == 302:
                print("✅ استيراد ملف Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في استيراد الملف: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في استيراد الملف: {e}")
            return False
    
    def test_products_export(self):
        """اختبار تصدير المنتجات للتأكد من الاستيراد"""
        print("\n🔄 اختبار تصدير المنتجات للتأكد من الاستيراد...")
        try:
            response = self.session.get(f"{BASE_URL}/products/export")
            if response.status_code == 200 or response.status_code == 302:
                print("✅ تصدير المنتجات يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير المنتجات: {e}")
            return False
    
    def cleanup_test_files(self):
        """تنظيف ملفات الاختبار"""
        print("\n🧹 تنظيف ملفات الاختبار...")
        try:
            files_to_remove = [
                'test_products_import.xlsx',
                'products_template_test.xlsx'
            ]
            
            for file in files_to_remove:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"✅ تم حذف {file}")
            
            return True
        except Exception as e:
            print(f"❌ خطأ في تنظيف الملفات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار وظائف استيراد المنتجات")
        print("=" * 60)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("اختبار صفحة المنتجات", self.test_products_page),
            ("اختبار تحميل القالب", self.test_download_template),
            ("اختبار صفحة الاستيراد", self.test_import_page),
            ("إنشاء ملف اختبار", self.create_test_excel_file),
            ("اختبار استيراد Excel", self.test_import_excel_file),
            ("اختبار تصدير المنتجات", self.test_products_export),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        # تنظيف الملفات
        self.cleanup_test_files()
        
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف استيراد المنتجات تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️  يوجد {failed} وظيفة تحتاج إلى إصلاح")
            return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار وظائف استيراد المنتجات - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    # التحقق من وجود pandas
    try:
        import pandas as pd
        print("✅ مكتبة pandas متوفرة")
    except ImportError:
        print("❌ مكتبة pandas غير متوفرة. يرجى تثبيتها أولاً:")
        print("pip install pandas openpyxl")
        sys.exit(1)
    
    tester = ProductsImportTester()
    success = tester.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
