@echo off
title AutoParts Manager V5.1 - Installation

echo.
echo ============================================================
echo AutoParts Manager V5.1 - Installation
echo ============================================================
echo.
echo This program will install the complete system on your computer
echo including all requirements and necessary configurations
echo.
echo Make sure you have internet connection before continuing
echo.
pause

echo.
echo ============================================================
echo 🔍 فحص متطلبات النظام...
echo ============================================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من الرابط التالي:
    echo    https://www.python.org/downloads/
    echo.
    echo 💡 تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
python --version

REM Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متوفر
    echo 📥 جاري تثبيت pip...
    python -m ensurepip --upgrade
)

echo ✅ pip متوفر بنجاح
pip --version

echo.
echo ============================================================
echo 📦 إنشاء البيئة الافتراضية...
echo ============================================================

REM Create virtual environment
if exist "venv" (
    echo 🔄 البيئة الافتراضية موجودة، جاري حذفها وإعادة إنشائها...
    rmdir /s /q venv
)

echo 🔨 إنشاء بيئة افتراضية جديدة...
python -m venv venv

if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم إنشاء البيئة الافتراضية بنجاح

echo.
echo ============================================================
echo 🔧 تفعيل البيئة الافتراضية وتثبيت المتطلبات...
echo ============================================================

REM Activate virtual environment and install requirements
call venv\Scripts\activate.bat

echo 🔄 ترقية pip إلى أحدث إصدار...
python -m pip install --upgrade pip

echo 📦 تثبيت المتطلبات الأساسية...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    echo 🔄 محاولة تثبيت المتطلبات واحداً تلو الآخر...

    pip install Flask==2.3.3
    pip install Flask-SQLAlchemy==3.0.5
    pip install Flask-Login==0.6.3
    pip install Werkzeug==2.3.7
    pip install SQLAlchemy==2.0.21
    pip install Jinja2==3.1.2
    pip install MarkupSafe==2.1.3
    pip install itsdangerous==2.1.2
    pip install click==8.1.7
    pip install blinker==1.6.3
    pip install python-barcode==0.15.1
    pip install Pillow==10.0.1
    pip install reportlab==4.0.4
    pip install openpyxl==3.1.2
    pip install pandas==2.1.1
    pip install PyInstaller==6.0.0
    pip install hijri-converter==2.3.1
    pip install python-dateutil==2.8.2
    pip install requests==2.31.0
)

echo ✅ تم تثبيت جميع المتطلبات بنجاح

echo.
echo ============================================================
echo 🗄️ إعداد قاعدة البيانات...
echo ============================================================

REM Initialize database if it doesn't exist
if not exist "autoparts.db" (
    echo 🔨 إنشاء قاعدة البيانات الأولية...
    python init_db.py

    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        echo 🔄 محاولة إنشاء قاعدة البيانات يدوياً...
        python -c "from app import app, db; app.app_context().push(); db.create_all(); print('تم إنشاء قاعدة البيانات بنجاح')"
    )

    echo ✅ تم إنشاء قاعدة البيانات بنجاح
) else (
    echo ✅ قاعدة البيانات موجودة مسبقاً
)

echo.
echo ============================================================
echo 📁 إنشاء المجلدات المطلوبة...
echo ============================================================

REM Create required directories
if not exist "exports" mkdir exports
if not exist "exports\reports" mkdir exports\reports
if not exist "exports\excel" mkdir exports\excel
if not exist "backups" mkdir backups
if not exist "uploads" mkdir uploads
if not exist "static\uploads" mkdir static\uploads
if not exist "logs" mkdir logs

echo ✅ تم إنشاء جميع المجلدات المطلوبة

echo.
echo ============================================================
echo 🧪 اختبار النظام...
echo ============================================================

echo 🔄 اختبار تشغيل النظام...
timeout /t 2 /nobreak >nul

REM Test if the system can start
python -c "from app import app; print('✅ النظام جاهز للتشغيل')" 2>nul

if %errorlevel% neq 0 (
    echo ❌ خطأ في اختبار النظام
    echo 🔍 فحص الأخطاء...
    python -c "from app import app; print('النظام يعمل')"
    pause
) else (
    echo ✅ اختبار النظام ناجح
)

echo.
echo ============================================================
echo 🎉 تم تثبيت النظام بنجاح!
echo ============================================================
echo.
echo 📋 معلومات النظام:
echo    📁 مجلد النظام: %CD%
echo    🐍 Python:
python --version
echo    🌐 عنوان النظام: http://127.0.0.1:8888
echo    👤 المستخدم الافتراضي: admin
echo    🔐 كلمة المرور الافتراضية: admin123
echo.
echo 🚀 لتشغيل النظام:
echo    1. انقر نقراً مزدوجاً على ملف "start.bat"
echo    أو
echo    2. افتح موجه الأوامر في هذا المجلد وشغل:
echo       venv\Scripts\activate
echo       python app.py
echo.
echo 📖 لمزيد من المعلومات، راجع ملف README.md
echo.
echo ============================================================
echo 💡 نصائح مهمة:
echo ============================================================
echo.
echo 🔥 للحصول على أفضل أداء:
echo    • تأكد من إغلاق برامج مكافح الفيروسات مؤقتاً أثناء التشغيل
echo    • استخدم متصفح Chrome أو Firefox للحصول على أفضل تجربة
echo    • تأكد من وجود مساحة كافية على القرص الصلب (على الأقل 1GB)
echo.
echo 🛡️ للأمان:
echo    • غيّر كلمة مرور المدير بعد أول تسجيل دخول
echo    • قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
echo    • لا تشارك بيانات الدخول مع أشخاص غير مخولين
echo.
echo 🔧 للصيانة:
echo    • استخدم أدوات إدارة قاعدة البيانات في الإعدادات
echo    • راقب حجم ملفات السجلات والتقارير
echo    • قم بتحديث النظام عند توفر إصدارات جديدة
echo.
echo ============================================================

pause
echo.
echo 🎊 شكراً لاستخدام نظام إدارة قطع الغيار!
echo    نتمنى لك تجربة ممتعة ومفيدة
echo ============================================================
pause
