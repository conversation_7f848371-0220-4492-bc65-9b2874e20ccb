# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from models import (db, Product, InventoryTransaction, StockAdjustment, StockTransfer,
                   StockTransferItem, LowStockAlert, Category)
from utils import login_required, permission_required, log_audit, generate_invoice_number
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
@login_required
@permission_required('inventory')
def index():
    # Get inventory summary
    total_products = Product.query.filter_by(is_active=True).count()
    low_stock_count = Product.query.filter(
        Product.is_active == True,
        Product.quantity <= Product.min_quantity
    ).count()

    # Get total inventory value
    total_value = db.session.query(
        func.sum(Product.quantity * Product.cost_price)
    ).filter_by(is_active=True).scalar() or 0

    # Recent transactions
    recent_transactions = InventoryTransaction.query.order_by(
        InventoryTransaction.transaction_date.desc()
    ).limit(10).all()

    # Low stock alerts
    low_stock_products = Product.query.filter(
        Product.is_active == True,
        Product.quantity <= Product.min_quantity
    ).limit(5).all()

    return render_template('inventory/index.html',
                         total_products=total_products,
                         low_stock_count=low_stock_count,
                         total_value=total_value,
                         recent_transactions=recent_transactions,
                         low_stock_products=low_stock_products)

@inventory_bp.route('/transactions')
@login_required
@permission_required('inventory')
def transactions():
    page = request.args.get('page', 1, type=int)
    product_id = request.args.get('product_id', type=int)
    transaction_type = request.args.get('transaction_type')
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    query = InventoryTransaction.query

    if product_id:
        query = query.filter_by(product_id=product_id)
    if transaction_type:
        query = query.filter_by(transaction_type=transaction_type)
    if from_date:
        query = query.filter(InventoryTransaction.transaction_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        query = query.filter(InventoryTransaction.transaction_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    transactions = query.order_by(InventoryTransaction.transaction_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()

    return render_template('inventory/transactions.html',
                         transactions=transactions,
                         products=products,
                         product_id=product_id,
                         transaction_type=transaction_type,
                         from_date=from_date,
                         to_date=to_date)

@inventory_bp.route('/adjustments')
@login_required
@permission_required('inventory')
def adjustments():
    page = request.args.get('page', 1, type=int)

    adjustments = StockAdjustment.query.order_by(
        StockAdjustment.adjustment_date.desc()
    ).paginate(page=page, per_page=20, error_out=False)

    return render_template('inventory/adjustments.html', adjustments=adjustments)

@inventory_bp.route('/add-adjustment', methods=['GET', 'POST'])
@login_required
@permission_required('inventory')
def add_adjustment():
    if request.method == 'POST':
        product_id = request.form.get('product_id', type=int)
        new_quantity = request.form.get('new_quantity', type=int)
        reason = request.form.get('reason')
        notes = request.form.get('notes', '')

        if not all([product_id, new_quantity is not None, reason]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
            return render_template('inventory/add_adjustment.html', products=products)

        product = Product.query.get_or_404(product_id)
        old_quantity = product.quantity
        adjustment_quantity = new_quantity - old_quantity

        if adjustment_quantity == 0:
            flash('لا يوجد تغيير في الكمية', 'warning')
            products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
            return render_template('inventory/add_adjustment.html', products=products)

        try:
            # Create adjustment record
            adjustment_number = generate_invoice_number('ADJ')
            adjustment = StockAdjustment(
                adjustment_number=adjustment_number,
                product_id=product_id,
                old_quantity=old_quantity,
                new_quantity=new_quantity,
                adjustment_quantity=adjustment_quantity,
                reason=reason,
                notes=notes,
                user_id=session['user_id'],
                adjustment_date=datetime.now()
            )

            db.session.add(adjustment)

            # Update product quantity
            product.quantity = new_quantity

            # Create inventory transaction
            transaction = InventoryTransaction(
                product_id=product_id,
                transaction_type='adjustment',
                quantity=abs(adjustment_quantity),
                reference_type='adjustment',
                reference_id=adjustment.id,
                notes=f'تعديل مخزون: {reason}',
                user_id=session['user_id'],
                transaction_date=datetime.now(),
                balance_after=new_quantity
            )

            db.session.add(transaction)
            db.session.commit()

            # Log audit
            log_audit(session['user_id'], 'تعديل مخزون', 'stock_adjustments', adjustment.id,
                     new_values={'product': product.name, 'old_qty': old_quantity, 'new_qty': new_quantity})

            flash('تم تعديل المخزون بنجاح', 'success')
            return redirect(url_for('inventory.adjustments'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    products = Product.query.filter_by(is_active=True).order_by(Product.name).all()
    return render_template('inventory/add_adjustment.html', products=products)

@inventory_bp.route('/transfers')
@login_required
@permission_required('inventory')
def transfers():
    page = request.args.get('page', 1, type=int)

    transfers = StockTransfer.query.order_by(
        StockTransfer.transfer_date.desc()
    ).paginate(page=page, per_page=20, error_out=False)

    return render_template('inventory/transfers.html', transfers=transfers)

@inventory_bp.route('/low-stock')
@login_required
@permission_required('inventory')
def low_stock():
    products = Product.query.filter(
        Product.is_active == True,
        Product.quantity <= Product.min_quantity
    ).order_by(Product.quantity).all()

    return render_template('inventory/low_stock.html', products=products)

@inventory_bp.route('/product-history/<int:product_id>')
@login_required
@permission_required('inventory')
def product_history(product_id):
    product = Product.query.get_or_404(product_id)

    transactions = InventoryTransaction.query.filter_by(
        product_id=product_id
    ).order_by(InventoryTransaction.transaction_date.desc()).all()

    return render_template('inventory/product_history.html',
                         product=product,
                         transactions=transactions)

@inventory_bp.route('/api/product-info/<int:product_id>')
@login_required
@permission_required('inventory')
def api_product_info(product_id):
    product = Product.query.get_or_404(product_id)

    return jsonify({
        'id': product.id,
        'name': product.name,
        'current_quantity': product.quantity,
        'min_quantity': product.min_quantity,
        'unit': product.unit,
        'location': product.location or ''
    })

@inventory_bp.route('/reports')
@login_required
@permission_required('inventory')
def reports():
    # Inventory valuation
    products = Product.query.filter_by(is_active=True).all()

    inventory_data = []
    total_value = 0

    for product in products:
        value = product.quantity * product.cost_price
        total_value += value

        inventory_data.append({
            'product': product,
            'value': value,
            'status': 'منخفض' if product.is_low_stock else 'طبيعي'
        })

    # Sort by value descending
    inventory_data.sort(key=lambda x: x['value'], reverse=True)

    return render_template('inventory/reports.html',
                         inventory_data=inventory_data,
                         total_value=total_value)
