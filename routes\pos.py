# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from models import db, Product, Customer, Sale, SaleItem, CashTransaction, JournalEntry, Account
from utils import login_required, permission_required, generate_invoice_number, log_audit, calculate_tax
from datetime import datetime

pos_bp = Blueprint('pos', __name__)

@pos_bp.route('/')
@login_required
@permission_required('pos')
def index():
    return render_template('pos/index.html')

@pos_bp.route('/search-product')
@login_required
@permission_required('pos')
def search_product():
    query = request.args.get('q', '')
    search_type = request.args.get('type', 'name')  # name or barcode

    if len(query) < 2:
        return jsonify([])

    if search_type == 'barcode':
        products = Product.query.filter(
            Product.is_active == True,
            Product.barcode == query
        ).limit(1).all()
    else:
        products = Product.query.filter(
            Product.is_active == True,
            Product.name.contains(query)
        ).limit(10).all()

    results = []
    for product in products:
        results.append({
            'id': product.id,
            'name': product.name,
            'barcode': product.barcode,
            'price': product.selling_price,
            'quantity': product.quantity,
            'unit': product.unit
        })

    return jsonify(results)

@pos_bp.route('/create-sale', methods=['POST'])
@login_required
@permission_required('pos')
def create_sale():
    try:
        data = request.get_json()

        customer_id = data.get('customer_id')
        items = data.get('items', [])
        payment_method = data.get('payment_method', 'cash')
        discount_rate = float(data.get('discount_rate', 0))
        tax_rate = float(data.get('tax_rate', 0.15))
        notes = data.get('notes', '')

        if not items:
            return jsonify({'success': False, 'message': 'لا توجد منتجات في الفاتورة'})

        # Calculate totals
        subtotal = 0
        for item in items:
            product = Product.query.get(item['product_id'])
            if not product or product.quantity < item['quantity']:
                return jsonify({'success': False, 'message': f'كمية غير كافية للمنتج {product.name if product else "غير موجود"}'})

            item_total = item['quantity'] * item['unit_price']
            subtotal += item_total

        discount_amount = subtotal * (discount_rate / 100)
        taxable_amount = subtotal - discount_amount
        tax_amount = taxable_amount * tax_rate
        total_amount = taxable_amount + tax_amount

        # Create sale
        sale = Sale(
            invoice_number=generate_invoice_number('INV'),
            customer_id=customer_id if customer_id else None,
            user_id=session['user_id'],
            subtotal=subtotal,
            tax_rate=tax_rate,
            tax_amount=tax_amount,
            discount_rate=discount_rate,
            discount_amount=discount_amount,
            total_amount=total_amount,
            paid_amount=total_amount,
            payment_method=payment_method,
            notes=notes
        )

        db.session.add(sale)
        db.session.flush()  # Get the sale ID

        # Create sale items and update inventory
        for item in items:
            product = Product.query.get(item['product_id'])

            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['quantity'] * item['unit_price']
            )

            db.session.add(sale_item)

            # Update product quantity
            product.quantity -= item['quantity']

        # Create cash transaction
        cash_transaction = CashTransaction(
            transaction_type='sale',
            amount=total_amount,
            description=f'مبيعات فاتورة رقم {sale.invoice_number}',
            reference_type='sale',
            reference_id=sale.id,
            user_id=session['user_id']
        )
        db.session.add(cash_transaction)

        # Create journal entries
        create_sale_journal_entries(sale)

        db.session.commit()

        # Log audit
        log_audit(session['user_id'], 'إنشاء فاتورة بيع', 'sales', sale.id,
                 new_values={'invoice_number': sale.invoice_number, 'total_amount': total_amount})

        return jsonify({
            'success': True,
            'message': 'تم إنشاء الفاتورة بنجاح',
            'sale_id': sale.id,
            'invoice_number': sale.invoice_number
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

def create_sale_journal_entries(sale):
    """Create journal entries for sale"""
    try:
        # Get accounts
        cash_account = Account.query.filter_by(code='1110').first()  # Cash
        sales_account = Account.query.filter_by(code='4100').first()  # Sales
        tax_account = Account.query.filter_by(code='2120').first()  # Tax Payable

        if not all([cash_account, sales_account]):
            return

        # Generate unique entry numbers for each journal entry
        cash_entry_number = generate_invoice_number('JE')

        # Debit Cash
        cash_entry = JournalEntry(
            entry_number=cash_entry_number,
            account_id=cash_account.id,
            transaction_date=sale.sale_date,
            description=f'مبيعات فاتورة رقم {sale.invoice_number}',
            debit_amount=sale.total_amount,
            credit_amount=0,
            reference_type='sale',
            reference_id=sale.id,
            user_id=sale.user_id
        )
        db.session.add(cash_entry)

        # Credit Sales - Generate unique entry number
        sales_entry_number = generate_invoice_number('JE')
        sales_entry = JournalEntry(
            entry_number=sales_entry_number,
            account_id=sales_account.id,
            transaction_date=sale.sale_date,
            description=f'مبيعات فاتورة رقم {sale.invoice_number}',
            debit_amount=0,
            credit_amount=sale.subtotal - sale.discount_amount,
            reference_type='sale',
            reference_id=sale.id,
            user_id=sale.user_id
        )
        db.session.add(sales_entry)

        # Credit Tax if applicable - Generate unique entry number
        if sale.tax_amount > 0 and tax_account:
            tax_entry_number = generate_invoice_number('JE')
            tax_entry = JournalEntry(
                entry_number=tax_entry_number,
                account_id=tax_account.id,
                transaction_date=sale.sale_date,
                description=f'ضريبة فاتورة رقم {sale.invoice_number}',
                debit_amount=0,
                credit_amount=sale.tax_amount,
                reference_type='sale',
                reference_id=sale.id,
                user_id=sale.user_id
            )
            db.session.add(tax_entry)

        # Update account balances
        cash_account.balance += sale.total_amount
        sales_account.balance += (sale.subtotal - sale.discount_amount)
        if tax_account and sale.tax_amount > 0:
            tax_account.balance += sale.tax_amount

    except Exception as e:
        print(f"Error creating journal entries: {e}")

@pos_bp.route('/invoice/<int:sale_id>')
@login_required
@permission_required('pos')
def invoice(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    return render_template('pos/invoice.html', sale=sale)

@pos_bp.route('/print-invoice/<int:sale_id>')
@login_required
@permission_required('pos')
def print_invoice(sale_id):
    sale = Sale.query.get_or_404(sale_id)
    return render_template('pos/print_invoice.html', sale=sale)

@pos_bp.route('/export-invoice-pdf/<int:sale_id>')
@login_required
@permission_required('pos')
def export_invoice_pdf(sale_id):
    sale = Sale.query.get_or_404(sale_id)

    # Prepare data for PDF
    data = []
    data.append(['رقم الفاتورة', sale.invoice_number, ''])
    data.append(['التاريخ', sale.sale_date.strftime('%Y-%m-%d %H:%M'), ''])
    data.append(['العميل', sale.customer.name if sale.customer else 'عميل نقدي', ''])
    data.append(['', '', ''])
    data.append(['المنتج', 'الكمية', 'السعر', 'الإجمالي'])

    for item in sale.items:
        data.append([
            item.product.name,
            f"{item.quantity} {item.product.unit}",
            f"{item.unit_price:,.2f} ر.س",
            f"{item.total_price:,.2f} ر.س"
        ])

    data.append(['', '', '', ''])
    data.append(['المجموع الفرعي', '', '', f"{sale.subtotal:,.2f} ر.س"])
    if sale.discount_amount > 0:
        data.append(['الخصم', '', '', f"{sale.discount_amount:,.2f} ر.س"])
    data.append(['الضريبة (15%)', '', '', f"{sale.tax_amount:,.2f} ر.س"])
    data.append(['الإجمالي النهائي', '', '', f"{sale.total_amount:,.2f} ر.س"])

    headers = ['البند', 'التفاصيل', 'السعر', 'الإجمالي']
    filename = f"invoice_{sale.invoice_number}"

    from utils import create_pdf_report
    filepath = create_pdf_report(data, f"فاتورة رقم {sale.invoice_number}", filename, headers)

    if filepath:
        return send_file(filepath, as_attachment=True)
    else:
        flash('حدث خطأ في تصدير الفاتورة', 'error')
        return redirect(url_for('pos.invoice', sale_id=sale_id))

@pos_bp.route('/sales-history')
@login_required
@permission_required('pos')
def sales_history():
    page = request.args.get('page', 1, type=int)
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    query = Sale.query

    # Filter by user if cashier
    from models import User
    user = User.query.get(session['user_id'])
    if user and user.role == 'cashier':
        query = query.filter_by(user_id=session['user_id'])

    if from_date:
        query = query.filter(Sale.sale_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        query = query.filter(Sale.sale_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    sales = query.order_by(Sale.sale_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('pos/sales_history.html',
                         sales=sales,
                         from_date=from_date,
                         to_date=to_date)

@pos_bp.route('/export-sales')
@login_required
@permission_required('pos')
def export_sales():
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    query = Sale.query

    # Filter by user if cashier
    from models import User
    user = User.query.get(session['user_id'])
    if user and user.role == 'cashier':
        query = query.filter_by(user_id=session['user_id'])

    if from_date:
        query = query.filter(Sale.sale_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        query = query.filter(Sale.sale_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    sales = query.order_by(Sale.sale_date.desc()).all()

    data = []
    for sale in sales:
        data.append({
            'رقم الفاتورة': sale.invoice_number,
            'التاريخ': sale.sale_date.strftime('%Y-%m-%d %H:%M'),
            'العميل': sale.customer.name if sale.customer else 'عميل نقدي',
            'المجموع الفرعي': sale.subtotal,
            'الخصم': sale.discount_amount,
            'الضريبة': sale.tax_amount,
            'الإجمالي': sale.total_amount,
            'طريقة الدفع': sale.payment_method,
            'الكاشير': sale.user.full_name
        })

    filename = f"sales_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    if from_date and to_date:
        filename = f"sales_report_{from_date}_to_{to_date}"

    from utils import export_to_excel
    filepath = export_to_excel(data, filename, "تقرير المبيعات")

    if filepath:
        return send_file(filepath, as_attachment=True)
    else:
        flash('حدث خطأ في تصدير التقرير', 'error')
        return redirect(url_for('pos.sales_history'))
