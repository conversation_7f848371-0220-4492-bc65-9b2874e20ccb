{% extends "base.html" %}

{% block title %}خطأ في الخادم - 500{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="text-center">
                <div class="error-page">
                    <h1 class="display-1 text-danger">500</h1>
                    <h2 class="mb-4">خطأ في الخادم</h2>
                    <p class="lead text-muted mb-4">
                        عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.
                    </p>
                    
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
                    </div>
                    
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        إذا استمر هذا الخطأ، يرجى التواصل مع الدعم الفني.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                        <button onclick="location.reload()" class="btn btn-outline-secondary">
                            <i class="fas fa-redo me-2"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-page {
    padding: 60px 0;
}

.error-page h1 {
    font-size: 8rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-page h2 {
    color: #6c757d;
}
</style>
{% endblock %}
