{% extends "base.html" %}

{% block title %}فاتورة رقم {{ sale.invoice_number }} - نظام إدارة قطع الغيار{% endblock %}

{% block extra_css %}
<style>
    .invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px 15px 0 0;
    }

    .invoice-details {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;
    }

    .invoice-table {
        margin: 20px 0;
    }

    .invoice-total {
        background: #e9ecef;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .invoice-container {
            box-shadow: none !important;
            border: none !important;
        }

        body {
            background: white !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="no-print mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="h3 mb-0">
            <i class="fas fa-receipt me-2"></i>
            فاتورة رقم {{ sale.invoice_number }}
        </h1>
        <div>
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
            <a href="{{ url_for('pos.export_invoice_pdf', sale_id=sale.id) }}" class="btn btn-danger me-2">
                <i class="fas fa-file-pdf me-1"></i>
                تصدير PDF
            </a>
            <a href="{{ url_for('pos.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لنقطة البيع
            </a>
        </div>
    </div>
</div>

<div class="invoice-container card">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="row">
            <div class="col-md-6">
                <h2 class="mb-3">
                    <i class="fas fa-car me-2"></i>
                    شركة قطع الغيار
                </h2>
                <p class="mb-1">الرياض، المملكة العربية السعودية</p>
                <p class="mb-1">هاتف: +966 11 123 4567</p>
                <p class="mb-0">البريد: <EMAIL></p>
            </div>
            <div class="col-md-6 text-md-end">
                <h3 class="mb-3">فاتورة بيع</h3>
                <p class="mb-1"><strong>رقم الفاتورة:</strong> {{ sale.invoice_number }}</p>
                <p class="mb-1"><strong>التاريخ:</strong> {{ sale.sale_date.strftime('%Y-%m-%d') }}</p>
                <p class="mb-1"><strong>الوقت:</strong> {{ sale.sale_date.strftime('%H:%M') }}</p>
                <p class="mb-0"><strong>الكاشير:</strong> {{ sale.user.full_name }}</p>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Customer Details -->
        <div class="invoice-details">
            <h5 class="mb-3">
                <i class="fas fa-user me-2"></i>
                بيانات العميل
            </h5>
            <div class="row">
                <div class="col-md-6">
                    {% if sale.customer %}
                    <p class="mb-1"><strong>الاسم:</strong> {{ sale.customer.name }}</p>
                    <p class="mb-1"><strong>الهاتف:</strong> {{ sale.customer.phone or 'غير محدد' }}</p>
                    <p class="mb-0"><strong>النوع:</strong> {{ sale.customer.customer_type }}</p>
                    {% else %}
                    <p class="mb-0"><strong>عميل نقدي</strong></p>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>طريقة الدفع:</strong>
                        {% if sale.payment_method == 'cash' %}نقدي
                        {% elif sale.payment_method == 'credit' %}آجل
                        {% elif sale.payment_method == 'bank' %}بنكي
                        {% else %}{{ sale.payment_method }}
                        {% endif %}
                    </p>
                    <p class="mb-0"><strong>الحالة:</strong>
                        <span class="badge bg-success">مكتملة</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <div class="invoice-table">
            <h5 class="mb-3">
                <i class="fas fa-list me-2"></i>
                تفاصيل المنتجات
            </h5>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>اسم المنتج</th>
                            <th>الباركود</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in sale.items %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ item.product.name }}</td>
                            <td><code>{{ item.product.barcode }}</code></td>
                            <td>{{ item.quantity }} {{ item.product.unit }}</td>
                            <td>{{ item.unit_price|currency }}</td>
                            <td>{{ item.total_price|currency }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Invoice Totals -->
        <div class="row">
            <div class="col-md-6">
                {% if sale.notes %}
                <div class="invoice-details">
                    <h6 class="mb-2">
                        <i class="fas fa-sticky-note me-2"></i>
                        ملاحظات
                    </h6>
                    <p class="mb-0">{{ sale.notes }}</p>
                </div>
                {% endif %}
            </div>
            <div class="col-md-6">
                <div class="invoice-total">
                    <table class="table table-borderless mb-0">
                        <tr>
                            <td><strong>المجموع الفرعي:</strong></td>
                            <td class="text-end"><strong>{{ sale.subtotal|currency }}</strong></td>
                        </tr>
                        {% if sale.discount_amount > 0 %}
                        <tr>
                            <td>الخصم ({{ sale.discount_rate }}%):</td>
                            <td class="text-end text-danger">- {{ sale.discount_amount|currency }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td>الضريبة ({{ (sale.tax_rate * 100)|round(1) }}%):</td>
                            <td class="text-end">{{ sale.tax_amount|currency }}</td>
                        </tr>
                        <tr class="border-top">
                            <td><h5><strong>الإجمالي النهائي:</strong></h5></td>
                            <td class="text-end"><h5><strong>{{ sale.total_amount|currency }}</strong></h5></td>
                        </tr>
                        <tr>
                            <td>المبلغ المدفوع:</td>
                            <td class="text-end text-success">{{ sale.paid_amount|currency }}</td>
                        </tr>
                        {% if sale.remaining_amount > 0 %}
                        <tr>
                            <td class="text-danger">المبلغ المتبقي:</td>
                            <td class="text-end text-danger">{{ sale.remaining_amount|currency }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-4 pt-4 border-top">
            <p class="text-muted mb-1">شكراً لتعاملكم معنا</p>
            <p class="text-muted mb-0">{{ sale.sale_date|hijri }}</p>
        </div>
    </div>
</div>

<!-- QR Code (if available) -->
<div class="text-center mt-3 no-print">
    <small class="text-muted">
        يمكنك طباعة هذه الفاتورة أو حفظها كملف PDF
    </small>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto print if requested
const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get('print') === 'true') {
    window.onload = function() {
        setTimeout(function() {
            window.print();
        }, 1000);
    };
}

// Print function
function printInvoice() {
    window.print();
}

// Save as PDF (if browser supports it)
function saveAsPDF() {
    if (window.chrome) {
        window.print();
    } else {
        alert('استخدم خيار الطباعة واختر "حفظ كـ PDF" من قائمة الطابعات');
    }
}
</script>
{% endblock %}
