{% extends "base.html" %}

{% block title %}نقطة البيع - نظام إدارة قطع الغيار{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .product-search {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .cart-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    
    .cart-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .cart-total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .quantity-control {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .quantity-control button {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        border: none;
        background: #667eea;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .quantity-control input {
        width: 60px;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px;
    }
    
    .payment-methods {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .payment-method {
        flex: 1;
        padding: 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .payment-method.active {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }
    
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    .search-result-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .search-result-item:hover {
        background: #f8f9fa;
    }
    
    .search-result-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-cash-register me-2"></i>
        نقطة البيع
    </h1>
    <div>
        <button class="btn btn-outline-primary me-2" onclick="clearCart()">
            <i class="fas fa-trash me-1"></i>
            مسح الكل
        </button>
        <a href="{{ url_for('pos.sales_history') }}" class="btn btn-outline-secondary">
            <i class="fas fa-history me-1"></i>
            سجل المبيعات
        </a>
    </div>
</div>

<div class="row">
    <!-- Product Search & Cart -->
    <div class="col-lg-8">
        <!-- Product Search -->
        <div class="product-search">
            <h5 class="mb-3">
                <i class="fas fa-search me-2"></i>
                البحث عن المنتجات
            </h5>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="position-relative">
                        <input type="text" class="form-control" id="productSearch" 
                               placeholder="ابحث بالاسم أو امسح الباركود..." autocomplete="off">
                        <div id="searchResults" class="search-results" style="display: none;"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100" onclick="searchByBarcode()">
                        <i class="fas fa-barcode me-1"></i>
                        بحث بالباركود
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Shopping Cart -->
        <div class="pos-container">
            <h5 class="mb-3">
                <i class="fas fa-shopping-cart me-2"></i>
                سلة التسوق
                <span class="badge bg-primary ms-2" id="cartCount">0</span>
            </h5>
            
            <div id="cartItems">
                <div class="text-center py-5 text-muted">
                    <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                    <p>السلة فارغة. ابحث عن المنتجات لإضافتها</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Order Summary & Payment -->
    <div class="col-lg-4">
        <div class="pos-container">
            <h5 class="mb-3">
                <i class="fas fa-calculator me-2"></i>
                ملخص الطلب
            </h5>
            
            <!-- Customer Selection -->
            <div class="mb-3">
                <label class="form-label">العميل</label>
                <select class="form-select" id="customerId">
                    <option value="">عميل نقدي</option>
                    <!-- Will be populated via AJAX -->
                </select>
            </div>
            
            <!-- Order Totals -->
            <div class="border rounded p-3 mb-3">
                <div class="d-flex justify-content-between mb-2">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0.00 ريال</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>الخصم:</span>
                    <div class="d-flex align-items-center">
                        <input type="number" class="form-control form-control-sm me-2" 
                               id="discountRate" style="width: 60px;" min="0" max="100" value="0">
                        <span>%</span>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>مبلغ الخصم:</span>
                    <span id="discountAmount">0.00 ريال</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>الضريبة (15%):</span>
                    <span id="taxAmount">0.00 ريال</span>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between fw-bold fs-5">
                    <span>الإجمالي:</span>
                    <span id="totalAmount">0.00 ريال</span>
                </div>
            </div>
            
            <!-- Payment Methods -->
            <div class="mb-3">
                <label class="form-label">طريقة الدفع</label>
                <div class="payment-methods">
                    <div class="payment-method active" data-method="cash">
                        <i class="fas fa-money-bill-wave d-block mb-1"></i>
                        <small>نقدي</small>
                    </div>
                    <div class="payment-method" data-method="credit">
                        <i class="fas fa-credit-card d-block mb-1"></i>
                        <small>آجل</small>
                    </div>
                    <div class="payment-method" data-method="bank">
                        <i class="fas fa-university d-block mb-1"></i>
                        <small>بنكي</small>
                    </div>
                </div>
            </div>
            
            <!-- Notes -->
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea class="form-control" id="orderNotes" rows="2" placeholder="ملاحظات إضافية..."></textarea>
            </div>
            
            <!-- Action Buttons -->
            <div class="d-grid gap-2">
                <button class="btn btn-success btn-lg" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check me-2"></i>
                    إتمام البيع
                </button>
                
                <button class="btn btn-outline-primary" onclick="holdSale()">
                    <i class="fas fa-pause me-2"></i>
                    تعليق الطلب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="barcodeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">مسح الباركود</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="text" class="form-control" id="barcodeInput" 
                       placeholder="امسح الباركود أو اكتبه..." autofocus>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="searchByBarcodeValue()">بحث</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let cart = [];
let selectedPaymentMethod = 'cash';

// Initialize
$(document).ready(function() {
    loadCustomers();
    updateCartDisplay();
    
    // Product search
    $('#productSearch').on('input', function() {
        const query = $(this).val();
        if (query.length >= 2) {
            searchProducts(query);
        } else {
            $('#searchResults').hide();
        }
    });
    
    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#productSearch, #searchResults').length) {
            $('#searchResults').hide();
        }
    });
    
    // Payment method selection
    $('.payment-method').on('click', function() {
        $('.payment-method').removeClass('active');
        $(this).addClass('active');
        selectedPaymentMethod = $(this).data('method');
    });
    
    // Discount calculation
    $('#discountRate').on('input', function() {
        updateTotals();
    });
    
    // Barcode modal
    $('#barcodeModal').on('shown.bs.modal', function() {
        $('#barcodeInput').focus();
    });
    
    $('#barcodeInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchByBarcodeValue();
        }
    });
});

function loadCustomers() {
    // Load customers for selection
    $.get('/customers/search?q=', function(data) {
        const select = $('#customerId');
        select.empty().append('<option value="">عميل نقدي</option>');
        
        data.forEach(function(customer) {
            select.append(`<option value="${customer.id}">${customer.name}</option>`);
        });
    });
}

function searchProducts(query) {
    $.get('/products/search', { q: query }, function(data) {
        const results = $('#searchResults');
        results.empty();
        
        if (data.length > 0) {
            data.forEach(function(product) {
                const item = $(`
                    <div class="search-result-item" onclick="addToCart(${product.id}, '${product.name}', ${product.price}, ${product.quantity})">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>${product.name}</strong>
                                <br>
                                <small class="text-muted">الباركود: ${product.barcode}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">${product.price.toFixed(2)} ريال</div>
                                <small class="text-muted">متوفر: ${product.quantity}</small>
                            </div>
                        </div>
                    </div>
                `);
                results.append(item);
            });
            results.show();
        } else {
            results.html('<div class="search-result-item text-center text-muted">لا توجد نتائج</div>').show();
        }
    });
}

function searchByBarcode() {
    $('#barcodeModal').modal('show');
}

function searchByBarcodeValue() {
    const barcode = $('#barcodeInput').val();
    if (barcode) {
        $.get('/pos/search-product', { q: barcode, type: 'barcode' }, function(data) {
            if (data.length > 0) {
                const product = data[0];
                addToCart(product.id, product.name, product.price, product.quantity);
                $('#barcodeModal').modal('hide');
                $('#barcodeInput').val('');
            } else {
                alert('لم يتم العثور على المنتج');
            }
        });
    }
}

function addToCart(productId, productName, price, availableQty) {
    // Check if product already in cart
    const existingItem = cart.find(item => item.productId === productId);
    
    if (existingItem) {
        if (existingItem.quantity < availableQty) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            alert('الكمية المطلوبة غير متوفرة');
            return;
        }
    } else {
        if (availableQty > 0) {
            cart.push({
                productId: productId,
                productName: productName,
                price: price,
                quantity: 1,
                total: price,
                availableQty: availableQty
            });
        } else {
            alert('المنتج غير متوفر');
            return;
        }
    }
    
    updateCartDisplay();
    $('#productSearch').val('');
    $('#searchResults').hide();
}

function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.productId === productId);
    if (item) {
        if (newQuantity > 0 && newQuantity <= item.availableQty) {
            item.quantity = newQuantity;
            item.total = item.quantity * item.price;
            updateCartDisplay();
        } else if (newQuantity <= 0) {
            removeFromCart(productId);
        } else {
            alert('الكمية المطلوبة غير متوفرة');
        }
    }
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.productId !== productId);
    updateCartDisplay();
}

function clearCart() {
    cart = [];
    updateCartDisplay();
}

function updateCartDisplay() {
    const cartContainer = $('#cartItems');
    const cartCount = $('#cartCount');
    
    cartCount.text(cart.length);
    
    if (cart.length === 0) {
        cartContainer.html(`
            <div class="text-center py-5 text-muted">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>السلة فارغة. ابحث عن المنتجات لإضافتها</p>
            </div>
        `);
        $('#completeSaleBtn').prop('disabled', true);
    } else {
        let cartHtml = '';
        cart.forEach(function(item) {
            cartHtml += `
                <div class="cart-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${item.productName}</h6>
                            <small class="text-muted">${item.price.toFixed(2)} ريال للقطعة</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.productId})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="quantity-control">
                            <button onclick="updateQuantity(${item.productId}, ${item.quantity - 1})">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" value="${item.quantity}" min="1" max="${item.availableQty}"
                                   onchange="updateQuantity(${item.productId}, parseInt(this.value))">
                            <button onclick="updateQuantity(${item.productId}, ${item.quantity + 1})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="fw-bold">${item.total.toFixed(2)} ريال</div>
                    </div>
                </div>
            `;
        });
        
        cartContainer.html(cartHtml);
        $('#completeSaleBtn').prop('disabled', false);
    }
    
    updateTotals();
}

function updateTotals() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discountRate = parseFloat($('#discountRate').val()) || 0;
    const discountAmount = subtotal * (discountRate / 100);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * 0.15; // 15% VAT
    const totalAmount = taxableAmount + taxAmount;
    
    $('#subtotal').text(subtotal.toFixed(2) + ' ريال');
    $('#discountAmount').text(discountAmount.toFixed(2) + ' ريال');
    $('#taxAmount').text(taxAmount.toFixed(2) + ' ريال');
    $('#totalAmount').text(totalAmount.toFixed(2) + ' ريال');
}

function completeSale() {
    if (cart.length === 0) {
        alert('السلة فارغة');
        return;
    }
    
    const saleData = {
        customer_id: $('#customerId').val() || null,
        items: cart.map(item => ({
            product_id: item.productId,
            quantity: item.quantity,
            unit_price: item.price
        })),
        payment_method: selectedPaymentMethod,
        discount_rate: parseFloat($('#discountRate').val()) || 0,
        tax_rate: 0.15,
        notes: $('#orderNotes').val()
    };
    
    $('#completeSaleBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
    
    $.ajax({
        url: '/pos/create-sale',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(saleData),
        success: function(response) {
            if (response.success) {
                alert('تم إتمام البيع بنجاح');
                
                // Open invoice in new window
                window.open(`/pos/invoice/${response.sale_id}`, '_blank');
                
                // Clear cart
                clearCart();
                $('#customerId').val('');
                $('#discountRate').val(0);
                $('#orderNotes').val('');
                $('.payment-method').removeClass('active');
                $('.payment-method[data-method="cash"]').addClass('active');
                selectedPaymentMethod = 'cash';
            } else {
                alert('خطأ: ' + response.message);
            }
        },
        error: function() {
            alert('حدث خطأ في الاتصال');
        },
        complete: function() {
            $('#completeSaleBtn').prop('disabled', false).html('<i class="fas fa-check me-2"></i>إتمام البيع');
        }
    });
}

function holdSale() {
    // Implementation for holding/saving sale for later
    alert('سيتم تطوير هذه الميزة قريباً');
}
</script>
{% endblock %}
