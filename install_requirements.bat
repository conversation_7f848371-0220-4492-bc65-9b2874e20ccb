@echo off
chcp 65001 >nul
title AutoParts Manager V5.1 - تثبيت المتطلبات

echo ========================================
echo    AutoParts Manager V5.1
echo    تثبيت المتطلبات الأساسية
echo ========================================
echo.

echo 📦 جاري تثبيت المتطلبات الأساسية...
echo.

REM Install basic requirements
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-Login==0.6.3
pip install Werkzeug==2.3.7

echo.
echo ✅ تم تثبيت المتطلبات الأساسية بنجاح
echo.

echo 📦 جاري تثبيت المتطلبات الاختيارية...
echo.

REM Install optional requirements (ignore errors)
pip install python-barcode 2>nul
pip install Pillow 2>nul
pip install reportlab 2>nul
pip install openpyxl 2>nul
pip install pandas 2>nul
pip install hijri-converter 2>nul

echo.
echo ✅ تم الانتهاء من التثبيت
echo.
echo 🚀 يمكنك الآن تشغيل النظام باستخدام:
echo    start.bat
echo    أو
echo    python app.py
echo.

pause
