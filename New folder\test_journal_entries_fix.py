#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة القيود اليومية
Test Journal Entries UNIQUE Constraint Fix
"""

import requests
import time
import json
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class JournalEntriesTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False

    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False

    def test_pos_sale(self):
        """اختبار إنشاء فاتورة مبيعات (يجب أن ينشئ قيود يومية)"""
        print("\n🔄 اختبار إنشاء فاتورة مبيعات...")
        try:
            # بيانات فاتورة مبيعات تجريبية
            sale_data = {
                'customer_id': None,  # عميل نقدي
                'payment_method': 'cash',
                'discount_rate': 0,
                'tax_rate': 0.15,
                'notes': 'فاتورة اختبار لإصلاح القيود اليومية',
                'items': [
                    {
                        'product_id': 1,
                        'quantity': 2,
                        'unit_price': 50.0
                    }
                ]
            }

            # إرسال البيانات كـ JSON
            response = self.session.post(f"{BASE_URL}/pos/create-sale",
                                       json=sale_data,
                                       headers={'Content-Type': 'application/json'})

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ تم إنشاء فاتورة المبيعات بنجاح")
                    print(f"📄 رقم الفاتورة: {result.get('invoice_number', 'غير محدد')}")
                    return True
                else:
                    print(f"❌ فشل في إنشاء فاتورة المبيعات: {result.get('message', 'خطأ غير محدد')}")
                    return False
            else:
                print(f"❌ فشل في إنشاء فاتورة المبيعات: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                return False

        except Exception as e:
            print(f"❌ خطأ في إنشاء فاتورة المبيعات: {e}")
            return False

    def test_manual_journal_entry(self):
        """اختبار إنشاء قيد يومي يدوي"""
        print("\n🔄 اختبار إنشاء قيد يومي يدوي...")
        try:
            # بيانات قيد يومي تجريبي - استخدام form data بدلاً من JSON
            journal_data = {
                'transaction_date': datetime.now().strftime('%Y-%m-%d'),
                'description': 'قيد اختبار لإصلاح مشكلة UNIQUE constraint',
                'account_id_1': '1',  # حساب النقدية
                'debit_amount_1': '100.0',
                'credit_amount_1': '0.0',
                'account_id_2': '2',  # حساب المبيعات
                'debit_amount_2': '0.0',
                'credit_amount_2': '100.0'
            }

            response = self.session.post(f"{BASE_URL}/accounting/add-journal-entry", data=journal_data)

            if response.status_code == 302:  # إعادة توجيه بعد النجاح
                print("✅ تم إنشاء القيد اليومي بنجاح")
                return True
            elif response.status_code == 200:
                # تحقق من وجود رسالة نجاح في المحتوى
                if 'تم إضافة القيد بنجاح' in response.text:
                    print("✅ تم إنشاء القيد اليومي بنجاح")
                    return True
                else:
                    print("❌ فشل في إنشاء القيد اليومي - لا توجد رسالة نجاح")
                    print(f"📄 جزء من المحتوى: {response.text[:300]}...")
                    return False
            else:
                print(f"❌ فشل في إنشاء القيد اليومي: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ خطأ في إنشاء القيد اليومي: {e}")
            return False

    def test_journal_entries_page(self):
        """اختبار صفحة القيود اليومية"""
        print("\n🔄 اختبار صفحة القيود اليومية...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/journal-entries")

            if response.status_code == 200:
                print("✅ صفحة القيود اليومية تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة القيود اليومية: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ خطأ في صفحة القيود اليومية: {e}")
            return False

    def test_multiple_sales(self):
        """اختبار إنشاء عدة فواتير متتالية (اختبار تكرار entry_number)"""
        print("\n🔄 اختبار إنشاء عدة فواتير متتالية...")

        success_count = 0
        total_tests = 3

        for i in range(total_tests):
            try:
                sale_data = {
                    'customer_id': None,
                    'payment_method': 'cash',
                    'discount_rate': 0,
                    'tax_rate': 0.15,
                    'notes': f'فاتورة اختبار متتالية رقم {i+1}',
                    'items': [
                        {
                            'product_id': 1,
                            'quantity': 1,
                            'unit_price': 25.0
                        }
                    ]
                }

                response = self.session.post(f"{BASE_URL}/pos/create-sale",
                                           json=sale_data,
                                           headers={'Content-Type': 'application/json'})

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"✅ فاتورة {i+1}: نجحت - {result.get('invoice_number', 'غير محدد')}")
                        success_count += 1
                    else:
                        print(f"❌ فاتورة {i+1}: فشلت - {result.get('message', 'خطأ غير محدد')}")
                else:
                    print(f"❌ فاتورة {i+1}: فشلت - كود {response.status_code}")

                time.sleep(1)  # انتظار ثانية بين الفواتير

            except Exception as e:
                print(f"❌ فاتورة {i+1}: خطأ - {e}")

        if success_count == total_tests:
            print(f"✅ جميع الفواتير المتتالية نجحت ({success_count}/{total_tests})")
            return True
        else:
            print(f"⚠️ نجح {success_count} من {total_tests} فواتير")
            return success_count > 0

    def test_accounting_dashboard(self):
        """اختبار لوحة المحاسبة"""
        print("\n🔄 اختبار لوحة المحاسبة...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/")

            if response.status_code == 200:
                print("✅ لوحة المحاسبة تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول للوحة المحاسبة: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ خطأ في لوحة المحاسبة: {e}")
            return False

    def run_all_tests(self):
        """تشغيل جميع اختبارات القيود اليومية"""
        print("📊 بدء اختبار إصلاح مشكلة القيود اليومية")
        print("=" * 60)

        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False

        tests = [
            ("لوحة المحاسبة", self.test_accounting_dashboard),
            ("صفحة القيود اليومية", self.test_journal_entries_page),
            ("إنشاء فاتورة مبيعات", self.test_pos_sale),
            ("إنشاء قيد يومي يدوي", self.test_manual_journal_entry),
            ("إنشاء عدة فواتير متتالية", self.test_multiple_sales),
        ]

        passed = 0
        failed = 0

        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(2)  # انتظار ثانيتين بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1

        print("\n" + "=" * 60)
        print("📊 نتائج اختبار إصلاح القيود اليومية:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")

        if failed == 0:
            print("\n🎉 تم إصلاح مشكلة القيود اليومية بنجاح 100%!")
            print("🚀 يمكن الآن إنشاء فواتير المبيعات وإيصالات الدفع بدون أخطاء!")
            return True
        elif passed > failed:
            print(f"\n✅ معظم الوظائف تعمل بنجاح ({passed}/{passed+failed})")
            print("⚠️ قد تحتاج بعض التحسينات الإضافية")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة تحتاج إلى مراجعة")
            return False

def main():
    """الدالة الرئيسية"""
    print("📊 اختبار إصلاح مشكلة القيود اليومية - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()

    tester = JournalEntriesTester()
    success = tester.run_all_tests()

    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
