#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات الأولية
Initial Database Setup
"""

import os
import sys
from datetime import datetime
from werkzeug.security import generate_password_hash

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db
    from models import (
        User, Customer, Supplier, Category, Product, 
        Sale, SaleItem, Purchase, PurchaseItem,
        Account, JournalEntry, CashTransaction,
        Setting, AuditLog
    )
    
    def create_database():
        """إنشاء قاعدة البيانات والجداول"""
        print("🔨 إنشاء قاعدة البيانات والجداول...")
        
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ تم إنشاء جميع الجداول بنجاح")
            
            # Check if admin user exists
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("👤 إنشاء المستخدم الافتراضي...")
                
                # Create default admin user
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    is_active=True,
                    created_at=datetime.now()
                )
                db.session.add(admin_user)
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
            else:
                print("✅ المستخدم الافتراضي موجود مسبقاً")
            
            # Create default settings
            print("⚙️ إنشاء الإعدادات الافتراضية...")
            
            default_settings = [
                ('company_name', 'نظام إدارة قطع الغيار'),
                ('company_address', 'المملكة العربية السعودية'),
                ('company_phone', '+966-XX-XXX-XXXX'),
                ('company_email', '<EMAIL>'),
                ('tax_rate', '15.0'),
                ('currency', 'ر.س'),
                ('language', 'ar'),
                ('timezone', 'Asia/Riyadh'),
                ('backup_frequency', 'daily'),
                ('low_stock_threshold', '10'),
                ('invoice_prefix', 'INV-'),
                ('receipt_prefix', 'REC-'),
                ('auto_backup', 'true'),
                ('email_notifications', 'false'),
                ('sms_notifications', 'false'),
                ('theme', 'default'),
                ('items_per_page', '25'),
                ('date_format', 'dd/mm/yyyy'),
                ('time_format', '24'),
                ('decimal_places', '2')
            ]
            
            for key, value in default_settings:
                existing_setting = Setting.query.filter_by(key=key).first()
                if not existing_setting:
                    setting = Setting(
                        key=key,
                        value=value,
                        description=f'إعداد {key}',
                        created_at=datetime.now()
                    )
                    db.session.add(setting)
            
            print("✅ تم إنشاء الإعدادات الافتراضية")
            
            # Create default chart of accounts
            print("📊 إنشاء دليل الحسابات الافتراضي...")
            
            default_accounts = [
                # Assets (الأصول)
                ('1000', 'النقدية', 'asset', 'الأصول المتداولة'),
                ('1100', 'البنك', 'asset', 'الأصول المتداولة'),
                ('1200', 'المخزون', 'asset', 'الأصول المتداولة'),
                ('1300', 'العملاء', 'asset', 'الأصول المتداولة'),
                ('1400', 'أوراق القبض', 'asset', 'الأصول المتداولة'),
                ('1500', 'المصروفات المدفوعة مقدماً', 'asset', 'الأصول المتداولة'),
                ('1600', 'الأثاث والمعدات', 'asset', 'الأصول الثابتة'),
                ('1700', 'مجمع إهلاك الأثاث والمعدات', 'asset', 'الأصول الثابتة'),
                
                # Liabilities (الخصوم)
                ('2000', 'الموردون', 'liability', 'الخصوم المتداولة'),
                ('2100', 'أوراق الدفع', 'liability', 'الخصوم المتداولة'),
                ('2200', 'المصروفات المستحقة', 'liability', 'الخصوم المتداولة'),
                ('2300', 'ضريبة القيمة المضافة', 'liability', 'الخصوم المتداولة'),
                ('2400', 'قروض قصيرة الأجل', 'liability', 'الخصوم المتداولة'),
                ('2500', 'قروض طويلة الأجل', 'liability', 'الخصوم طويلة الأجل'),
                
                # Equity (حقوق الملكية)
                ('3000', 'رأس المال', 'equity', 'حقوق الملكية'),
                ('3100', 'الأرباح المحتجزة', 'equity', 'حقوق الملكية'),
                ('3200', 'أرباح العام الحالي', 'equity', 'حقوق الملكية'),
                
                # Revenue (الإيرادات)
                ('4000', 'مبيعات قطع الغيار', 'revenue', 'الإيرادات'),
                ('4100', 'إيرادات الخدمات', 'revenue', 'الإيرادات'),
                ('4200', 'إيرادات أخرى', 'revenue', 'الإيرادات'),
                
                # Expenses (المصروفات)
                ('5000', 'تكلفة البضاعة المباعة', 'expense', 'المصروفات'),
                ('5100', 'رواتب وأجور', 'expense', 'المصروفات التشغيلية'),
                ('5200', 'إيجار', 'expense', 'المصروفات التشغيلية'),
                ('5300', 'كهرباء وماء', 'expense', 'المصروفات التشغيلية'),
                ('5400', 'هاتف وإنترنت', 'expense', 'المصروفات التشغيلية'),
                ('5500', 'صيانة وإصلاح', 'expense', 'المصروفات التشغيلية'),
                ('5600', 'مصروفات تسويق', 'expense', 'المصروفات التشغيلية'),
                ('5700', 'مصروفات إدارية', 'expense', 'المصروفات التشغيلية'),
                ('5800', 'إهلاك', 'expense', 'المصروفات التشغيلية'),
                ('5900', 'مصروفات أخرى', 'expense', 'المصروفات التشغيلية'),
            ]
            
            for code, name, account_type, category in default_accounts:
                existing_account = Account.query.filter_by(code=code).first()
                if not existing_account:
                    account = Account(
                        code=code,
                        name=name,
                        account_type=account_type,
                        category=category,
                        is_active=True,
                        created_at=datetime.now()
                    )
                    db.session.add(account)
            
            print("✅ تم إنشاء دليل الحسابات الافتراضي")
            
            # Create default categories
            print("📂 إنشاء فئات المنتجات الافتراضية...")
            
            default_categories = [
                ('قطع غيار السيارات', 'قطع غيار مختلفة للسيارات'),
                ('زيوت ومواد التشحيم', 'زيوت المحركات ومواد التشحيم'),
                ('إطارات وعجلات', 'إطارات وعجلات السيارات'),
                ('بطاريات', 'بطاريات السيارات'),
                ('فلاتر', 'فلاتر الهواء والزيت والوقود'),
                ('أدوات وعدد', 'أدوات الصيانة والإصلاح'),
                ('إكسسوارات', 'إكسسوارات السيارات'),
                ('قطع غيار أصلية', 'قطع غيار أصلية من الوكيل'),
                ('قطع غيار تجارية', 'قطع غيار تجارية عالية الجودة'),
            ]
            
            for name, description in default_categories:
                existing_category = Category.query.filter_by(name=name).first()
                if not existing_category:
                    category = Category(
                        name=name,
                        description=description,
                        is_active=True,
                        created_at=datetime.now()
                    )
                    db.session.add(category)
            
            print("✅ تم إنشاء فئات المنتجات الافتراضية")
            
            # Commit all changes
            db.session.commit()
            print("💾 تم حفظ جميع البيانات بنجاح")
            
            # Log the initialization
            audit_log = AuditLog(
                user_id=admin_user.id,
                action='إعداد النظام الأولي',
                table_name='system',
                record_id=None,
                old_values=None,
                new_values='تم إعداد النظام وإنشاء البيانات الافتراضية',
                timestamp=datetime.now()
            )
            db.session.add(audit_log)
            db.session.commit()
            
            print("📝 تم تسجيل عملية الإعداد في سجل المراجعة")
            
    def create_directories():
        """إنشاء المجلدات المطلوبة"""
        print("📁 إنشاء المجلدات المطلوبة...")
        
        directories = [
            'exports',
            'exports/reports',
            'exports/excel',
            'backups',
            'uploads',
            'static/uploads',
            'logs'
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✅ تم إنشاء مجلد: {directory}")
            else:
                print(f"✅ مجلد موجود: {directory}")
    
    def main():
        """الدالة الرئيسية"""
        print("=" * 60)
        print("🚀 إعداد نظام إدارة قطع الغيار - AutoParts Manager V5.1")
        print("=" * 60)
        print()
        
        try:
            # Create directories
            create_directories()
            print()
            
            # Create database
            create_database()
            print()
            
            print("=" * 60)
            print("🎉 تم إعداد النظام بنجاح!")
            print("=" * 60)
            print()
            print("📋 معلومات الدخول:")
            print("   👤 اسم المستخدم: admin")
            print("   🔑 كلمة المرور: admin123")
            print()
            print("🌐 لتشغيل النظام:")
            print("   python app.py")
            print("   ثم افتح المتصفح على: http://127.0.0.1:8888")
            print()
            print("=" * 60)
            
        except Exception as e:
            print(f"❌ خطأ في إعداد النظام: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    if __name__ == "__main__":
        success = main()
        if not success:
            sys.exit(1)

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
    print("🔧 تأكد من تثبيت جميع المتطلبات باستخدام:")
    print("   pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ عام: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
