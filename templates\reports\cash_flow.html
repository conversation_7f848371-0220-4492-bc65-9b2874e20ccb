{% extends "base.html" %}

{% block title %}تقرير التدفق النقدي - التقارير المالية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-exchange-alt me-2"></i>
        تقرير التدفق النقدي
    </h1>
    <div>
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للتقارير
        </a>
        <a href="{{ url_for('reports.cash_flow', export='excel') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>
            تصدير Excel
        </a>
        <a href="{{ url_for('reports.cash_flow', export='pdf') }}" class="btn btn-danger">
            <i class="fas fa-file-pdf me-2"></i>
            تصدير PDF
        </a>
    </div>
</div>

<!-- Date Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-calendar me-2"></i>
            فترة التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="from_date" 
                       value="{{ request.args.get('from_date', '') }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="to_date" 
                       value="{{ request.args.get('to_date', '') }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calculator me-1"></i>
                        إعداد التقرير
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Cash Flow Statement -->
<div class="card">
    <div class="card-header">
        <div class="text-center">
            <h4 class="mb-1">قائمة التدفق النقدي</h4>
            {% if from_date and to_date %}
                <p class="text-muted mb-0">للفترة من {{ from_date }} إلى {{ to_date }}</p>
            {% else %}
                <p class="text-muted mb-0">للفترة الحالية</p>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-borderless">
                <tbody>
                    <!-- Operating Activities -->
                    <tr class="table-primary">
                        <td colspan="2"><h5 class="mb-0">التدفقات النقدية من الأنشطة التشغيلية</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">صافي الدخل</td>
                        <td class="text-end">{{ net_income|currency }}</td>
                    </tr>
                    <tr class="table-light">
                        <td class="ps-4"><em>تعديلات لتسوية صافي الدخل:</em></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="ps-5">الاستهلاك والإطفاء</td>
                        <td class="text-end">{{ depreciation|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-5">مخصص الديون المشكوك فيها</td>
                        <td class="text-end">{{ bad_debt_provision|currency }}</td>
                    </tr>
                    <tr class="table-light">
                        <td class="ps-4"><em>التغيرات في رأس المال العامل:</em></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="ps-5">التغير في المخزون</td>
                        <td class="text-end {% if inventory_change < 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ inventory_change|currency }}
                        </td>
                    </tr>
                    <tr>
                        <td class="ps-5">التغير في العملاء</td>
                        <td class="text-end {% if customers_change < 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ customers_change|currency }}
                        </td>
                    </tr>
                    <tr>
                        <td class="ps-5">التغير في الموردين</td>
                        <td class="text-end {% if suppliers_change > 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ suppliers_change|currency }}
                        </td>
                    </tr>
                    <tr>
                        <td class="ps-5">التغير في المصروفات المدفوعة مقدماً</td>
                        <td class="text-end {% if prepaid_change < 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ prepaid_change|currency }}
                        </td>
                    </tr>
                    <tr>
                        <td class="ps-5">التغير في المصروفات المستحقة</td>
                        <td class="text-end {% if accrued_change > 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ accrued_change|currency }}
                        </td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>صافي النقد من الأنشطة التشغيلية</strong></td>
                        <td class="text-end">
                            <strong class="{% if operating_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ operating_cash_flow|currency }}
                            </strong>
                        </td>
                    </tr>
                    
                    <!-- Investing Activities -->
                    <tr class="table-warning">
                        <td colspan="2"><h5 class="mb-0 mt-3">التدفقات النقدية من الأنشطة الاستثمارية</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">شراء أصول ثابتة</td>
                        <td class="text-end text-danger">{{ fixed_assets_purchases|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">بيع أصول ثابتة</td>
                        <td class="text-end text-success">{{ fixed_assets_sales|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">استثمارات أخرى</td>
                        <td class="text-end">{{ other_investments|currency }}</td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>صافي النقد من الأنشطة الاستثمارية</strong></td>
                        <td class="text-end">
                            <strong class="{% if investing_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ investing_cash_flow|currency }}
                            </strong>
                        </td>
                    </tr>
                    
                    <!-- Financing Activities -->
                    <tr class="table-info">
                        <td colspan="2"><h5 class="mb-0 mt-3">التدفقات النقدية من الأنشطة التمويلية</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">قروض جديدة</td>
                        <td class="text-end text-success">{{ new_loans|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">سداد قروض</td>
                        <td class="text-end text-danger">{{ loan_payments|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">زيادة رأس المال</td>
                        <td class="text-end text-success">{{ capital_increase|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">توزيعات أرباح</td>
                        <td class="text-end text-danger">{{ dividends_paid|currency }}</td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>صافي النقد من الأنشطة التمويلية</strong></td>
                        <td class="text-end">
                            <strong class="{% if financing_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ financing_cash_flow|currency }}
                            </strong>
                        </td>
                    </tr>
                    
                    <!-- Net Change in Cash -->
                    <tr class="table-secondary">
                        <td><h5 class="mb-0 mt-3">صافي التغير في النقدية</h5></td>
                        <td class="text-end">
                            <h5 class="mb-0 mt-3 {% if net_cash_change >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ net_cash_change|currency }}
                            </h5>
                        </td>
                    </tr>
                    
                    <!-- Cash Balances -->
                    <tr>
                        <td class="ps-4">النقدية في بداية الفترة</td>
                        <td class="text-end">{{ opening_cash|currency }}</td>
                    </tr>
                    <tr class="table-dark">
                        <td><h4 class="mb-0 mt-3 text-white">النقدية في نهاية الفترة</h4></td>
                        <td class="text-end">
                            <h4 class="mb-0 mt-3 text-success">{{ closing_cash|currency }}</h4>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Cash Flow Analysis -->
<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    تحليل التدفق النقدي
                </h5>
            </div>
            <div class="card-body">
                <canvas id="cashFlowChart" height="150"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    ملخص التدفقات
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الأنشطة التشغيلية:</span>
                        <span class="{% if operating_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ operating_cash_flow|currency }}
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الأنشطة الاستثمارية:</span>
                        <span class="{% if investing_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ investing_cash_flow|currency }}
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>الأنشطة التمويلية:</span>
                        <span class="{% if financing_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ financing_cash_flow|currency }}
                        </span>
                    </div>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>صافي التغير:</strong>
                    <strong class="{% if net_cash_change >= 0 %}text-success{% else %}text-danger{% endif %}">
                        {{ net_cash_change|currency }}
                    </strong>
                </div>
            </div>
        </div>
        
        <!-- Cash Ratios -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-percentage me-2"></i>
                    نسب السيولة
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h4 class="text-primary">{{ operating_cash_ratio|round(2) }}</h4>
                    <p class="text-muted mb-0">نسبة التدفق التشغيلي</p>
                </div>
                <div class="text-center">
                    <h4 class="text-info">{{ cash_coverage_ratio|round(2) }}</h4>
                    <p class="text-muted mb-0">نسبة تغطية النقدية</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Cash Flow Trend -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    اتجاه التدفق النقدي الشهري
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Cash Flow Chart
const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
new Chart(cashFlowCtx, {
    type: 'bar',
    data: {
        labels: ['الأنشطة التشغيلية', 'الأنشطة الاستثمارية', 'الأنشطة التمويلية'],
        datasets: [{
            label: 'التدفق النقدي',
            data: [{{ operating_cash_flow }}, {{ investing_cash_flow }}, {{ financing_cash_flow }}],
            backgroundColor: ['#28a745', '#ffc107', '#17a2b8'],
            borderColor: ['#1e7e34', '#e0a800', '#138496'],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Monthly Trend Chart
const monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');
new Chart(monthlyTrendCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [{
            label: 'التدفق النقدي التشغيلي',
            data: {{ monthly_operating_cash|tojson }},
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'إجمالي التدفق النقدي',
            data: {{ monthly_total_cash|tojson }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: false
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        },
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
