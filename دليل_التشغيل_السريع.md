# 🚀 دليل التشغيل السريع - AutoParts Manager V5.1

## 📋 خطوات التشغيل السريع

### 1. تثبيت المتطلبات
```bash
# انقر نقراً مزدوجاً على:
install_requirements.bat
```

### 2. تشغيل النظام
```bash
# انقر نقراً مزدوجاً على:
start.bat
```

### 3. فتح النظام في المتصفح
افتح المتصفح واذهب إلى:
```
http://127.0.0.1:8888
```

## 👤 بيانات الدخول الافتراضية

| المستخدم | اسم المستخدم | كلمة المرور | الصلاحيات |
|----------|-------------|------------|-----------|
| المدير | `admin` | `admin123` | صلاحيات كاملة |
| المحاسب | `accountant` | `acc123` | الحسابات والتقارير |
| الكاشير | `cashier` | `cash123` | نقطة البيع فقط |

## 🔧 استكشاف الأخطاء

### إذا لم يعمل النظام:
1. تأكد من تثبيت Python
2. شغل `install_requirements.bat` أولاً
3. تأكد من أن المنفذ 8888 غير مستخدم

### إذا ظهرت أخطاء في المتطلبات:
```bash
pip install Flask Flask-SQLAlchemy Flask-Login
```

### إذا لم يفتح المتصفح تلقائياً:
افتح المتصفح يدوياً واذهب إلى: `http://127.0.0.1:8888`

## 📁 ملفات مهمة

- `app.py` - الملف الرئيسي للنظام
- `autoparts.db` - قاعدة البيانات (سيتم إنشاؤها تلقائياً)
- `start.bat` - تشغيل سريع
- `install_requirements.bat` - تثبيت المتطلبات

## 🎯 الخطوات التالية

1. **تسجيل الدخول** باستخدام بيانات المدير
2. **تغيير كلمات المرور** الافتراضية
3. **إضافة بيانات الشركة** في الإعدادات
4. **إضافة المنتجات والعملاء**
5. **بدء استخدام نقطة البيع**

## 📞 الدعم

للحصول على المساعدة:
- راجع ملف `README.md` للتفاصيل الكاملة
- تحقق من ملف `دليل_التثبيت.md`

---
**نظام إدارة قطع الغيار المتكامل - الإصدار 5.1**
