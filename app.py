# -*- coding: utf-8 -*-
import os
from flask import Flask, render_template, redirect, url_for, session
from flask_login import LoginManager
from models import db, User
from auth import auth_bp, init_default_users
from datetime import datetime

def create_app():
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = 'autoparts-manager-secret-key-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///autoparts.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['UPLOAD_FOLDER'] = 'static/uploads'

    # Initialize extensions
    db.init_app(app)

    # Create upload directories
    os.makedirs('static/uploads', exist_ok=True)
    os.makedirs('static/barcodes', exist_ok=True)
    os.makedirs('static/exports', exist_ok=True)
    os.makedirs('static/reports', exist_ok=True)
    os.makedirs('backups', exist_ok=True)

    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')

    # Main routes
    @app.route('/')
    def index():
        if 'user_id' in session:
            user = User.query.get(session['user_id'])
            if user and user.role == 'cashier':
                return redirect(url_for('pos.index'))
            return redirect(url_for('main.dashboard'))
        return redirect(url_for('auth.login'))

    # Import and register other blueprints
    from routes.main import main_bp
    from routes.products import products_bp
    from routes.customers import customers_bp
    from routes.suppliers import suppliers_bp
    from routes.pos import pos_bp
    from routes.accounting import accounting_bp
    from routes.reports import reports_bp
    from routes.settings import settings_bp
    from routes.inventory import inventory_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(products_bp, url_prefix='/products')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(suppliers_bp, url_prefix='/suppliers')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(accounting_bp, url_prefix='/accounting')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    # Template filters
    @app.template_filter('currency')
    def currency_filter(amount):
        if amount is None:
            return "0.00 ريال"
        try:
            return f"{float(amount):,.2f} ريال"
        except (ValueError, TypeError):
            return "0.00 ريال"

    @app.template_filter('hijri')
    def hijri_filter(date):
        from utils import hijri_date
        return hijri_date(date)

    # Template globals
    @app.template_global()
    def get_current_user():
        if 'user_id' in session:
            return User.query.get(session['user_id'])
        return None

    @app.template_global()
    def has_permission(permission):
        if 'user_id' in session:
            user = User.query.get(session['user_id'])
            return user.has_permission(permission) if user else False
        return False

    # Initialize database
    with app.app_context():
        db.create_all()
        init_default_users()
        init_default_data()

    return app

def init_default_data():
    """Initialize default data"""
    from models import Account, ExpenseCategory, Setting

    # Initialize chart of accounts
    if Account.query.count() == 0:
        accounts = [
            # Assets
            {'code': '1000', 'name': 'الأصول', 'account_type': 'assets', 'parent_id': None},
            {'code': '1100', 'name': 'الأصول المتداولة', 'account_type': 'assets', 'parent_id': 1},
            {'code': '1110', 'name': 'النقدية', 'account_type': 'assets', 'parent_id': 2},
            {'code': '1120', 'name': 'المخزون', 'account_type': 'assets', 'parent_id': 2},
            {'code': '1130', 'name': 'العملاء', 'account_type': 'assets', 'parent_id': 2},

            # Liabilities
            {'code': '2000', 'name': 'الالتزامات', 'account_type': 'liabilities', 'parent_id': None},
            {'code': '2100', 'name': 'الالتزامات المتداولة', 'account_type': 'liabilities', 'parent_id': 6},
            {'code': '2110', 'name': 'الموردين', 'account_type': 'liabilities', 'parent_id': 7},

            # Equity
            {'code': '3000', 'name': 'حقوق الملكية', 'account_type': 'equity', 'parent_id': None},
            {'code': '3100', 'name': 'رأس المال', 'account_type': 'equity', 'parent_id': 9},

            # Revenue
            {'code': '4000', 'name': 'الإيرادات', 'account_type': 'revenue', 'parent_id': None},
            {'code': '4100', 'name': 'مبيعات', 'account_type': 'revenue', 'parent_id': 11},

            # Expenses
            {'code': '5000', 'name': 'المصروفات', 'account_type': 'expenses', 'parent_id': None},
            {'code': '5100', 'name': 'تكلفة البضاعة المباعة', 'account_type': 'expenses', 'parent_id': 13},
            {'code': '5200', 'name': 'مصروفات تشغيلية', 'account_type': 'expenses', 'parent_id': 13},
        ]

        for acc_data in accounts:
            account = Account(**acc_data)
            db.session.add(account)

        db.session.commit()

    # Initialize expense categories
    if ExpenseCategory.query.count() == 0:
        categories = [
            {'name': 'إيجار', 'description': 'إيجار المحل والمكاتب'},
            {'name': 'كهرباء', 'description': 'فواتير الكهرباء'},
            {'name': 'مياه', 'description': 'فواتير المياه'},
            {'name': 'هاتف وإنترنت', 'description': 'فواتير الاتصالات'},
            {'name': 'رواتب', 'description': 'رواتب الموظفين'},
            {'name': 'صيانة', 'description': 'صيانة المعدات والأجهزة'},
            {'name': 'وقود', 'description': 'وقود السيارات'},
            {'name': 'مصروفات أخرى', 'description': 'مصروفات متنوعة'},
        ]

        for cat_data in categories:
            category = ExpenseCategory(**cat_data)
            db.session.add(category)

        db.session.commit()

    # Initialize system settings
    if Setting.query.count() == 0:
        settings = [
            {'key': 'company_name', 'value': 'شركة قطع الغيار', 'description': 'اسم الشركة'},
            {'key': 'company_address', 'value': 'الرياض، المملكة العربية السعودية', 'description': 'عنوان الشركة'},
            {'key': 'company_phone', 'value': '+966 11 123 4567', 'description': 'هاتف الشركة'},
            {'key': 'company_email', 'value': '<EMAIL>', 'description': 'بريد الشركة الإلكتروني'},
            {'key': 'tax_rate', 'value': '0.15', 'description': 'معدل الضريبة'},
            {'key': 'currency', 'value': 'ريال', 'description': 'العملة'},
            {'key': 'invoice_prefix', 'value': 'INV', 'description': 'بادئة رقم الفاتورة'},
            {'key': 'receipt_prefix', 'value': 'REC', 'description': 'بادئة رقم الإيصال'},
        ]

        for setting_data in settings:
            setting = Setting(**setting_data)
            db.session.add(setting)

        db.session.commit()

if __name__ == '__main__':
    import socket

    app = create_app()

    # Get all available IP addresses
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)

    print("=" * 60)
    print("🚀 نظام إدارة قطع الغيار - AutoParts Manager")
    print("=" * 60)
    print(f"🌐 النظام يعمل على العناوين التالية:")
    print(f"   📍 المحلي: http://127.0.0.1:8888")
    print(f"   📍 الشبكة: http://{local_ip}:8888")
    print(f"   📍 جميع الشبكات: http://0.0.0.0:8888")
    print("=" * 60)
    print("💡 يمكن الوصول للنظام من أي جهاز في الشبكة المحلية")
    print("🔐 المستخدم الافتراضي: admin | كلمة المرور: admin123")
    print("=" * 60)

    # Run on all available interfaces
    app.run(debug=False, host='0.0.0.0', port=8888, threaded=True)
