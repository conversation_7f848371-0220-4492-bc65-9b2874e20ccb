# -*- coding: utf-8 -*-
"""
AutoParts Manager - Installer Script
نظام إدارة قطع الغيار - ملف التثبيت الشامل
"""

import os
import sys
import subprocess
import shutil
import socket
import webbrowser
from pathlib import Path
import time

class AutoPartsInstaller:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.install_dir = Path.home() / "AutoParts Manager"
        self.python_exe = None
        
    def print_header(self):
        print("=" * 80)
        print("🚀 نظام إدارة قطع الغيار - AutoParts Manager")
        print("📦 برنامج التثبيت الشامل")
        print("=" * 80)
        print()
        
    def check_python(self):
        """Check if Python is installed"""
        print("🔍 فحص Python...")
        
        # Try different Python executables
        python_candidates = ['python', 'python3', 'py']
        
        for candidate in python_candidates:
            try:
                result = subprocess.run([candidate, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.python_exe = candidate
                    version = result.stdout.strip()
                    print(f"✅ تم العثور على Python: {version}")
                    return True
            except FileNotFoundError:
                continue
                
        print("❌ لم يتم العثور على Python!")
        print("📥 يرجى تحميل وتثبيت Python من: https://python.org")
        return False
        
    def install_requirements(self):
        """Install required packages"""
        print("\n📦 تثبيت المتطلبات...")
        
        requirements = [
            'Flask==2.3.3',
            'Flask-SQLAlchemy==3.0.5',
            'Flask-Login==0.6.3',
            'Werkzeug==2.3.7',
            'SQLAlchemy==2.0.21',
            'Jinja2==3.1.2',
            'MarkupSafe==2.1.3',
            'itsdangerous==2.1.2',
            'click==8.1.7',
            'blinker==1.6.3',
            'python-barcode==0.15.1',
            'Pillow==10.0.1',
            'reportlab==4.0.4',
            'openpyxl==3.1.2',
            'pandas==2.1.1',
            'PyInstaller==6.0.0',
            'hijri-converter==2.3.1',
            'python-dateutil==2.8.2'
        ]
        
        for package in requirements:
            print(f"📥 تثبيت {package}...")
            try:
                subprocess.run([self.python_exe, '-m', 'pip', 'install', package], 
                             check=True, capture_output=True)
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError as e:
                print(f"❌ فشل في تثبيت {package}: {e}")
                return False
                
        print("✅ تم تثبيت جميع المتطلبات بنجاح!")
        return True
        
    def copy_files(self):
        """Copy application files to installation directory"""
        print(f"\n📁 نسخ الملفات إلى {self.install_dir}...")
        
        # Create installation directory
        self.install_dir.mkdir(parents=True, exist_ok=True)
        
        # Files and directories to copy
        items_to_copy = [
            'app.py', 'auth.py', 'models.py', 'utils.py',
            'routes', 'templates', 'static', 'requirements.txt'
        ]
        
        for item in items_to_copy:
            src = self.base_dir / item
            dst = self.install_dir / item
            
            if src.exists():
                if src.is_file():
                    shutil.copy2(src, dst)
                    print(f"✅ نسخ ملف: {item}")
                else:
                    if dst.exists():
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
                    print(f"✅ نسخ مجلد: {item}")
            else:
                print(f"⚠️ لم يتم العثور على: {item}")
                
        print("✅ تم نسخ جميع الملفات!")
        
    def create_startup_script(self):
        """Create startup script"""
        print("\n📝 إنشاء ملف التشغيل...")
        
        # Get local IP
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        
        # Create batch file for Windows
        batch_content = f'''@echo off
chcp 65001 > nul
title نظام إدارة قطع الغيار - AutoParts Manager
color 0A

echo ===============================================================
echo 🚀 نظام إدارة قطع الغيار - AutoParts Manager
echo ===============================================================
echo.
echo 🌐 النظام يعمل على العناوين التالية:
echo    📍 المحلي: http://127.0.0.1:8888
echo    📍 الشبكة: http://{local_ip}:8888
echo    📍 جميع الشبكات: http://0.0.0.0:8888
echo.
echo 💡 يمكن الوصول للنظام من أي جهاز في الشبكة المحلية
echo 🔐 المستخدم الافتراضي: admin ^| كلمة المرور: admin123
echo ===============================================================
echo.
echo ⏳ جاري تشغيل النظام...
echo.

cd /d "{self.install_dir}"
{self.python_exe} app.py

pause
'''
        
        batch_file = self.install_dir / "تشغيل_النظام.bat"
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(batch_content)
            
        print(f"✅ تم إنشاء ملف التشغيل: {batch_file}")
        
        # Create Python startup script
        py_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoParts Manager Startup Script
"""

import os
import sys
import socket
import webbrowser
import time
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def main():
    try:
        # Import the app
        from app import create_app
        
        app = create_app()
        
        # Get local IP
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        
        print("=" * 60)
        print("🚀 نظام إدارة قطع الغيار - AutoParts Manager")
        print("=" * 60)
        print(f"🌐 النظام يعمل على العناوين التالية:")
        print(f"   📍 المحلي: http://127.0.0.1:8888")
        print(f"   📍 الشبكة: http://{{local_ip}}:8888")
        print(f"   📍 جميع الشبكات: http://0.0.0.0:8888")
        print("=" * 60)
        print("💡 يمكن الوصول للنظام من أي جهاز في الشبكة المحلية")
        print("🔐 المستخدم الافتراضي: admin | كلمة المرور: admin123")
        print("=" * 60)
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://127.0.0.1:8888')
            
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        # Run the app
        app.run(debug=False, host='0.0.0.0', port=8888, threaded=True)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {{e}}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
'''
        
        py_file = self.install_dir / "run_autoparts.py"
        with open(py_file, 'w', encoding='utf-8') as f:
            f.write(py_content)
            
        print(f"✅ تم إنشاء ملف Python: {py_file}")
        
    def create_desktop_shortcut(self):
        """Create desktop shortcut"""
        print("\n🖥️ إنشاء اختصار سطح المكتب...")
        
        try:
            desktop = Path.home() / "Desktop"
            if not desktop.exists():
                desktop = Path.home() / "سطح المكتب"
                
            if desktop.exists():
                shortcut_content = f'''[InternetShortcut]
URL=file:///{self.install_dir / "تشغيل_النظام.bat"}
IconFile={self.install_dir / "static" / "favicon.ico"}
'''
                shortcut_file = desktop / "نظام إدارة قطع الغيار.url"
                with open(shortcut_file, 'w', encoding='utf-8') as f:
                    f.write(shortcut_content)
                    
                print(f"✅ تم إنشاء اختصار سطح المكتب: {shortcut_file}")
            else:
                print("⚠️ لم يتم العثور على سطح المكتب")
                
        except Exception as e:
            print(f"⚠️ لم يتم إنشاء اختصار سطح المكتب: {e}")
            
    def run_installation(self):
        """Run the complete installation process"""
        self.print_header()
        
        # Check Python
        if not self.check_python():
            input("اضغط Enter للخروج...")
            return False
            
        # Install requirements
        if not self.install_requirements():
            input("اضغط Enter للخروج...")
            return False
            
        # Copy files
        self.copy_files()
        
        # Create startup scripts
        self.create_startup_script()
        
        # Create desktop shortcut
        self.create_desktop_shortcut()
        
        print("\n" + "=" * 80)
        print("🎉 تم تثبيت النظام بنجاح!")
        print("=" * 80)
        print(f"📁 مجلد التثبيت: {self.install_dir}")
        print(f"🚀 لتشغيل النظام: انقر نقراً مزدوجاً على 'تشغيل_النظام.bat'")
        print("🌐 أو افتح المتصفح واذهب إلى: http://127.0.0.1:8888")
        print("🔐 المستخدم الافتراضي: admin | كلمة المرور: admin123")
        print("=" * 80)
        
        # Ask if user wants to start the system now
        start_now = input("\n❓ هل تريد تشغيل النظام الآن؟ (y/n): ").lower()
        if start_now in ['y', 'yes', 'نعم', 'ن']:
            print("🚀 جاري تشغيل النظام...")
            os.chdir(self.install_dir)
            subprocess.Popen([self.python_exe, "run_autoparts.py"])
            
        return True

def main():
    installer = AutoPartsInstaller()
    installer.run_installation()
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
