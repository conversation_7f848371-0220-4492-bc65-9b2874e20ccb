# -*- coding: utf-8 -*-
"""
Simple installation script for AutoParts Manager
"""
import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🚗 AutoParts Manager V5.1 - Installation")
    print("=" * 50)
    
    # Required packages
    required_packages = [
        "Flask==2.3.3",
        "Flask-SQLAlchemy==3.0.5", 
        "Flask-Login==0.6.3",
        "Werkzeug==2.3.7"
    ]
    
    # Optional packages
    optional_packages = [
        "python-barcode",
        "Pillow",
        "reportlab", 
        "openpyxl",
        "pandas",
        "hijri-converter"
    ]
    
    print("📦 Installing required packages...")
    for package in required_packages:
        print(f"   Installing {package}...")
        if install_package(package):
            print(f"   ✅ {package} installed successfully")
        else:
            print(f"   ❌ Failed to install {package}")
            return False
    
    print("\n📦 Installing optional packages...")
    for package in optional_packages:
        print(f"   Installing {package}...")
        if install_package(package):
            print(f"   ✅ {package} installed successfully")
        else:
            print(f"   ⚠️  {package} installation failed (optional)")
    
    print("\n🎉 Installation completed!")
    print("🚀 You can now run the system with:")
    print("   python run_simple.py")
    print("   or")
    print("   python app.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ Installation failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Installation cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    input("\nPress Enter to exit...")
