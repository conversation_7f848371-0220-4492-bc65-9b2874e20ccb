{% extends "base.html" %}

{% block title %}إدارة المنتجات - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-boxes me-2"></i>
        إدارة المنتجات
    </h1>
    <div>
        <a href="{{ url_for('products.add') }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i>
            إضافة منتج
        </a>
        <a href="{{ url_for('products.categories') }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-tags me-1"></i>
            التصنيفات
        </a>
        <div class="btn-group me-2" role="group">
            <a href="{{ url_for('products.export') }}" class="btn btn-outline-success">
                <i class="fas fa-file-excel me-1"></i>
                تصدير Excel
            </a>
            <button type="button" class="btn btn-outline-success dropdown-toggle dropdown-toggle-split"
                    data-bs-toggle="dropdown" aria-expanded="false">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="{{ url_for('products.download_template') }}">
                        <i class="fas fa-download me-2"></i>تحميل قالب الاستيراد
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{{ url_for('products.import_excel') }}">
                        <i class="fas fa-file-import me-2"></i>استيراد من Excel
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search"
                       value="{{ search }}" placeholder="ابحث بالاسم أو الباركود...">
            </div>
            <div class="col-md-3">
                <label class="form-label">التصنيف</label>
                <select class="form-select" name="category_id">
                    <option value="">جميع التصنيفات</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}"
                            {% if category.id == category_id %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المنتجات</h5>
    </div>
    <div class="card-body">
        {% if products.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>التصنيف</th>
                        <th>الباركود</th>
                        <th>سعر البيع</th>
                        <th>الكمية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products.items %}
                    <tr {% if product.is_low_stock %}class="table-warning"{% endif %}>
                        <td>
                            <div class="product-image">
                                <i class="fas fa-box fa-2x text-muted"></i>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ product.name }}</strong>
                                {% if product.description %}
                                <br><small class="text-muted">{{ product.description[:50] }}...</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if product.category %}
                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <code>{{ product.barcode }}</code>
                            <br>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('products.barcode', id=product.id) }}"
                                   class="btn btn-outline-primary" target="_blank" title="عرض الباركود">
                                    <i class="fas fa-barcode"></i>
                                </a>
                                <button type="button" class="btn btn-outline-success"
                                        onclick="printBarcode({{ product.id }})" title="طباعة الباركود">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            <strong>{{ product.selling_price|currency }}</strong>
                            {% if product.cost_price > 0 %}
                            <br><small class="text-muted">التكلفة: {{ product.cost_price|currency }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge {% if product.is_low_stock %}bg-warning{% elif product.quantity == 0 %}bg-danger{% else %}bg-success{% endif %}">
                                {{ product.quantity }} {{ product.unit }}
                            </span>
                            {% if product.is_low_stock %}
                            <br><small class="text-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                مخزون منخفض
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('products.edit', id=product.id) }}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete({{ product.id }}, '{{ product.name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products.index', page=products.prev_num, search=search, category_id=category_id) }}">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products.index', page=page_num, search=search, category_id=category_id) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if products.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('products.index', page=products.next_num, search=search, category_id=category_id) }}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">ابدأ بإضافة منتجات جديدة</p>
            <a href="{{ url_for('products.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(productId, productName) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
        window.location.href = `/products/delete/${productId}`;
    }
}

function printBarcode(productId) {
    // Show modal to ask for number of copies
    const count = prompt('كم نسخة تريد طباعتها؟', '1');

    if (count && !isNaN(count) && parseInt(count) > 0) {
        const copies = Math.min(parseInt(count), 100); // Limit to 100 copies
        const url = `/products/print-barcode/${productId}?count=${copies}&auto_print=true`;

        // Open in new window for printing
        const printWindow = window.open(url, '_blank', 'width=800,height=600');

        // Focus on the new window
        if (printWindow) {
            printWindow.focus();
        }
    }
}
</script>
{% endblock %}
