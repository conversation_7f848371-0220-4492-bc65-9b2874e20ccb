{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="text-muted">
        <i class="fas fa-calendar-alt me-1"></i>
        <span id="current-date"></span>
        <span class="me-3"></span>
        <i class="fas fa-clock me-1"></i>
        <span id="current-time"></span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.total_products }}</div>
                    <div class="stats-label">إجمالي المنتجات</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-boxes fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.total_customers }}</div>
                    <div class="stats-label">العملاء</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.today_sales }}</div>
                    <div class="stats-label">مبيعات اليوم</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-shopping-cart fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">{{ stats.low_stock_count }}</div>
                    <div class="stats-label">مخزون منخفض</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Sales -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    المبيعات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('pos.invoice', sale_id=sale.id) }}" class="text-decoration-none">
                                        {{ sale.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ sale.customer.name if sale.customer else 'عميل نقدي' }}</td>
                                <td>{{ sale.total_amount|currency }}</td>
                                <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge bg-success">مكتملة</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مبيعات حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions & Alerts -->
    <div class="col-lg-4 mb-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('pos.index') }}" class="btn btn-primary">
                        <i class="fas fa-cash-register me-2"></i>
                        نقطة البيع
                    </a>

                    {% if has_permission('all') %}
                    <a href="{{ url_for('products.add') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج
                    </a>

                    <a href="{{ url_for('customers.add') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة عميل
                    </a>

                    <a href="{{ url_for('reports.sales_report') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقرير المبيعات
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Low Stock Alert -->
        {% if low_stock_products %}
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيه مخزون منخفض
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for product in low_stock_products[:5] %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">{{ product.name }}</h6>
                            <small class="text-muted">الكمية المتبقية: {{ product.quantity }}</small>
                        </div>
                        <span class="badge bg-warning rounded-pill">{{ product.quantity }}</span>
                    </div>
                    {% endfor %}
                </div>

                {% if low_stock_products|length > 5 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('reports.inventory_report', low_stock_only=true) }}" class="btn btn-sm btn-outline-warning">
                        عرض الكل ({{ low_stock_products|length }})
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Cash Balance -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    رصيد الصندوق
                </h5>
            </div>
            <div class="card-body text-center">
                <h2 class="text-primary mb-0">{{ cash_balance|currency }}</h2>
                <p class="text-muted">الرصيد الحالي</p>

                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-success">{{ monthly_sales_amount|currency }}</h5>
                        <small class="text-muted">مبيعات الشهر</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-danger">{{ monthly_expenses|currency }}</h5>
                        <small class="text-muted">مصروفات الشهر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    المبيعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Update current time and date
    function updateDateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA');
        const dateString = now.toLocaleDateString('ar-SA');

        document.getElementById('current-time').textContent = timeString;
        document.getElementById('current-date').textContent = dateString;
    }

    updateDateTime();
    setInterval(updateDateTime, 1000);

    // Sales Chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesData = {{ monthly_sales|tojson }};

    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: salesData.map(item => item.month),
            datasets: [{
                label: 'المبيعات',
                data: salesData.map(item => item.amount),
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ريال';
                        }
                    }
                }
            }
        }
    });
</script>
{% endblock %}
