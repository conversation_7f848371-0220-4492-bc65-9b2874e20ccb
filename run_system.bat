@echo off
chcp 65001 >nul
title نظام إدارة قطع الغيار - تشغيل مبسط

echo.
echo ============================================================
echo 🚀 نظام إدارة قطع الغيار - AutoParts Manager V5.1
echo ============================================================
echo    تشغيل مبسط للمستخدمين
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Python أولاً من:
    echo    https://www.python.org/downloads/
    echo.
    echo 💡 أو شغل ملف install.bat لتثبيت النظام كاملاً
    echo.
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "app.py" (
    echo ❌ ملفات النظام غير موجودة
    echo 🔍 تأكد من وجود ملف app.py في نفس المجلد
    echo.
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ ملف المتطلبات غير موجود
    echo 🔍 تأكد من وجود ملف requirements.txt
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص المتطلبات الأساسية...

REM Check if Flask is installed
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  المتطلبات غير مثبتة
    echo 🔧 جاري تثبيت المتطلبات الأساسية...
    
    pip install Flask Flask-SQLAlchemy Flask-Login Werkzeug
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات الأساسية
        echo 💡 جرب تشغيل install.bat للتثبيت الكامل
        pause
        exit /b 1
    )
    
    echo ✅ تم تثبيت المتطلبات الأساسية
)

echo 🔍 فحص المتطلبات الإضافية...

REM Check additional requirements
python -c "import reportlab, openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔧 تثبيت المتطلبات الإضافية...
    pip install reportlab openpyxl pandas python-barcode Pillow
)

echo 🗄️ فحص قاعدة البيانات...

REM Check if database exists
if not exist "autoparts.db" (
    echo 🔨 إنشاء قاعدة البيانات الأولية...
    
    if exist "init_db.py" (
        python init_db.py
    ) else (
        python -c "from app import app, db; app.app_context().push(); db.create_all(); print('✅ تم إنشاء قاعدة البيانات')"
    )
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
    
    echo ✅ تم إنشاء قاعدة البيانات بنجاح
) else (
    echo ✅ قاعدة البيانات موجودة
)

echo 📁 إنشاء المجلدات المطلوبة...

REM Create required directories
if not exist "exports" mkdir exports
if not exist "exports\reports" mkdir exports\reports
if not exist "exports\excel" mkdir exports\excel
if not exist "backups" mkdir backups
if not exist "uploads" mkdir uploads
if not exist "static\uploads" mkdir static\uploads
if not exist "logs" mkdir logs

echo ✅ تم إنشاء جميع المجلدات

echo.
echo ============================================================
echo 🌐 بدء تشغيل النظام...
echo ============================================================
echo.
echo 💡 معلومات الوصول:
echo    📍 العنوان المحلي: http://127.0.0.1:8888
echo    📍 عنوان الشبكة: http://192.168.x.x:8888
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo    👤 المدير: admin / admin123
echo    👤 المحاسب: accountant / acc123  
echo    👤 الكاشير: cashier / cash123
echo.
echo ⚠️  لإيقاف النظام: اضغط Ctrl+C في هذه النافذة
echo.
echo ============================================================
echo.

REM Start the application
echo 🚀 جاري تشغيل النظام...
echo.

python app.py

REM If we reach here, the application has stopped
echo.
echo ============================================================
echo 🛑 تم إيقاف النظام
echo ============================================================
echo.
echo 💡 لإعادة التشغيل: شغل هذا الملف مرة أخرى
echo 🔧 للمساعدة: راجع ملف README.md أو INSTALLATION_GUIDE.md
echo.
pause
