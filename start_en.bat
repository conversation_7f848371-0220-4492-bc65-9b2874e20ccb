@echo off
title AutoParts Manager V5.1 - System Startup

echo.
echo ============================================================
echo AutoParts Manager V5.1 - System Startup
echo ============================================================
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found
    echo Please run install_en.bat first to install the system
    echo.
    pause
    exit /b 1
)

REM Check if database exists
if not exist "autoparts.db" (
    echo WARNING: Database not found
    echo Creating database...
    
    call venv\Scripts\activate.bat
    python -c "from app import app, db; app.app_context().push(); db.create_all(); print('SUCCESS: Database created')"
    
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create database
        pause
        exit /b 1
    )
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Checking requirements...
python -c "import flask, flask_sqlalchemy, reportlab, openpyxl; print('SUCCESS: All requirements available')" 2>nul

if %errorlevel% neq 0 (
    echo WARNING: Some requirements are missing
    echo Installing missing requirements...
    pip install -r requirements.txt
)

echo.
echo ============================================================
echo Starting system...
echo ============================================================
echo.
echo System can be accessed from:
echo    Local: http://127.0.0.1:8888
echo    Network: http://192.168.x.x:8888
echo.
echo Default login credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo To stop the system: Press Ctrl+C
echo.
echo ============================================================

REM Start the application
python app.py

echo.
echo ============================================================
echo System stopped
echo ============================================================
pause
