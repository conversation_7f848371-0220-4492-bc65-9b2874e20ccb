#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوظائف التصدير والاستيراد في نظام إدارة قطع الغيار
Test Export and Import Functions for AutoParts Manager
"""

import os
import sys
import requests
import time
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class ExportImportTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_products_export(self):
        """اختبار تصدير المنتجات إلى Excel"""
        print("\n🔄 اختبار تصدير المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/export")
            if response.status_code == 200 or response.status_code == 302:
                print("✅ تصدير المنتجات إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير المنتجات: {e}")
            return False
    
    def test_customers_export(self):
        """اختبار تصدير العملاء إلى Excel"""
        print("\n🔄 اختبار تصدير العملاء...")
        try:
            response = self.session.get(f"{BASE_URL}/customers/export")
            if response.status_code == 200 or response.status_code == 302:
                print("✅ تصدير العملاء إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير العملاء: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير العملاء: {e}")
            return False
    
    def test_suppliers_export(self):
        """اختبار تصدير الموردين إلى Excel"""
        print("\n🔄 اختبار تصدير الموردين...")
        try:
            response = self.session.get(f"{BASE_URL}/suppliers/export")
            if response.status_code == 200 or response.status_code == 302:
                print("✅ تصدير الموردين إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير الموردين: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير الموردين: {e}")
            return False
    
    def test_sales_report_excel(self):
        """اختبار تصدير تقرير المبيعات إلى Excel"""
        print("\n🔄 اختبار تصدير تقرير المبيعات إلى Excel...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/sales-report?export=excel")
            if response.status_code == 200:
                print("✅ تصدير تقرير المبيعات إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير تقرير المبيعات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير تقرير المبيعات: {e}")
            return False
    
    def test_sales_report_pdf(self):
        """اختبار تصدير تقرير المبيعات إلى PDF"""
        print("\n🔄 اختبار تصدير تقرير المبيعات إلى PDF...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/sales-report?export=pdf")
            if response.status_code == 200:
                print("✅ تصدير تقرير المبيعات إلى PDF يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير تقرير المبيعات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير تقرير المبيعات: {e}")
            return False
    
    def test_inventory_report_excel(self):
        """اختبار تصدير تقرير المخزون إلى Excel"""
        print("\n🔄 اختبار تصدير تقرير المخزون إلى Excel...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/inventory-report?export=excel")
            if response.status_code == 200:
                print("✅ تصدير تقرير المخزون إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير تقرير المخزون: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير تقرير المخزون: {e}")
            return False
    
    def test_profit_loss_excel(self):
        """اختبار تصدير قائمة الدخل إلى Excel"""
        print("\n🔄 اختبار تصدير قائمة الدخل إلى Excel...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/profit-loss?export=excel")
            if response.status_code == 200:
                print("✅ تصدير قائمة الدخل إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير قائمة الدخل: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير قائمة الدخل: {e}")
            return False
    
    def test_profit_loss_pdf(self):
        """اختبار تصدير قائمة الدخل إلى PDF"""
        print("\n🔄 اختبار تصدير قائمة الدخل إلى PDF...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/profit-loss?export=pdf")
            if response.status_code == 200:
                print("✅ تصدير قائمة الدخل إلى PDF يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير قائمة الدخل: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير قائمة الدخل: {e}")
            return False
    
    def test_cash_flow_excel(self):
        """اختبار تصدير قائمة التدفق النقدي إلى Excel"""
        print("\n🔄 اختبار تصدير قائمة التدفق النقدي إلى Excel...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/cash-flow?export=excel")
            if response.status_code == 200:
                print("✅ تصدير قائمة التدفق النقدي إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير قائمة التدفق النقدي: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير قائمة التدفق النقدي: {e}")
            return False
    
    def test_cash_flow_pdf(self):
        """اختبار تصدير قائمة التدفق النقدي إلى PDF"""
        print("\n🔄 اختبار تصدير قائمة التدفق النقدي إلى PDF...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/cash-flow?export=pdf")
            if response.status_code == 200:
                print("✅ تصدير قائمة التدفق النقدي إلى PDF يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير قائمة التدفق النقدي: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير قائمة التدفق النقدي: {e}")
            return False
    
    def test_balance_sheet_excel(self):
        """اختبار تصدير الميزانية العمومية إلى Excel"""
        print("\n🔄 اختبار تصدير الميزانية العمومية إلى Excel...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/balance-sheet?export=excel")
            if response.status_code == 200:
                print("✅ تصدير الميزانية العمومية إلى Excel يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير الميزانية العمومية: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير الميزانية العمومية: {e}")
            return False
    
    def test_balance_sheet_pdf(self):
        """اختبار تصدير الميزانية العمومية إلى PDF"""
        print("\n🔄 اختبار تصدير الميزانية العمومية إلى PDF...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/balance-sheet?export=pdf")
            if response.status_code == 200:
                print("✅ تصدير الميزانية العمومية إلى PDF يعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في تصدير الميزانية العمومية: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تصدير الميزانية العمومية: {e}")
            return False
    
    def test_download_templates(self):
        """اختبار تحميل قوالب الاستيراد"""
        print("\n🔄 اختبار تحميل قوالب الاستيراد...")
        
        templates = [
            ("/customers/download-template", "قالب العملاء"),
            ("/suppliers/download-template", "قالب الموردين")
        ]
        
        success_count = 0
        for url, name in templates:
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200 or response.status_code == 302:
                    print(f"✅ تحميل {name} يعمل بنجاح")
                    success_count += 1
                else:
                    print(f"❌ فشل في تحميل {name}: {response.status_code}")
            except Exception as e:
                print(f"❌ خطأ في تحميل {name}: {e}")
        
        return success_count == len(templates)
    
    def test_import_pages(self):
        """اختبار صفحات الاستيراد"""
        print("\n🔄 اختبار صفحات الاستيراد...")
        
        import_pages = [
            ("/customers/import-excel", "صفحة استيراد العملاء"),
            ("/suppliers/import-excel", "صفحة استيراد الموردين")
        ]
        
        success_count = 0
        for url, name in import_pages:
            try:
                response = self.session.get(f"{BASE_URL}{url}")
                if response.status_code == 200:
                    print(f"✅ {name} تعمل بنجاح")
                    success_count += 1
                else:
                    print(f"❌ فشل في الوصول إلى {name}: {response.status_code}")
            except Exception as e:
                print(f"❌ خطأ في الوصول إلى {name}: {e}")
        
        return success_count == len(import_pages)
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار وظائف التصدير والاستيراد")
        print("=" * 60)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            self.test_products_export,
            self.test_customers_export,
            self.test_suppliers_export,
            self.test_sales_report_excel,
            self.test_sales_report_pdf,
            self.test_inventory_report_excel,
            self.test_profit_loss_excel,
            self.test_profit_loss_pdf,
            self.test_cash_flow_excel,
            self.test_cash_flow_pdf,
            self.test_balance_sheet_excel,
            self.test_balance_sheet_pdf,
            self.test_download_templates,
            self.test_import_pages
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل الاختبار: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف التصدير والاستيراد تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️  يوجد {failed} وظيفة تحتاج إلى إصلاح")
            return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار وظائف التصدير والاستيراد - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = ExportImportTester()
    success = tester.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
