{% extends "base.html" %}

{% block title %}حركات المخزون{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-exchange-alt me-2"></i>حركات المخزون</h2>
                <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلترة النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="product_id" class="form-label">المنتج</label>
                            <select class="form-select" id="product_id" name="product_id">
                                <option value="">جميع المنتجات</option>
                                {% for product in products %}
                                <option value="{{ product.id }}" {% if product_id == product.id %}selected{% endif %}>
                                    {{ product.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="transaction_type" class="form-label">نوع الحركة</label>
                            <select class="form-select" id="transaction_type" name="transaction_type">
                                <option value="">جميع الأنواع</option>
                                <option value="in" {% if transaction_type == 'in' %}selected{% endif %}>دخول</option>
                                <option value="out" {% if transaction_type == 'out' %}selected{% endif %}>خروج</option>
                                <option value="adjustment" {% if transaction_type == 'adjustment' %}selected{% endif %}>تعديل</option>
                                <option value="transfer" {% if transaction_type == 'transfer' %}selected{% endif %}>تحويل</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="from_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="from_date" name="from_date" value="{{ from_date }}">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="to_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="to_date" name="to_date" value="{{ to_date }}">
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                                <a href="{{ url_for('inventory.transactions') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل حركات المخزون
                        {% if transactions.total %}
                        <span class="badge bg-light text-dark ms-2">{{ transactions.total }} حركة</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if transactions.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ والوقت</th>
                                    <th>المنتج</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>المرجع</th>
                                    <th>ملاحظات</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions.items %}
                                <tr>
                                    <td>
                                        {{ transaction.transaction_date.strftime('%Y-%m-%d') }}
                                        <br>
                                        <small class="text-muted">{{ transaction.transaction_date.strftime('%H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ transaction.product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ transaction.product.barcode }}</small>
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'in' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-arrow-down me-1"></i>دخول
                                            </span>
                                        {% elif transaction.transaction_type == 'out' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-arrow-up me-1"></i>خروج
                                            </span>
                                        {% elif transaction.transaction_type == 'adjustment' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-balance-scale me-1"></i>تعديل
                                            </span>
                                        {% elif transaction.transaction_type == 'transfer' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-exchange-alt me-1"></i>تحويل
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ transaction.transaction_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'out' %}
                                            <span class="text-danger">-{{ "{:,.2f}".format(transaction.quantity) }}</span>
                                        {% else %}
                                            <span class="text-success">+{{ "{:,.2f}".format(transaction.quantity) }}</span>
                                        {% endif %}
                                        <small class="text-muted">{{ transaction.product.unit }}</small>
                                    </td>
                                    <td>
                                        {% if transaction.reference_type and transaction.reference_id %}
                                            <span class="badge bg-light text-dark">
                                                {{ transaction.reference_type }} #{{ transaction.reference_id }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.notes %}
                                            {{ transaction.notes[:50] }}{% if transaction.notes|length > 50 %}...{% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.user.full_name }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if transactions.pages > 1 %}
                    <nav aria-label="صفحات حركات المخزون">
                        <ul class="pagination justify-content-center">
                            {% if transactions.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.transactions', page=transactions.prev_num, product_id=product_id, transaction_type=transaction_type, from_date=from_date, to_date=to_date) }}">السابق</a>
                            </li>
                            {% endif %}

                            {% for page_num in transactions.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != transactions.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory.transactions', page=page_num, product_id=product_id, transaction_type=transaction_type, from_date=from_date, to_date=to_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if transactions.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.transactions', page=transactions.next_num, product_id=product_id, transaction_type=transaction_type, from_date=from_date, to_date=to_date) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد حركات مخزون</h5>
                        <p class="text-muted">لم يتم العثور على حركات مخزون تطابق معايير البحث</p>
                        {% if product_id or transaction_type or from_date or to_date %}
                        <a href="{{ url_for('inventory.transactions') }}" class="btn btn-primary">
                            <i class="fas fa-times me-2"></i>مسح الفلاتر
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
