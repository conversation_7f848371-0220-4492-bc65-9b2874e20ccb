{% extends "base.html" %}

{% block title %}استعادة النسخ الاحتياطية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-upload me-2"></i>
                        استعادة النسخ الاحتياطية
                    </h3>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للإعدادات
                    </a>
                </div>

                <div class="card-body">
                    <!-- Warning Alert -->
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير مهم:</strong> استعادة النسخة الاحتياطية ستؤدي إلى حذف جميع البيانات الحالية واستبدالها بالبيانات من النسخة الاحتياطية. تأكد من إنشاء نسخة احتياطية من البيانات الحالية قبل المتابعة.
                    </div>

                    <!-- Backup Files List -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">النسخ الاحتياطية المتاحة</h5>
                                </div>
                                <div class="card-body">
                                    {% if backup_files %}
                                    <div class="list-group">
                                        {% for backup in backup_files %}
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">{{ backup.name }}</h6>
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ backup.date }}
                                                    <i class="fas fa-hdd ms-3 me-1"></i>
                                                    {{ backup.size }}
                                                </small>
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-info"
                                                        onclick="viewBackupInfo('{{ backup.name }}')">
                                                    <i class="fas fa-info"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success"
                                                        onclick="restoreBackup('{{ backup.name }}')">
                                                    <i class="fas fa-upload"></i> استعادة
                                                </button>
                                                <button type="button" class="btn btn-outline-danger"
                                                        onclick="deleteBackup('{{ backup.name }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                                        <p class="text-muted">لم يتم العثور على أي نسخ احتياطية</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Upload Backup File -->
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">رفع نسخة احتياطية</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" enctype="multipart/form-data" action="{{ url_for('settings.restore') }}">
                                        <div class="mb-3">
                                            <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية</label>
                                            <input type="file" class="form-control" id="backup_file" name="backup_file"
                                                   accept=".sql,.db,.backup" required>
                                            <div class="form-text">الملفات المدعومة: .sql, .db, .backup</div>
                                        </div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="confirm_upload" required>
                                            <label class="form-check-label" for="confirm_upload">
                                                أؤكد أنني أريد رفع هذا الملف
                                            </label>
                                        </div>
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-upload"></i> رفع الملف
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Create New Backup -->
                            <div class="card border-info mt-3">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">إنشاء نسخة احتياطية جديدة</h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">قم بإنشاء نسخة احتياطية من البيانات الحالية قبل استعادة نسخة أخرى</p>
                                    <button type="button" class="btn btn-info w-100" onclick="createBackup()">
                                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Restore Progress -->
                    <div id="restoreProgress" class="mt-4" style="display: none;">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">جاري الاستعادة...</h5>
                            </div>
                            <div class="card-body">
                                <div class="progress mb-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                                <div id="progressText">بدء عملية الاستعادة...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Backup Info Modal -->
<div class="modal fade" id="backupInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معلومات النسخة الاحتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="backupInfoContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Restore Confirmation Modal -->
<div class="modal fade" id="restoreConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">تأكيد الاستعادة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء سيحذف جميع البيانات الحالية ولا يمكن التراجع عنه!
                </div>
                <p>هل أنت متأكد من استعادة النسخة الاحتياطية: <strong id="backupNameToRestore"></strong>؟</p>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirmRestore" required>
                    <label class="form-check-label" for="confirmRestore">
                        أؤكد أنني أريد استعادة هذه النسخة الاحتياطية وحذف البيانات الحالية
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRestoreBtn" onclick="executeRestore()">
                    استعادة النسخة الاحتياطية
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let backupToRestore = '';

function viewBackupInfo(backupName) {
    // For now, show basic info - can be enhanced later
    const content = `
        <table class="table table-sm">
            <tr><td><strong>اسم الملف:</strong></td><td>${backupName}</td></tr>
            <tr><td><strong>الحالة:</strong></td><td>متاح للاستعادة</td></tr>
        </table>
    `;
    document.getElementById('backupInfoContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('backupInfoModal')).show();
}

function restoreBackup(backupName) {
    backupToRestore = backupName;
    document.getElementById('backupNameToRestore').textContent = backupName;
    document.getElementById('confirmRestore').checked = false;
    new bootstrap.Modal(document.getElementById('restoreConfirmModal')).show();
}

function executeRestore() {
    if (!document.getElementById('confirmRestore').checked) {
        alert('يرجى تأكيد الاستعادة');
        return;
    }

    // Hide modal and show progress
    bootstrap.Modal.getInstance(document.getElementById('restoreConfirmModal')).hide();
    document.getElementById('restoreProgress').style.display = 'block';

    // Simulate restore process for now
    updateProgress(25, 'جاري قراءة ملف النسخة الاحتياطية...');
    setTimeout(() => {
        updateProgress(50, 'جاري حذف البيانات الحالية...');
        setTimeout(() => {
            updateProgress(75, 'جاري استعادة البيانات...');
            setTimeout(() => {
                updateProgress(100, 'تمت الاستعادة بنجاح! سيتم إعادة تحميل الصفحة...');
                setTimeout(() => {
                    alert('تم محاكاة عملية الاستعادة بنجاح');
                    window.location.reload();
                }, 2000);
            }, 1000);
        }, 1000);
    }, 1000);
}

function updateProgress(percent, text) {
    const progressBar = document.querySelector('#restoreProgress .progress-bar');
    const progressText = document.getElementById('progressText');

    progressBar.style.width = percent + '%';
    progressText.textContent = text;

    if (percent === 100) {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-success');
    } else if (percent === 0 && text.includes('خطأ')) {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
    }
}

function deleteBackup(backupName) {
    if (confirm(`هل أنت متأكد من حذف النسخة الاحتياطية: ${backupName}؟`)) {
        alert('تم محاكاة حذف النسخة الاحتياطية بنجاح');
        location.reload();
    }
}

function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من البيانات الحالية؟')) {
        alert('تم محاكاة إنشاء النسخة الاحتياطية بنجاح');
        location.reload();
    }
}
</script>
{% endblock %}
