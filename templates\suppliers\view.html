{% extends "base.html" %}

{% block title %}{{ supplier.name }} - تفاصيل المورد{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-truck me-2"></i>
        تفاصيل المورد: {{ supplier.name }}
    </h1>
    <div>
        <a href="{{ url_for('suppliers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
        {% if has_permission('all') %}
        <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" class="btn btn-warning">
            <i class="fas fa-edit me-2"></i>
            تعديل
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Supplier Info -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المورد
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                    <h4 class="mt-2 mb-0">{{ supplier.name }}</h4>
                    {% if supplier.company_name %}
                    <p class="text-muted">{{ supplier.company_name }}</p>
                    {% endif %}
                    <span class="badge {% if supplier.supplier_type == 'individual' %}bg-info{% else %}bg-success{% endif %}">
                        {% if supplier.supplier_type == 'individual' %}فردي{% else %}شركة{% endif %}
                    </span>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="{% if supplier.balance > 0 %}text-success{% elif supplier.balance < 0 %}text-danger{% else %}text-muted{% endif %}">
                            {{ supplier.balance|currency }}
                        </h4>
                        <p class="text-muted mb-0">الرصيد الحالي</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ supplier.payment_terms or 'نقدي' }}</h4>
                        <p class="text-muted mb-0">شروط الدفع</p>
                    </div>
                </div>

                <hr>

                <div class="info-list">
                    {% if supplier.phone %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone text-muted me-2"></i>
                        <a href="tel:{{ supplier.phone }}" class="text-decoration-none">{{ supplier.phone }}</a>
                    </div>
                    {% endif %}

                    {% if supplier.email %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-envelope text-muted me-2"></i>
                        <a href="mailto:{{ supplier.email }}" class="text-decoration-none">{{ supplier.email }}</a>
                    </div>
                    {% endif %}

                    {% if supplier.address %}
                    <div class="d-flex align-items-start mb-2">
                        <i class="fas fa-map-marker-alt text-muted me-2 mt-1"></i>
                        <span>{{ supplier.address }}</span>
                    </div>
                    {% endif %}

                    {% if supplier.tax_number %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-file-invoice text-muted me-2"></i>
                        <span>{{ supplier.tax_number }}</span>
                    </div>
                    {% endif %}

                    {% if supplier.bank_account %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-university text-muted me-2"></i>
                        <span>{{ supplier.bank_account }} - {{ supplier.bank_name or 'غير محدد' }}</span>
                    </div>
                    {% endif %}

                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-calendar text-muted me-2"></i>
                        <span>{{ supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد' }}</span>
                    </div>
                </div>

                {% if supplier.notes %}
                <hr>
                <h6>ملاحظات:</h6>
                <p class="text-muted">{{ supplier.notes }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('purchases.add', supplier_id=supplier.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-shopping-cart me-2"></i>
                        شراء من هذا المورد
                    </a>
                    <a href="{{ url_for('suppliers.statement', supplier_id=supplier.id) }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-file-alt me-2"></i>
                        كشف حساب
                    </a>
                    {% if has_permission('accounting') %}
                    <a href="{{ url_for('accounting.payment', supplier_id=supplier.id) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-money-bill me-2"></i>
                        تسجيل دفعة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions History -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    سجل المعاملات
                </h5>
            </div>
            <div class="card-body">
                {% if transactions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.date.strftime('%Y-%m-%d %H:%M') if transaction.date else 'غير محدد' }}</td>
                                <td>
                                    {% if transaction.type == 'purchase' %}
                                        <span class="badge bg-primary">مشتريات</span>
                                    {% elif transaction.type == 'payment' %}
                                        <span class="badge bg-success">دفعة</span>
                                    {% elif transaction.type == 'return' %}
                                        <span class="badge bg-warning">مرتجع</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ transaction.type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.description or 'غير محدد' }}</td>
                                <td>
                                    {% if transaction.amount > 0 %}
                                        <span class="text-success">+{{ transaction.amount|currency }}</span>
                                    {% else %}
                                        <span class="text-danger">{{ transaction.amount|currency }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.balance|currency }}</td>
                                <td>
                                    {% if transaction.reference_id %}
                                    <a href="{{ url_for('purchases.invoice', purchase_id=transaction.reference_id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="عرض الفاتورة">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد معاملات</h5>
                    <p class="text-muted">لم يتم تسجيل أي معاملات مع هذا المورد بعد</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ supplier_stats.total_purchases or 0 }}</h4>
                        <p class="mb-0">إجمالي المشتريات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ supplier_stats.total_payments|currency or '0.00 ريال' }}</h4>
                        <p class="mb-0">إجمالي المدفوعات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ supplier_stats.last_purchase_date.strftime('%Y-%m-%d') if supplier_stats.last_purchase_date else 'لا يوجد' }}</h4>
                        <p class="mb-0">آخر عملية شراء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ supplier_stats.avg_purchase_amount|currency or '0.00 ريال' }}</h4>
                        <p class="mb-0">متوسط قيمة الشراء</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.info-list .fas {
    width: 20px;
}
</style>
{% endblock %}
