@echo off
chcp 65001 >nul
title تجميع النظام للتوزيع - AutoParts Manager V5.1

echo.
echo ============================================================
echo 📦 تجميع نظام إدارة قطع الغيار للتوزيع
echo    AutoParts Manager V5.1
echo ============================================================
echo.
echo 🎯 سيتم إنشاء حزمة كاملة جاهزة للتوزيع
echo    تحتوي على جميع الملفات والأدوات المطلوبة
echo.
pause

echo.
echo ============================================================
echo 🔍 فحص الملفات المطلوبة...
echo ============================================================

REM Check required files
set "missing_files="

if not exist "app.py" (
    echo ❌ app.py مفقود
    set "missing_files=1"
) else (
    echo ✅ app.py
)

if not exist "models.py" (
    echo ❌ models.py مفقود
    set "missing_files=1"
) else (
    echo ✅ models.py
)

if not exist "utils.py" (
    echo ❌ utils.py مفقود
    set "missing_files=1"
) else (
    echo ✅ utils.py
)

if not exist "requirements.txt" (
    echo ❌ requirements.txt مفقود
    set "missing_files=1"
) else (
    echo ✅ requirements.txt
)

if not exist "templates" (
    echo ❌ مجلد templates مفقود
    set "missing_files=1"
) else (
    echo ✅ templates/
)

if not exist "static" (
    echo ❌ مجلد static مفقود
    set "missing_files=1"
) else (
    echo ✅ static/
)

if not exist "routes" (
    echo ❌ مجلد routes مفقود
    set "missing_files=1"
) else (
    echo ✅ routes/
)

if defined missing_files (
    echo.
    echo ❌ بعض الملفات المطلوبة مفقودة
    echo 🔧 تأكد من وجود جميع ملفات النظام
    pause
    exit /b 1
)

echo.
echo ✅ جميع الملفات المطلوبة موجودة

echo.
echo ============================================================
echo 📁 إنشاء مجلد التوزيع...
echo ============================================================

set "DIST_DIR=AutoParts_Manager_V5.1_Distribution"
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "PACKAGE_NAME=%DIST_DIR%_%TIMESTAMP%"

if exist "%PACKAGE_NAME%" (
    echo 🔄 حذف مجلد التوزيع السابق...
    rmdir /s /q "%PACKAGE_NAME%"
)

echo 🔨 إنشاء مجلد التوزيع: %PACKAGE_NAME%
mkdir "%PACKAGE_NAME%"

echo.
echo ============================================================
echo 📋 نسخ ملفات النظام...
echo ============================================================

echo 🔄 نسخ الملفات الأساسية...
copy "app.py" "%PACKAGE_NAME%\"
copy "models.py" "%PACKAGE_NAME%\"
copy "utils.py" "%PACKAGE_NAME%\"
copy "requirements.txt" "%PACKAGE_NAME%\"
copy "init_db.py" "%PACKAGE_NAME%\"

echo 🔄 نسخ ملفات التثبيت والتشغيل...
copy "install.bat" "%PACKAGE_NAME%\"
copy "install_simple.bat" "%PACKAGE_NAME%\"
copy "start.bat" "%PACKAGE_NAME%\"
copy "run_system.bat" "%PACKAGE_NAME%\"
copy "check_system.py" "%PACKAGE_NAME%\"

echo 🔄 نسخ ملفات الوثائق...
copy "README.md" "%PACKAGE_NAME%\"
copy "INSTALLATION_GUIDE.md" "%PACKAGE_NAME%\"
copy "QUICK_START.md" "%PACKAGE_NAME%\"

echo 🔄 نسخ المجلدات...
xcopy "templates" "%PACKAGE_NAME%\templates\" /E /I /Q
xcopy "static" "%PACKAGE_NAME%\static\" /E /I /Q
xcopy "routes" "%PACKAGE_NAME%\routes\" /E /I /Q

echo 🔄 إنشاء المجلدات الفارغة...
mkdir "%PACKAGE_NAME%\exports"
mkdir "%PACKAGE_NAME%\exports\reports"
mkdir "%PACKAGE_NAME%\exports\excel"
mkdir "%PACKAGE_NAME%\backups"
mkdir "%PACKAGE_NAME%\uploads"
mkdir "%PACKAGE_NAME%\static\uploads"
mkdir "%PACKAGE_NAME%\logs"

echo.
echo ============================================================
echo 📄 إنشاء ملفات إضافية...
echo ============================================================

echo 🔨 إنشاء ملف معلومات الحزمة...
(
echo # 📦 حزمة نظام إدارة قطع الغيار - AutoParts Manager V5.1
echo.
echo ## 📋 معلومات الحزمة
echo - **الإصدار:** 5.1
echo - **تاريخ التجميع:** %date% %time%
echo - **نوع الحزمة:** توزيع كامل
echo.
echo ## 🚀 التثبيت السريع
echo 1. انقر نقراً مزدوجاً على `install.bat`
echo 2. انتظر اكتمال التثبيت
echo 3. انقر نقراً مزدوجاً على `start.bat`
echo 4. افتح المتصفح على: http://127.0.0.1:8888
echo.
echo ## 🔐 بيانات الدخول
echo - **المدير:** admin / admin123
echo - **المحاسب:** accountant / acc123
echo - **الكاشير:** cashier / cash123
echo.
echo ## 📖 المساعدة
echo - راجع ملف `README.md` للتفاصيل الكاملة
echo - راجع ملف `INSTALLATION_GUIDE.md` لحل المشاكل
echo - راجع ملف `QUICK_START.md` للبدء السريع
) > "%PACKAGE_NAME%\PACKAGE_INFO.md"

echo 🔨 إنشاء ملف تشغيل سريع...
(
echo @echo off
echo chcp 65001 ^>nul
echo title نظام إدارة قطع الغيار - تشغيل سريع
echo.
echo echo ============================================================
echo echo 🚀 نظام إدارة قطع الغيار - AutoParts Manager V5.1
echo echo ============================================================
echo echo.
echo echo 💡 للتثبيت الأول: انقر على install.bat
echo echo 🚀 للتشغيل العادي: انقر على start.bat
echo echo.
echo echo 🌐 عنوان النظام: http://127.0.0.1:8888
echo echo 🔐 المدير: admin / admin123
echo echo.
echo pause
) > "%PACKAGE_NAME%\RUN_ME_FIRST.bat"

echo 🔨 إنشاء ملف فحص النظام...
(
echo @echo off
echo chcp 65001 ^>nul
echo title فحص النظام
echo.
echo echo 🔍 فحص جاهزية النظام...
echo echo.
echo python check_system.py
echo pause
) > "%PACKAGE_NAME%\check_system.bat"

echo.
echo ============================================================
echo 🗜️ ضغط الحزمة...
echo ============================================================

REM Check if 7-Zip is available
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    echo 🔄 ضغط الحزمة باستخدام 7-Zip...
    7z a -tzip "%PACKAGE_NAME%.zip" "%PACKAGE_NAME%\*"
    
    if %errorlevel% equ 0 (
        echo ✅ تم إنشاء الملف المضغوط: %PACKAGE_NAME%.zip
    ) else (
        echo ⚠️  فشل في ضغط الحزمة
    )
) else (
    echo ⚠️  7-Zip غير متوفر، لم يتم ضغط الحزمة
    echo 💡 يمكنك ضغط مجلد %PACKAGE_NAME% يدوياً
)

echo.
echo ============================================================
echo 📊 إحصائيات الحزمة...
echo ============================================================

echo 🔍 حساب حجم الحزمة...
for /f "tokens=3" %%a in ('dir "%PACKAGE_NAME%" /s /-c ^| find "File(s)"') do set "TOTAL_SIZE=%%a"
echo 📁 حجم الحزمة: %TOTAL_SIZE% بايت

echo.
echo 🔍 عدد الملفات...
for /f "tokens=1" %%a in ('dir "%PACKAGE_NAME%" /s /-c ^| find "File(s)"') do set "TOTAL_FILES=%%a"
echo 📄 عدد الملفات: %TOTAL_FILES%

echo.
echo ============================================================
echo 🎉 تم تجميع الحزمة بنجاح!
echo ============================================================
echo.
echo 📦 اسم الحزمة: %PACKAGE_NAME%
echo 📁 مجلد الحزمة: %CD%\%PACKAGE_NAME%
if exist "%PACKAGE_NAME%.zip" (
    echo 🗜️ الملف المضغوط: %PACKAGE_NAME%.zip
)
echo.
echo 📋 محتويات الحزمة:
echo    ✅ جميع ملفات النظام
echo    ✅ ملفات التثبيت والتشغيل
echo    ✅ الوثائق والأدلة
echo    ✅ أدوات الفحص والصيانة
echo.
echo 🚀 الحزمة جاهزة للتوزيع!
echo.
echo ============================================================
echo 💡 تعليمات التوزيع:
echo ============================================================
echo.
echo 📤 للمستخدم النهائي:
echo    1. أرسل مجلد %PACKAGE_NAME% أو الملف المضغوط
echo    2. اطلب منه تشغيل RUN_ME_FIRST.bat أولاً
echo    3. ثم تشغيل install.bat للتثبيت
echo    4. ثم start.bat للتشغيل
echo.
echo 🔧 للدعم الفني:
echo    • استخدم check_system.bat لفحص المشاكل
echo    • راجع ملفات الوثائق للمساعدة
echo    • تأكد من وجود Python على جهاز المستخدم
echo.
echo ============================================================

pause

echo.
set /p choice="هل تريد فتح مجلد الحزمة؟ (y/n): "
if /i "%choice%"=="y" (
    explorer "%PACKAGE_NAME%"
)

echo.
echo 🎊 شكراً لاستخدام أداة تجميع النظام!
pause
