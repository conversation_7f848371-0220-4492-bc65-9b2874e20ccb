{% extends "base.html" %}

{% block title %}تقرير المبيعات - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-line me-2"></i>
        تقرير المبيعات
    </h1>
    <div>
        <a href="{{ url_for('reports.sales_report', from_date=from_date, to_date=to_date, customer_id=customer_id, export='excel') }}"
           class="btn btn-success me-2">
            <i class="fas fa-file-excel me-1"></i>
            تصدير Excel
        </a>
        <a href="{{ url_for('reports.sales_report', from_date=from_date, to_date=to_date, customer_id=customer_id, export='pdf') }}"
           class="btn btn-danger">
            <i class="fas fa-file-pdf me-1"></i>
            تصدير PDF
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            فلترة التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="from_date" value="{{ from_date }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="to_date" value="{{ to_date }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">العميل</label>
                <select class="form-select" name="customer_id">
                    <option value="">جميع العملاء</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}"
                            {% if customer.id == customer_id %}selected{% endif %}>
                        {{ customer.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        تطبيق
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ sales|length }}</h4>
                        <p class="mb-0">عدد الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_sales|currency }}</h4>
                        <p class="mb-0">إجمالي المبيعات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_tax|currency }}</h4>
                        <p class="mb-0">إجمالي الضريبة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_discount|currency }}</h4>
                        <p class="mb-0">إجمالي الخصومات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تفاصيل المبيعات</h5>
    </div>
    <div class="card-body">
        {% if sales %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>المبلغ الفرعي</th>
                        <th>الخصم</th>
                        <th>الضريبة</th>
                        <th>الإجمالي</th>
                        <th>طريقة الدفع</th>
                        <th>الكاشير</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales %}
                    <tr>
                        <td>
                            <a href="{{ url_for('pos.invoice', sale_id=sale.id) }}"
                               class="text-decoration-none" target="_blank">
                                {{ sale.invoice_number }}
                            </a>
                        </td>
                        <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            {% if sale.customer %}
                                {{ sale.customer.name }}
                            {% else %}
                                <span class="text-muted">عميل نقدي</span>
                            {% endif %}
                        </td>
                        <td>{{ sale.subtotal|currency }}</td>
                        <td>
                            {% if sale.discount_amount > 0 %}
                                <span class="text-warning">{{ sale.discount_amount|currency }}</span>
                                <br><small class="text-muted">({{ sale.discount_rate }}%)</small>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>{{ sale.tax_amount|currency }}</td>
                        <td><strong>{{ sale.total_amount|currency }}</strong></td>
                        <td>
                            {% if sale.payment_method == 'cash' %}
                                <span class="badge bg-success">نقدي</span>
                            {% elif sale.payment_method == 'credit' %}
                                <span class="badge bg-warning">آجل</span>
                            {% elif sale.payment_method == 'bank' %}
                                <span class="badge bg-info">بنكي</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ sale.payment_method }}</span>
                            {% endif %}
                        </td>
                        <td>{{ sale.user.full_name }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('pos.invoice', sale_id=sale.id) }}"
                                   class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('pos.print_invoice', sale_id=sale.id) }}"
                                   class="btn btn-sm btn-outline-secondary" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="3">الإجمالي</th>
                        <th>{{ sales|sum(attribute='subtotal')|currency }}</th>
                        <th>{{ total_discount|currency }}</th>
                        <th>{{ total_tax|currency }}</th>
                        <th>{{ total_sales|currency }}</th>
                        <th colspan="3"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مبيعات في الفترة المحددة</h5>
            <p class="text-muted">جرب تغيير فترة البحث أو إزالة الفلاتر</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Chart Section -->
{% if sales %}
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">المبيعات اليومية</h5>
            </div>
            <div class="card-body">
                <canvas id="dailySalesChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">طرق الدفع</h5>
            </div>
            <div class="card-body">
                <canvas id="paymentMethodChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if sales %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Prepare data for charts
const salesData = {{ sales_data|tojson }};

// Daily Sales Chart
const dailySalesData = {};
salesData.forEach(sale => {
    const date = sale.sale_date.split(' ')[0];
    if (!dailySalesData[date]) {
        dailySalesData[date] = 0;
    }
    dailySalesData[date] += sale.total_amount;
});

const dailyLabels = Object.keys(dailySalesData).sort();
const dailyValues = dailyLabels.map(date => dailySalesData[date]);

const dailyCtx = document.getElementById('dailySalesChart').getContext('2d');
new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: dailyLabels,
        datasets: [{
            label: 'المبيعات اليومية',
            data: dailyValues,
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// Payment Method Chart
const paymentData = {};
salesData.forEach(sale => {
    const method = sale.payment_method;
    if (!paymentData[method]) {
        paymentData[method] = 0;
    }
    paymentData[method] += sale.total_amount;
});

const paymentLabels = Object.keys(paymentData);
const paymentValues = Object.values(paymentData);
const paymentColors = ['#28a745', '#ffc107', '#17a2b8', '#6c757d'];

const paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: paymentLabels.map(method => {
            switch(method) {
                case 'cash': return 'نقدي';
                case 'credit': return 'آجل';
                case 'bank': return 'بنكي';
                default: return method;
            }
        }),
        datasets: [{
            data: paymentValues,
            backgroundColor: paymentColors,
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
