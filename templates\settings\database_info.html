{% extends "base.html" %}

{% block title %}معلومات قاعدة البيانات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-database me-2"></i>
                        معلومات قاعدة البيانات
                    </h3>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="optimizeDatabase()">
                            <i class="fas fa-tools"></i> تحسين قاعدة البيانات
                        </button>
                        <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للإعدادات
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Database Overview -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-database fa-2x mb-2"></i>
                                    <h4>{{ db_info.size }}</h4>
                                    <p class="mb-0">حجم قاعدة البيانات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-table fa-2x mb-2"></i>
                                    <h4>{{ db_info.tables_count }}</h4>
                                    <p class="mb-0">عدد الجداول</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-list fa-2x mb-2"></i>
                                    <h4>{{ db_info.total_records }}</h4>
                                    <p class="mb-0">إجمالي السجلات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h4>{{ db_info.last_backup or 'لا يوجد' }}</h4>
                                    <p class="mb-0">آخر نسخة احتياطية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">معلومات عامة</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>نوع قاعدة البيانات:</strong></td>
                                            <td>{{ db_info.type }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الإصدار:</strong></td>
                                            <td>{{ db_info.version }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>مسار الملف:</strong></td>
                                            <td><small>{{ db_info.path }}</small></td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>{{ db_info.created_date }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>آخر تعديل:</strong></td>
                                            <td>{{ db_info.modified_date }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الترميز:</strong></td>
                                            <td>{{ db_info.encoding }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">إحصائيات الجداول</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>اسم الجدول</th>
                                                    <th>عدد السجلات</th>
                                                    <th>الحجم</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for table in db_info.tables %}
                                                <tr>
                                                    <td>{{ table.name }}</td>
                                                    <td>{{ table.records }}</td>
                                                    <td>{{ table.size }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">مقاييس الأداء</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6>متوسط وقت الاستعلام</h6>
                                                <h4 class="text-primary">{{ db_info.avg_query_time }}ms</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6>عدد الاستعلامات اليوم</h6>
                                                <h4 class="text-success">{{ db_info.queries_today }}</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6>حالة الفهارس</h6>
                                                <h4 class="text-info">{{ db_info.index_status }}</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6>مستوى التجزئة</h6>
                                                <h4 class="text-warning">{{ db_info.fragmentation }}%</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Maintenance -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">صيانة قاعدة البيانات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-primary" onclick="analyzeDatabase()">
                                            <i class="fas fa-search"></i> تحليل قاعدة البيانات
                                        </button>
                                        <button type="button" class="btn btn-outline-success" onclick="rebuildIndexes()">
                                            <i class="fas fa-hammer"></i> إعادة بناء الفهارس
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" onclick="vacuumDatabase()">
                                            <i class="fas fa-broom"></i> تنظيف قاعدة البيانات
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="checkIntegrity()">
                                            <i class="fas fa-shield-alt"></i> فحص سلامة البيانات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">عمليات خطيرة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>تحذير:</strong> هذه العمليات قد تؤثر على أداء النظام
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-warning" onclick="compactDatabase()">
                                            <i class="fas fa-compress"></i> ضغط قاعدة البيانات
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="resetDatabase()">
                                            <i class="fas fa-trash-restore"></i> إعادة تعيين قاعدة البيانات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">النشاط الأخير</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الوقت</th>
                                                    <th>العملية</th>
                                                    <th>الجدول</th>
                                                    <th>المستخدم</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for activity in db_info.recent_activity %}
                                                <tr>
                                                    <td>{{ activity.timestamp }}</td>
                                                    <td>{{ activity.operation }}</td>
                                                    <td>{{ activity.table }}</td>
                                                    <td>{{ activity.user }}</td>
                                                    <td>
                                                        {% if activity.status == 'success' %}
                                                        <span class="badge bg-success">نجح</span>
                                                        {% else %}
                                                        <span class="badge bg-danger">فشل</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">جاري التنفيذ...</h5>
            </div>
            <div class="modal-body">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
                <div id="progressText">بدء العملية...</div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    location.reload();
}

function showProgress(title, text) {
    document.querySelector('#progressModal .modal-title').textContent = title;
    document.getElementById('progressText').textContent = text;
    new bootstrap.Modal(document.getElementById('progressModal')).show();
}

function hideProgress() {
    bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
}

function updateProgress(percent, text) {
    const progressBar = document.querySelector('#progressModal .progress-bar');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = percent + '%';
    progressText.textContent = text;
}

function optimizeDatabase() {
    if (confirm('هل تريد تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.')) {
        showProgress('تحسين قاعدة البيانات', 'جاري تحسين قاعدة البيانات...');
        
        fetch('/settings/optimize-database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideProgress();
            if (data.success) {
                alert('تم تحسين قاعدة البيانات بنجاح');
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في تحسين قاعدة البيانات');
            }
        })
        .catch(error => {
            hideProgress();
            console.error('Error:', error);
            alert('حدث خطأ في تحسين قاعدة البيانات');
        });
    }
}

function analyzeDatabase() {
    showProgress('تحليل قاعدة البيانات', 'جاري تحليل قاعدة البيانات...');
    
    fetch('/settings/analyze-database', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideProgress();
        if (data.success) {
            alert('تم تحليل قاعدة البيانات بنجاح');
        } else {
            alert(data.message || 'حدث خطأ في تحليل قاعدة البيانات');
        }
    })
    .catch(error => {
        hideProgress();
        console.error('Error:', error);
        alert('حدث خطأ في تحليل قاعدة البيانات');
    });
}

function rebuildIndexes() {
    if (confirm('هل تريد إعادة بناء الفهارس؟ قد يستغرق هذا بعض الوقت.')) {
        showProgress('إعادة بناء الفهارس', 'جاري إعادة بناء الفهارس...');
        
        fetch('/settings/rebuild-indexes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideProgress();
            if (data.success) {
                alert('تم إعادة بناء الفهارس بنجاح');
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في إعادة بناء الفهارس');
            }
        })
        .catch(error => {
            hideProgress();
            console.error('Error:', error);
            alert('حدث خطأ في إعادة بناء الفهارس');
        });
    }
}

function vacuumDatabase() {
    if (confirm('هل تريد تنظيف قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.')) {
        showProgress('تنظيف قاعدة البيانات', 'جاري تنظيف قاعدة البيانات...');
        
        fetch('/settings/vacuum-database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideProgress();
            if (data.success) {
                alert('تم تنظيف قاعدة البيانات بنجاح');
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في تنظيف قاعدة البيانات');
            }
        })
        .catch(error => {
            hideProgress();
            console.error('Error:', error);
            alert('حدث خطأ في تنظيف قاعدة البيانات');
        });
    }
}

function checkIntegrity() {
    showProgress('فحص سلامة البيانات', 'جاري فحص سلامة البيانات...');
    
    fetch('/settings/check-integrity', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideProgress();
        if (data.success) {
            alert(`فحص سلامة البيانات مكتمل:\n${data.result}`);
        } else {
            alert(data.message || 'حدث خطأ في فحص سلامة البيانات');
        }
    })
    .catch(error => {
        hideProgress();
        console.error('Error:', error);
        alert('حدث خطأ في فحص سلامة البيانات');
    });
}

function compactDatabase() {
    if (confirm('هل تريد ضغط قاعدة البيانات؟ قد يستغرق هذا وقتاً طويلاً ويؤثر على الأداء.')) {
        showProgress('ضغط قاعدة البيانات', 'جاري ضغط قاعدة البيانات...');
        
        fetch('/settings/compact-database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideProgress();
            if (data.success) {
                alert('تم ضغط قاعدة البيانات بنجاح');
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ في ضغط قاعدة البيانات');
            }
        })
        .catch(error => {
            hideProgress();
            console.error('Error:', error);
            alert('حدث خطأ في ضغط قاعدة البيانات');
        });
    }
}

function resetDatabase() {
    if (confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
        if (confirm('هذا الإجراء لا يمكن التراجع عنه! هل أنت متأكد تماماً؟')) {
            showProgress('إعادة تعيين قاعدة البيانات', 'جاري إعادة تعيين قاعدة البيانات...');
            
            fetch('/settings/reset-database', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                hideProgress();
                if (data.success) {
                    alert('تم إعادة تعيين قاعدة البيانات بنجاح');
                    window.location.href = '/auth/login';
                } else {
                    alert(data.message || 'حدث خطأ في إعادة تعيين قاعدة البيانات');
                }
            })
            .catch(error => {
                hideProgress();
                console.error('Error:', error);
                alert('حدث خطأ في إعادة تعيين قاعدة البيانات');
            });
        }
    }
}
</script>
{% endblock %}
