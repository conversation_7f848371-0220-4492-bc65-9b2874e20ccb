#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الإصلاحات
Comprehensive Test for All Fixes
"""

import requests
import time

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class ComprehensiveTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_print_invoice(self, invoice_id=6):
        """اختبار طباعة الفاتورة"""
        print(f"\n🔄 اختبار طباعة الفاتورة رقم {invoice_id}...")
        try:
            response = self.session.get(f"{BASE_URL}/pos/print-invoice/{invoice_id}")
            
            if response.status_code == 200:
                if "طباعة فاتورة" in response.text:
                    print("✅ صفحة طباعة الفاتورة تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة طباعة الفاتورة لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في طباعة الفاتورة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في طباعة الفاتورة: {e}")
            return False
    
    def test_accounting_index(self):
        """اختبار صفحة المحاسبة الرئيسية"""
        print("\n🔄 اختبار صفحة المحاسبة الرئيسية...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/")
            
            if response.status_code == 200:
                if "لوحة المحاسبة" in response.text or "المحاسبة" in response.text:
                    print("✅ صفحة المحاسبة الرئيسية تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة المحاسبة لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة المحاسبة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة المحاسبة: {e}")
            return False
    
    def test_journal_entries(self):
        """اختبار صفحة القيود اليومية"""
        print("\n🔄 اختبار صفحة القيود اليومية...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/journal-entries")
            
            if response.status_code == 200:
                if "القيود اليومية" in response.text:
                    print("✅ صفحة القيود اليومية تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة القيود اليومية لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة القيود اليومية: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة القيود اليومية: {e}")
            return False
    
    def test_trial_balance(self):
        """اختبار صفحة ميزان المراجعة"""
        print("\n🔄 اختبار صفحة ميزان المراجعة...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/trial-balance")
            
            if response.status_code == 200:
                if "ميزان المراجعة" in response.text:
                    print("✅ صفحة ميزان المراجعة تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة ميزان المراجعة لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة ميزان المراجعة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة ميزان المراجعة: {e}")
            return False
    
    def test_export_invoice_pdf(self, invoice_id=6):
        """اختبار تصدير PDF للفاتورة"""
        print(f"\n🔄 اختبار تصدير PDF للفاتورة رقم {invoice_id}...")
        try:
            response = self.session.get(f"{BASE_URL}/pos/export-invoice-pdf/{invoice_id}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/pdf' in content_type or 'application/octet-stream' in content_type:
                    file_size = len(response.content)
                    if file_size > 1000:
                        print("✅ تصدير PDF للفاتورة يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم ملف PDF صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في تصدير PDF: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تصدير PDF: {e}")
            return False
    
    def test_general_ledger(self):
        """اختبار صفحة دفتر الأستاذ العام"""
        print("\n🔄 اختبار صفحة دفتر الأستاذ العام...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/general-ledger")
            
            if response.status_code == 200:
                if "دفتر الأستاذ" in response.text:
                    print("✅ صفحة دفتر الأستاذ العام تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة دفتر الأستاذ لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة دفتر الأستاذ: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة دفتر الأستاذ: {e}")
            return False
    
    def test_chart_of_accounts(self):
        """اختبار صفحة دليل الحسابات"""
        print("\n🔄 اختبار صفحة دليل الحسابات...")
        try:
            response = self.session.get(f"{BASE_URL}/accounting/chart-of-accounts")
            
            if response.status_code == 200:
                if "دليل الحسابات" in response.text:
                    print("✅ صفحة دليل الحسابات تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة دليل الحسابات لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في الوصول لصفحة دليل الحسابات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة دليل الحسابات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("📊 بدء الاختبار الشامل لجميع الإصلاحات")
        print("=" * 60)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("طباعة الفاتورة", self.test_print_invoice),
            ("صفحة المحاسبة الرئيسية", self.test_accounting_index),
            ("صفحة القيود اليومية", self.test_journal_entries),
            ("صفحة ميزان المراجعة", self.test_trial_balance),
            ("تصدير PDF للفاتورة", self.test_export_invoice_pdf),
            ("صفحة دفتر الأستاذ العام", self.test_general_ledger),
            ("صفحة دليل الحسابات", self.test_chart_of_accounts),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار الشامل:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع الإصلاحات تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة تحتاج إصلاح")
            return False

def main():
    """الدالة الرئيسية"""
    print("📊 اختبار شامل لجميع الإصلاحات - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = ComprehensiveTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
