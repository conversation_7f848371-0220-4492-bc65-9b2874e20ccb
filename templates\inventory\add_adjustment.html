{% extends "base.html" %}

{% block title %}تعديل مخزون{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-edit me-2"></i>تعديل مخزون</h2>
                <a href="{{ url_for('inventory.adjustments') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتعديلات
                </a>
            </div>
        </div>
    </div>

    <!-- Add Adjustment Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        إضافة تعديل مخزون
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="product_id" class="form-label">المنتج <span class="text-danger">*</span></label>
                                <select class="form-select" id="product_id" name="product_id" required onchange="updateProductInfo()">
                                    <option value="">اختر المنتج</option>
                                    {% for product in products %}
                                    <option value="{{ product.id }}" data-quantity="{{ product.quantity }}" data-unit="{{ product.unit }}">
                                        {{ product.name }} - {{ product.barcode }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="current_quantity" class="form-label">الكمية الحالية</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="current_quantity" readonly>
                                    <span class="input-group-text" id="current_unit">وحدة</span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_quantity" class="form-label">الكمية الجديدة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="new_quantity" name="new_quantity" 
                                           min="0" step="0.01" required onchange="calculateDifference()">
                                    <span class="input-group-text" id="new_unit">وحدة</span>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="difference" class="form-label">الفرق</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="difference" readonly>
                                    <span class="input-group-text" id="diff_unit">وحدة</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">سبب التعديل <span class="text-danger">*</span></label>
                            <select class="form-select" id="reason" name="reason" required>
                                <option value="">اختر السبب</option>
                                <option value="جرد">جرد</option>
                                <option value="تلف">تلف</option>
                                <option value="فقدان">فقدان</option>
                                <option value="خطأ في الإدخال">خطأ في الإدخال</option>
                                <option value="إرجاع من عميل">إرجاع من عميل</option>
                                <option value="إرجاع إلى مورد">إرجاع إلى مورد</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تنبيه:</strong> سيتم تسجيل هذا التعديل في سجل حركات المخزون ولا يمكن التراجع عنه.
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('inventory.adjustments') }}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>حفظ التعديل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateProductInfo() {
    const select = document.getElementById('product_id');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        const quantity = selectedOption.getAttribute('data-quantity');
        const unit = selectedOption.getAttribute('data-unit');
        
        document.getElementById('current_quantity').value = quantity;
        document.getElementById('current_unit').textContent = unit;
        document.getElementById('new_unit').textContent = unit;
        document.getElementById('diff_unit').textContent = unit;
        
        // Clear previous values
        document.getElementById('new_quantity').value = '';
        document.getElementById('difference').value = '';
    } else {
        document.getElementById('current_quantity').value = '';
        document.getElementById('current_unit').textContent = 'وحدة';
        document.getElementById('new_unit').textContent = 'وحدة';
        document.getElementById('diff_unit').textContent = 'وحدة';
        document.getElementById('new_quantity').value = '';
        document.getElementById('difference').value = '';
    }
}

function calculateDifference() {
    const currentQty = parseFloat(document.getElementById('current_quantity').value) || 0;
    const newQty = parseFloat(document.getElementById('new_quantity').value) || 0;
    const difference = newQty - currentQty;
    
    let diffText = '';
    if (difference > 0) {
        diffText = `+${difference.toFixed(2)}`;
        document.getElementById('difference').className = 'form-control text-success';
    } else if (difference < 0) {
        diffText = difference.toFixed(2);
        document.getElementById('difference').className = 'form-control text-danger';
    } else {
        diffText = '0.00';
        document.getElementById('difference').className = 'form-control';
    }
    
    document.getElementById('difference').value = diffText;
}
</script>
{% endblock %}
