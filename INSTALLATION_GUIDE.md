# 📥 دليل التثبيت الشامل - نظام إدارة قطع الغيار

## 🎯 دليل التثبيت خطوة بخطوة

هذا الدليل سيساعدك على تثبيت نظام إدارة قطع الغيار على أي جهاز كمبيوتر بسهولة.

---

## 📋 قبل البدء

### متطلبات النظام
- **نظام التشغيل:** Windows 10/11 (مُوصى به)
- **الذاكرة:** 4 GB RAM أو أكثر
- **التخزين:** 2 GB مساحة فارغة
- **الإنترنت:** مطلوب للتثبيت فقط
- **المتصفح:** Chrome, Firefox, Edge

### ما ستحتاجه
- ملفات النظام (مجلد AutoParts Manager V5.1)
- اتصال إنترنت مستقر
- صلاحيات المدير على الجهاز

---

## 🚀 الطريقة الأولى: التثبيت التلقائي (الأسهل)

### الخطوة 1: تحضير الملفات
1. **فك ضغط ملفات النظام** إلى مجلد على سطح المكتب
2. **افتح مجلد النظام** `AutoParts Manager V5.1`

### الخطوة 2: تشغيل برنامج التثبيت
1. **انقر نقراً مزدوجاً** على ملف `install.bat`
2. **اختر "نعم"** عند ظهور تحذير Windows
3. **انتظر** حتى يكتمل التثبيت (قد يستغرق 5-10 دقائق)

### الخطوة 3: تشغيل النظام
1. **انقر نقراً مزدوجاً** على ملف `start.bat`
2. **انتظر** حتى يظهر "النظام جاهز"
3. **افتح المتصفح** واذهب إلى: `http://127.0.0.1:8888`

### الخطوة 4: تسجيل الدخول
```
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
```

---

## 🛠️ الطريقة الثانية: التثبيت اليدوي

### الخطوة 1: تثبيت Python

1. **تحميل Python:**
   - اذهب إلى: https://www.python.org/downloads/
   - حمل أحدث إصدار Python 3.x

2. **تثبيت Python:**
   - شغل ملف التثبيت
   - ✅ **مهم:** تأكد من تحديد "Add Python to PATH"
   - اختر "Install Now"

3. **التحقق من التثبيت:**
   ```bash
   # افتح Command Prompt واكتب:
   python --version
   ```

### الخطوة 2: إعداد النظام

1. **افتح Command Prompt كمدير:**
   - اضغط `Win + R`
   - اكتب `cmd`
   - اضغط `Ctrl + Shift + Enter`

2. **انتقل لمجلد النظام:**
   ```bash
   cd "C:\path\to\AutoParts Manager V5.1"
   ```

3. **إنشاء البيئة الافتراضية:**
   ```bash
   python -m venv venv
   ```

4. **تفعيل البيئة الافتراضية:**
   ```bash
   venv\Scripts\activate
   ```

5. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

### الخطوة 3: إعداد قاعدة البيانات

```bash
python init_db.py
```

### الخطوة 4: تشغيل النظام

```bash
python app.py
```

---

## 🌐 الوصول للنظام

### من نفس الجهاز
```
http://127.0.0.1:8888
أو
http://localhost:8888
```

### من أجهزة أخرى في الشبكة
```
http://[IP-ADDRESS]:8888
مثال: http://*************:8888
```

**لمعرفة عنوان IP:**
```bash
# في Command Prompt:
ipconfig
# ابحث عن IPv4 Address
```

---

## 🔐 بيانات الدخول الافتراضية

### المدير (صلاحيات كاملة)
```
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
```

### المحاسب (المحاسبة والتقارير)
```
👤 اسم المستخدم: accountant
🔑 كلمة المرور: acc123
```

### الكاشير (نقطة البيع فقط)
```
👤 اسم المستخدم: cashier
🔑 كلمة المرور: cash123
```

> ⚠️ **مهم:** غيّر كلمات المرور بعد أول تسجيل دخول

---

## 🔧 حل المشاكل الشائعة

### مشكلة: "Python is not recognized"
**السبب:** Python غير مثبت أو غير مضاف للـ PATH

**الحل:**
1. أعد تثبيت Python
2. تأكد من تحديد "Add Python to PATH"
3. أعد تشغيل الكمبيوتر

### مشكلة: "pip is not recognized"
**الحل:**
```bash
python -m ensurepip --upgrade
python -m pip install --upgrade pip
```

### مشكلة: خطأ في تثبيت المتطلبات
**الحل:**
```bash
# ترقية pip أولاً
python -m pip install --upgrade pip

# تثبيت المتطلبات واحداً تلو الآخر
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install reportlab==4.0.4
pip install openpyxl==3.1.2
```

### مشكلة: "Port 8888 is already in use"
**الحل:**
1. أغلق أي برنامج يستخدم المنفذ 8888
2. أو غيّر المنفذ في ملف `app.py`:
   ```python
   app.run(host='0.0.0.0', port=8889, debug=False)
   ```

### مشكلة: لا يمكن الوصول للنظام
**الحل:**
1. تأكد من تشغيل النظام بنجاح
2. تحقق من إعدادات الجدار الناري
3. جرب عنواناً مختلفاً: `http://127.0.0.1:8888`

### مشكلة: خطأ في قاعدة البيانات
**الحل:**
```bash
# احذف قاعدة البيانات القديمة
del autoparts.db

# أعد إنشاء قاعدة البيانات
python init_db.py
```

---

## 🔄 تحديث النظام

### عند توفر إصدار جديد:

1. **احفظ نسخة احتياطية:**
   - الإعدادات > إدارة قاعدة البيانات > نسخة احتياطية

2. **استبدل ملفات النظام:**
   - احتفظ بملف `autoparts.db`
   - استبدل باقي الملفات

3. **شغل التحديث:**
   ```bash
   python init_db.py
   ```

---

## 📁 هيكل الملفات بعد التثبيت

```
AutoParts Manager V5.1/
├── install.bat          # برنامج التثبيت
├── start.bat           # تشغيل النظام
├── app.py              # الملف الرئيسي
├── init_db.py          # إعداد قاعدة البيانات
├── requirements.txt    # متطلبات Python
├── autoparts.db        # قاعدة البيانات
├── venv/              # البيئة الافتراضية
├── templates/         # قوالب HTML
├── static/           # الملفات الثابتة
├── routes/           # ملفات الطرق
├── exports/          # ملفات التصدير
├── backups/          # النسخ الاحتياطية
└── logs/             # ملفات السجلات
```

---

## 🎯 الخطوات التالية بعد التثبيت

### 1. الإعداد الأولي
- سجل دخول كمدير
- اذهب إلى الإعدادات
- حدث معلومات الشركة
- غيّر كلمة مرور المدير

### 2. إضافة البيانات الأساسية
- أضف فئات المنتجات
- أضف المنتجات الأولى
- أضف العملاء والموردين
- حدد كميات المخزون

### 3. إضافة المستخدمين
- أضف المحاسبين والكاشيرين
- حدد الصلاحيات المناسبة
- وزع بيانات الدخول

### 4. اختبار النظام
- جرب عملية بيع تجريبية
- اطبع فاتورة تجريبية
- راجع التقارير الأولية

---

## 📞 الحصول على المساعدة

### إذا واجهت مشاكل:
1. **راجع هذا الدليل** مرة أخرى
2. **تحقق من ملف السجلات** `logs/app.log`
3. **جرب إعادة التثبيت** من البداية
4. **اتصل بالدعم الفني** مع تفاصيل المشكلة

### معلومات مفيدة للدعم:
- نظام التشغيل وإصداره
- إصدار Python المثبت
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---

## ✅ قائمة التحقق النهائية

- [ ] تم تثبيت Python بنجاح
- [ ] تم إنشاء البيئة الافتراضية
- [ ] تم تثبيت جميع المتطلبات
- [ ] تم إنشاء قاعدة البيانات
- [ ] يعمل النظام على المنفذ 8888
- [ ] يمكن الوصول للنظام من المتصفح
- [ ] تم تسجيل الدخول بنجاح
- [ ] تم تغيير كلمة مرور المدير

---

**🎉 تهانينا! تم تثبيت النظام بنجاح**

*يمكنك الآن البدء في استخدام نظام إدارة قطع الغيار*
