# -*- coding: utf-8 -*-
"""
Setup script for AutoParts Manager
سكريبت إعداد نظام إدارة قطع الغيار
"""
import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب النظام Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        print("يرجى تحديث Python من: https://www.python.org/downloads/")
        return False
    
    print(f"✅ إصدار Python متوافق: {sys.version.split()[0]}")
    return True

def create_virtual_environment():
    """Create virtual environment"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ البيئة الافتراضية موجودة مسبقاً")
        return True
    
    print("🔧 جاري إنشاء البيئة الافتراضية...")
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ تم إنشاء البيئة الافتراضية بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في إنشاء البيئة الافتراضية")
        return False

def install_requirements():
    """Install required packages"""
    print("📦 جاري تثبيت المتطلبات...")
    
    # Determine pip path
    if os.name == 'nt':  # Windows
        pip_path = Path("venv/Scripts/pip.exe")
    else:  # Linux/Mac
        pip_path = Path("venv/bin/pip")
    
    if not pip_path.exists():
        print("❌ لم يتم العثور على pip في البيئة الافتراضية")
        return False
    
    try:
        # Upgrade pip first
        subprocess.run([str(pip_path), "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)
        
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def initialize_database():
    """Initialize database"""
    print("🗄️  جاري إعداد قاعدة البيانات...")
    
    db_path = Path("autoparts.db")
    if db_path.exists():
        print("✅ قاعدة البيانات موجودة مسبقاً")
        return True
    
    try:
        # Determine python path in virtual environment
        if os.name == 'nt':  # Windows
            python_path = Path("venv/Scripts/python.exe")
        else:  # Linux/Mac
            python_path = Path("venv/bin/python")
        
        # Initialize database by importing the app
        init_script = """
from app import create_app
app = create_app()
print("✅ تم إنشاء قاعدة البيانات بنجاح")
"""
        
        with open("init_db.py", "w", encoding="utf-8") as f:
            f.write(init_script)
        
        subprocess.run([str(python_path), "init_db.py"], check=True)
        
        # Clean up
        os.remove("init_db.py")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إعداد قاعدة البيانات: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 جاري إنشاء المجلدات المطلوبة...")
    
    directories = [
        "static/uploads",
        "static/exports", 
        "static/reports",
        "static/barcodes",
        "backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ تم إنشاء جميع المجلدات")

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if os.name != 'nt':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "AutoParts Manager.lnk")
        target = os.path.join(os.getcwd(), "تشغيل_النظام.bat")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
    except ImportError:
        print("⚠️  لم يتم إنشاء اختصار سطح المكتب (winshell غير مثبت)")
    except Exception as e:
        print(f"⚠️  لم يتم إنشاء اختصار سطح المكتب: {e}")

def main():
    """Main setup function"""
    print("=" * 60)
    print("🚗 AutoParts Manager V5.1 - Setup")
    print("   إعداد نظام إدارة قطع الغيار")
    print("=" * 60)
    print()
    
    # Check Python version
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    print()
    
    # Create virtual environment
    if not create_virtual_environment():
        input("اضغط Enter للخروج...")
        return
    
    print()
    
    # Install requirements
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    print()
    
    # Create directories
    create_directories()
    
    print()
    
    # Initialize database
    if not initialize_database():
        input("اضغط Enter للخروج...")
        return
    
    print()
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    print()
    print("🎉 تم إعداد النظام بنجاح!")
    print()
    print("📋 الخطوات التالية:")
    print("1. انقر نقراً مزدوجاً على 'تشغيل_النظام.bat' لتشغيل النظام")
    print("2. أو شغل الأمر: python run.py")
    print("3. افتح المتصفح على: http://localhost:5000")
    print()
    print("👤 بيانات الدخول الافتراضية:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   الكاشير: cashier / cash123")
    print()
    print("📖 لمزيد من المعلومات، راجع ملف README.md")
    print("=" * 60)
    
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
