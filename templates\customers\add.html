{% extends "base.html" %}

{% block title %}إضافة عميل جديد - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-user-plus me-2"></i>
        إضافة عميل جديد
    </h1>
    <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    بيانات العميل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Customer Type -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع العميل <span class="text-danger">*</span></label>
                            <select class="form-select" name="customer_type" required onchange="toggleCompanyFields()">
                                <option value="">اختر نوع العميل</option>
                                <option value="individual">فردي</option>
                                <option value="company">شركة</option>
                            </select>
                        </div>

                        <!-- Customer Name -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم العميل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" required 
                                   placeholder="أدخل اسم العميل">
                        </div>

                        <!-- Company Name (for companies only) -->
                        <div class="col-md-6 mb-3" id="companyNameField" style="display: none;">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" name="company_name" 
                                   placeholder="أدخل اسم الشركة">
                        </div>

                        <!-- Tax Number (for companies only) -->
                        <div class="col-md-6 mb-3" id="taxNumberField" style="display: none;">
                            <label class="form-label">الرقم الضريبي</label>
                            <input type="text" class="form-control" name="tax_number" 
                                   placeholder="أدخل الرقم الضريبي">
                        </div>

                        <!-- Phone -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone" 
                                   placeholder="05xxxxxxxx">
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" 
                                   placeholder="<EMAIL>">
                        </div>

                        <!-- Address -->
                        <div class="col-12 mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="3" 
                                      placeholder="أدخل العنوان الكامل"></textarea>
                        </div>

                        <!-- Initial Balance -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الرصيد الابتدائي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="initial_balance" 
                                       step="0.01" value="0" placeholder="0.00">
                                <span class="input-group-text">ريال</span>
                            </div>
                            <small class="form-text text-muted">
                                الرصيد الموجب يعني أن العميل له رصيد، والرصيد السالب يعني أن عليه دين
                            </small>
                        </div>

                        <!-- Credit Limit -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حد الائتمان</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="credit_limit" 
                                       step="0.01" value="0" placeholder="0.00">
                                <span class="input-group-text">ريال</span>
                            </div>
                            <small class="form-text text-muted">
                                الحد الأقصى للدين المسموح به (0 = بدون حد)
                            </small>
                        </div>

                        <!-- Notes -->
                        <div class="col-12 mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="أي ملاحظات إضافية عن العميل"></textarea>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('customers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة
                </h5>
            </div>
            <div class="card-body">
                <h6>نصائح لإضافة العملاء:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من صحة رقم الهاتف
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أدخل البريد الإلكتروني للتواصل
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد نوع العميل بدقة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اضبط حد الائتمان حسب الحاجة
                    </li>
                </ul>

                <hr>

                <h6>أنواع العملاء:</h6>
                <div class="mb-2">
                    <span class="badge bg-info me-2">فردي</span>
                    للأشخاص العاديين
                </div>
                <div class="mb-2">
                    <span class="badge bg-success me-2">شركة</span>
                    للشركات والمؤسسات
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('customers.index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع العملاء
                    </a>
                    <a href="{{ url_for('customers.import_excel') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-file-excel me-2"></i>
                        استيراد من Excel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleCompanyFields() {
    const customerType = document.querySelector('select[name="customer_type"]').value;
    const companyNameField = document.getElementById('companyNameField');
    const taxNumberField = document.getElementById('taxNumberField');
    
    if (customerType === 'company') {
        companyNameField.style.display = 'block';
        taxNumberField.style.display = 'block';
        document.querySelector('input[name="company_name"]').required = true;
    } else {
        companyNameField.style.display = 'none';
        taxNumberField.style.display = 'none';
        document.querySelector('input[name="company_name"]').required = false;
        document.querySelector('input[name="company_name"]').value = '';
        document.querySelector('input[name="tax_number"]').value = '';
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const customerType = document.querySelector('select[name="customer_type"]').value;
        const name = document.querySelector('input[name="name"]').value.trim();
        
        if (!customerType) {
            e.preventDefault();
            alert('يرجى اختيار نوع العميل');
            return;
        }
        
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم العميل');
            return;
        }
        
        // Validate phone number format (Saudi format)
        const phone = document.querySelector('input[name="phone"]').value.trim();
        if (phone && !phone.match(/^(05|5)[0-9]{8}$/)) {
            if (!confirm('تنسيق رقم الهاتف غير صحيح. هل تريد المتابعة؟')) {
                e.preventDefault();
                return;
            }
        }
        
        // Validate email format
        const email = document.querySelector('input[name="email"]').value.trim();
        if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
            if (!confirm('تنسيق البريد الإلكتروني غير صحيح. هل تريد المتابعة؟')) {
                e.preventDefault();
                return;
            }
        }
    });
});
</script>
{% endblock %}
