{% extends "base.html" %}

{% block title %}تقرير المخزون - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-warehouse me-2"></i>
        تقرير المخزون
    </h1>
    <div>
        <a href="{{ url_for('reports.inventory_report', category_id=category_id, low_stock_only=low_stock_only, export='excel') }}"
           class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>
            تصدير Excel
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            فلترة التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">التصنيف</label>
                <select class="form-select" name="category_id">
                    <option value="">جميع التصنيفات</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}"
                            {% if category.id == category_id %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">نوع التقرير</label>
                <select class="form-select" name="low_stock_only">
                    <option value="">جميع المنتجات</option>
                    <option value="true" {% if low_stock_only %}selected{% endif %}>
                        المخزون المنخفض فقط
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        تطبيق
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('reports.inventory_report') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_products }}</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_value|currency }}</h4>
                        <p class="mb-0">قيمة المخزون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ low_stock_count }}</h4>
                        <p class="mb-0">مخزون منخفض</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ products|selectattr('quantity', 'equalto', 0)|list|length }}</h4>
                        <p class="mb-0">نفد المخزون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تفاصيل المخزون</h5>
    </div>
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>التصنيف</th>
                        <th>الباركود</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>سعر التكلفة</th>
                        <th>سعر البيع</th>
                        <th>قيمة المخزون</th>
                        <th>الحالة</th>
                        <th>الموقع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr {% if product.quantity == 0 %}class="table-danger"
                        {% elif product.is_low_stock %}class="table-warning"{% endif %}>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                            <br><small class="text-muted">{{ product.description[:50] }}...</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.category %}
                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td><code>{{ product.barcode }}</code></td>
                        <td>
                            <span class="badge {% if product.quantity == 0 %}bg-danger{% elif product.is_low_stock %}bg-warning{% else %}bg-success{% endif %}">
                                {{ product.quantity }} {{ product.unit }}
                            </span>
                        </td>
                        <td>{{ product.min_quantity }} {{ product.unit }}</td>
                        <td>{{ product.cost_price|currency }}</td>
                        <td>{{ product.selling_price|currency }}</td>
                        <td>
                            <strong>{{ (product.quantity * product.cost_price)|currency }}</strong>
                        </td>
                        <td>
                            {% if product.quantity == 0 %}
                                <span class="badge bg-danger">نفد المخزون</span>
                            {% elif product.is_low_stock %}
                                <span class="badge bg-warning">مخزون منخفض</span>
                            {% else %}
                                <span class="badge bg-success">متوفر</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.location %}
                                {{ product.location }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="7">الإجمالي</th>
                        <th>{{ total_value|currency }}</th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">جرب تغيير الفلاتر أو إضافة منتجات جديدة</p>
            <a href="{{ url_for('products.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Charts Section -->
{% if products %}
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">توزيع المخزون حسب التصنيف</h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">حالة المخزون</h5>
            </div>
            <div class="card-body">
                <canvas id="stockStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Top Products by Value -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">أعلى المنتجات قيمة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>سعر التكلفة</th>
                                <th>قيمة المخزون</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in (products|sort(attribute='quantity', reverse=true))[:10] %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.quantity }} {{ product.unit }}</td>
                                <td>{{ product.cost_price|currency }}</td>
                                <td><strong>{{ (product.quantity * product.cost_price)|currency }}</strong></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if products %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Prepare data for charts
const products = {{ products_data|tojson }};

// Category distribution
const categoryData = {};
products.forEach(product => {
    const category = product.category;
    if (!categoryData[category]) {
        categoryData[category] = 0;
    }
    categoryData[category] += product.total_value;
});

const categoryLabels = Object.keys(categoryData);
const categoryValues = Object.values(categoryData);

const categoryCtx = document.getElementById('categoryChart').getContext('2d');
new Chart(categoryCtx, {
    type: 'pie',
    data: {
        labels: categoryLabels,
        datasets: [{
            data: categoryValues,
            backgroundColor: [
                '#667eea', '#764ba2', '#28a745', '#ffc107',
                '#dc3545', '#17a2b8', '#6c757d', '#e83e8c'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Stock status
const stockStatus = {
    'متوفر': 0,
    'مخزون منخفض': 0,
    'نفد المخزون': 0
};

products.forEach(product => {
    if (product.quantity === 0) {
        stockStatus['نفد المخزون']++;
    } else if (product.quantity <= product.min_quantity) {
        stockStatus['مخزون منخفض']++;
    } else {
        stockStatus['متوفر']++;
    }
});

const statusCtx = document.getElementById('stockStatusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: Object.keys(stockStatus),
        datasets: [{
            data: Object.values(stockStatus),
            backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
