<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة قطع الغيار{% endblock %}</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .stats-card .stats-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .low-stock {
            background-color: #fff3cd;
            border-right: 4px solid #ffc107;
        }

        .out-of-stock {
            background-color: #f8d7da;
            border-right: 4px solid #dc3545;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }

            .sidebar.show {
                right: 0;
            }

            .main-content {
                margin-right: 0;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>

            <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-car"></i> نظام إدارة قطع الغيار
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> {{ get_current_user().full_name }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ url_for('main.profile') }}">
                            <i class="fas fa-user-edit"></i> الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-lg-2 d-lg-block sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>

                        {% if has_permission('all') or has_permission('pos') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('pos.index') }}">
                                <i class="fas fa-cash-register"></i> نقطة البيع
                            </a>
                        </li>
                        {% endif %}

                        {% if has_permission('all') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('products.index') }}">
                                <i class="fas fa-boxes"></i> المنتجات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('customers.index') }}">
                                <i class="fas fa-users"></i> العملاء
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('suppliers.index') }}">
                                <i class="fas fa-truck"></i> الموردين
                            </a>
                        </li>
                        {% endif %}

                        {% if has_permission('all') or has_permission('inventory') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('inventory.index') }}">
                                <i class="fas fa-warehouse"></i> إدارة المخزن
                            </a>
                        </li>
                        {% endif %}

                        {% if has_permission('all') or has_permission('accounting') %}
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#accountingMenu">
                                <i class="fas fa-calculator"></i> المحاسبة
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="accountingMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('accounting.index') }}">
                                            <i class="fas fa-chart-line"></i> نظرة عامة
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('accounting.chart_of_accounts') }}">
                                            <i class="fas fa-list"></i> دليل الحسابات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('accounting.journal_entries') }}">
                                            <i class="fas fa-book"></i> القيود اليومية
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('accounting.general_ledger') }}">
                                            <i class="fas fa-file-alt"></i> دفتر الأستاذ
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('accounting.trial_balance') }}">
                                            <i class="fas fa-balance-scale"></i> ميزان المراجعة
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        {% endif %}

                        {% if has_permission('all') or has_permission('reports') %}
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#reportsMenu">
                                <i class="fas fa-chart-bar"></i> التقارير
                                <i class="fas fa-chevron-down float-end"></i>
                            </a>
                            <div class="collapse" id="reportsMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('reports.sales_report') }}">
                                            <i class="fas fa-shopping-cart"></i> تقرير المبيعات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('reports.inventory_report') }}">
                                            <i class="fas fa-warehouse"></i> تقرير المخزون
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('reports.profit_loss') }}">
                                            <i class="fas fa-chart-pie"></i> قائمة الدخل
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('reports.cash_flow') }}">
                                            <i class="fas fa-money-bill-wave"></i> التدفق النقدي
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        {% endif %}

                        {% if has_permission('all') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('settings.index') }}">
                                <i class="fas fa-cog"></i> الإعدادات
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-lg-10 ms-sm-auto main-content">
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
