{% extends "base.html" %}

{% block title %}إضافة حساب جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة حساب جديد
                    </h3>
                    <a href="{{ url_for('accounting.chart_of_accounts') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة لدليل الحسابات
                    </a>
                </div>

                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- Account Code -->
                            <div class="col-md-6 mb-3">
                                <label for="account_code" class="form-label">رقم الحساب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="account_code" name="account_code" 
                                       placeholder="مثال: 1001" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال رقم الحساب
                                </div>
                                <div class="form-text">رقم فريد للحساب (أرقام فقط)</div>
                            </div>

                            <!-- Account Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="مثال: النقدية في الصندوق" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم الحساب
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Account Type -->
                            <div class="col-md-6 mb-3">
                                <label for="account_type" class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <option value="assets">الأصول</option>
                                    <option value="liabilities">الخصوم</option>
                                    <option value="equity">حقوق الملكية</option>
                                    <option value="revenue">الإيرادات</option>
                                    <option value="expenses">المصروفات</option>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار نوع الحساب
                                </div>
                            </div>

                            <!-- Parent Account -->
                            <div class="col-md-6 mb-3">
                                <label for="parent_id" class="form-label">الحساب الرئيسي</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">لا يوجد (حساب رئيسي)</option>
                                    {% for account in accounts %}
                                    <option value="{{ account.id }}">{{ account.account_code }} - {{ account.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">اختياري - للحسابات الفرعية فقط</div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="وصف مختصر للحساب (اختياري)"></textarea>
                        </div>

                        <!-- Account Status -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    حساب نشط
                                </label>
                                <div class="form-text">الحسابات غير النشطة لن تظهر في القوائم</div>
                            </div>
                        </div>

                        <!-- Account Type Info -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> معلومات أنواع الحسابات:</h6>
                            <ul class="mb-0">
                                <li><strong>الأصول:</strong> النقدية، البنوك، المخزون، الأصول الثابتة</li>
                                <li><strong>الخصوم:</strong> الموردين، القروض، المستحقات</li>
                                <li><strong>حقوق الملكية:</strong> رأس المال، الأرباح المحتجزة</li>
                                <li><strong>الإيرادات:</strong> المبيعات، الإيرادات الأخرى</li>
                                <li><strong>المصروفات:</strong> تكلفة البضاعة، المصروفات التشغيلية</li>
                            </ul>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('accounting.chart_of_accounts') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الحساب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Account code validation (numbers only)
document.getElementById('account_code').addEventListener('input', function(e) {
    this.value = this.value.replace(/[^0-9]/g, '');
});

// Account type change handler
document.getElementById('account_type').addEventListener('change', function() {
    const accountType = this.value;
    const parentSelect = document.getElementById('parent_id');
    
    // Filter parent accounts based on selected type
    Array.from(parentSelect.options).forEach(option => {
        if (option.value === '') return; // Keep empty option
        
        const optionText = option.text;
        const isVisible = accountType === '' || optionText.includes(getAccountTypeArabic(accountType));
        option.style.display = isVisible ? 'block' : 'none';
    });
});

function getAccountTypeArabic(type) {
    const types = {
        'assets': 'أصول',
        'liabilities': 'خصوم',
        'equity': 'حقوق',
        'revenue': 'إيرادات',
        'expenses': 'مصروفات'
    };
    return types[type] || '';
}
</script>
{% endblock %}
