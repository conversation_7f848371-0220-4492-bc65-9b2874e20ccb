{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-cog me-2"></i>
        إعدادات النظام
    </h1>
</div>

<div class="row">
    <!-- System Settings -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-building fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">إعدادات الشركة</h5>
                <p class="card-text">إدارة بيانات الشركة والمعلومات الأساسية</p>
                <a href="{{ url_for('settings.system_settings') }}" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>
                    تعديل الإعدادات
                </a>
            </div>
        </div>
    </div>
    
    <!-- User Management -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-success"></i>
                </div>
                <h5 class="card-title">إدارة المستخدمين</h5>
                <p class="card-text">إضافة وتعديل المستخدمين والصلاحيات</p>
                <a href="{{ url_for('settings.users') }}" class="btn btn-success">
                    <i class="fas fa-users me-1"></i>
                    إدارة المستخدمين
                </a>
            </div>
        </div>
    </div>
    
    <!-- Backup & Restore -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-database fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">النسخ الاحتياطي</h5>
                <p class="card-text">إنشاء واستعادة النسخ الاحتياطية</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('settings.backup') }}" class="btn btn-warning">
                        <i class="fas fa-download me-1"></i>
                        إنشاء نسخة احتياطية
                    </a>
                    <a href="{{ url_for('settings.restore') }}" class="btn btn-outline-warning">
                        <i class="fas fa-upload me-1"></i>
                        استعادة نسخة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Database Info -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-info-circle fa-3x text-info"></i>
                </div>
                <h5 class="card-title">معلومات قاعدة البيانات</h5>
                <p class="card-text">إحصائيات وتفاصيل قاعدة البيانات</p>
                <a href="{{ url_for('settings.database_info') }}" class="btn btn-info">
                    <i class="fas fa-chart-bar me-1"></i>
                    عرض الإحصائيات
                </a>
            </div>
        </div>
    </div>
    
    <!-- Audit Log -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-history fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">سجل العمليات</h5>
                <p class="card-text">مراجعة سجل جميع العمليات والتغييرات</p>
                <a href="{{ url_for('settings.audit_log') }}" class="btn btn-secondary">
                    <i class="fas fa-list me-1"></i>
                    عرض السجل
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-server fa-3x text-dark"></i>
                </div>
                <h5 class="card-title">معلومات النظام</h5>
                <p class="card-text">تفاصيل النظام والإصدار</p>
                <button class="btn btn-dark" onclick="showSystemInfo()">
                    <i class="fas fa-info me-1"></i>
                    عرض المعلومات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ stats.total_products or 0 }}</h4>
                            <small class="text-muted">المنتجات</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-success mb-1">{{ stats.total_customers or 0 }}</h4>
                            <small class="text-muted">العملاء</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-warning mb-1">{{ stats.total_suppliers or 0 }}</h4>
                            <small class="text-muted">الموردين</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-info mb-1">{{ stats.today_sales or 0 }}</h4>
                            <small class="text-muted">مبيعات اليوم</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-secondary mb-1">{{ stats.monthly_sales or 0 }}</h4>
                            <small class="text-muted">مبيعات الشهر</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-danger mb-1">{{ stats.low_stock_count or 0 }}</h4>
                        <small class="text-muted">مخزون منخفض</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Info Modal -->
<div class="modal fade" id="systemInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-server me-2"></i>
                    معلومات النظام
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات التطبيق</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>اسم النظام:</strong></td>
                                <td>AutoParts Manager</td>
                            </tr>
                            <tr>
                                <td><strong>الإصدار:</strong></td>
                                <td>5.1</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإصدار:</strong></td>
                                <td>2024</td>
                            </tr>
                            <tr>
                                <td><strong>المطور:</strong></td>
                                <td>AutoParts Team</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">معلومات تقنية</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Framework:</strong></td>
                                <td>Flask (Python)</td>
                            </tr>
                            <tr>
                                <td><strong>قاعدة البيانات:</strong></td>
                                <td>SQLite</td>
                            </tr>
                            <tr>
                                <td><strong>واجهة المستخدم:</strong></td>
                                <td>Bootstrap 5 RTL</td>
                            </tr>
                            <tr>
                                <td><strong>الترخيص:</strong></td>
                                <td>Commercial</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6 class="text-info">المميزات</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>نظام نقطة البيع</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة المخزون</li>
                                <li><i class="fas fa-check text-success me-2"></i>النظام المحاسبي</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة العملاء والموردين</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>التقارير المتقدمة</li>
                                <li><i class="fas fa-check text-success me-2"></i>النسخ الاحتياطي</li>
                                <li><i class="fas fa-check text-success me-2"></i>دعم اللغة العربية</li>
                                <li><i class="fas fa-check text-success me-2"></i>يعمل بدون إنترنت</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showSystemInfo() {
    const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
    modal.show();
}

// Load stats if not provided
document.addEventListener('DOMContentLoaded', function() {
    // You can add AJAX calls here to load real-time stats
});
</script>
{% endblock %}
