#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحميل قوالب Excel للعملاء والموردين
Test Download Templates for Customers and Suppliers
"""

import os
import sys
import requests
import time
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class TemplateDownloadTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_customers_template_download(self):
        """اختبار تحميل قالب العملاء"""
        print("\n🔄 اختبار تحميل قالب العملاء...")
        try:
            response = self.session.get(f"{BASE_URL}/customers/download-template")
            
            # التحقق من نوع المحتوى
            content_type = response.headers.get('content-type', '')
            
            if response.status_code == 200:
                if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type or \
                   'application/vnd.ms-excel' in content_type:
                    print("✅ تم تحميل قالب العملاء بنجاح (ملف Excel)")
                    
                    # حفظ الملف للتحقق
                    with open('test_customers_template.xlsx', 'wb') as f:
                        f.write(response.content)
                    
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:  # ملف Excel يجب أن يكون أكبر من 1KB
                        print("✅ حجم الملف مناسب")
                        return True
                    else:
                        print("❌ حجم الملف صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ فشل في تحميل قالب العملاء: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحميل قالب العملاء: {e}")
            return False
    
    def test_suppliers_template_download(self):
        """اختبار تحميل قالب الموردين"""
        print("\n🔄 اختبار تحميل قالب الموردين...")
        try:
            response = self.session.get(f"{BASE_URL}/suppliers/download-template")
            
            # التحقق من نوع المحتوى
            content_type = response.headers.get('content-type', '')
            
            if response.status_code == 200:
                if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type or \
                   'application/vnd.ms-excel' in content_type:
                    print("✅ تم تحميل قالب الموردين بنجاح (ملف Excel)")
                    
                    # حفظ الملف للتحقق
                    with open('test_suppliers_template.xlsx', 'wb') as f:
                        f.write(response.content)
                    
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:  # ملف Excel يجب أن يكون أكبر من 1KB
                        print("✅ حجم الملف مناسب")
                        return True
                    else:
                        print("❌ حجم الملف صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ فشل في تحميل قالب الموردين: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحميل قالب الموردين: {e}")
            return False
    
    def test_products_template_download(self):
        """اختبار تحميل قالب المنتجات"""
        print("\n🔄 اختبار تحميل قالب المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/download-template")
            
            # التحقق من نوع المحتوى
            content_type = response.headers.get('content-type', '')
            
            if response.status_code == 200:
                if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type or \
                   'application/vnd.ms-excel' in content_type:
                    print("✅ تم تحميل قالب المنتجات بنجاح (ملف Excel)")
                    
                    # حفظ الملف للتحقق
                    with open('test_products_template.xlsx', 'wb') as f:
                        f.write(response.content)
                    
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:  # ملف Excel يجب أن يكون أكبر من 1KB
                        print("✅ حجم الملف مناسب")
                        return True
                    else:
                        print("❌ حجم الملف صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ فشل في تحميل قالب المنتجات: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحميل قالب المنتجات: {e}")
            return False
    
    def cleanup_test_files(self):
        """حذف ملفات الاختبار"""
        test_files = [
            'test_customers_template.xlsx',
            'test_suppliers_template.xlsx', 
            'test_products_template.xlsx'
        ]
        
        for file in test_files:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"🗑️ تم حذف ملف الاختبار: {file}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {file}: {e}")
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات تحميل القوالب"""
        print("📥 بدء اختبار تحميل قوالب Excel")
        print("=" * 50)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("قالب العملاء", self.test_customers_template_download),
            ("قالب الموردين", self.test_suppliers_template_download),
            ("قالب المنتجات", self.test_products_template_download),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار تحميل القوالب:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        # تنظيف ملفات الاختبار
        self.cleanup_test_files()
        
        if failed == 0:
            print("\n🎉 جميع قوالب التحميل تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️  يوجد {failed} مشكلة في تحميل القوالب")
            return False

def main():
    """الدالة الرئيسية"""
    print("📥 اختبار تحميل قوالب Excel - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = TemplateDownloadTester()
    success = tester.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
