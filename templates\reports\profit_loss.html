{% extends "base.html" %}

{% block title %}قائمة الدخل - التقارير المالية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-pie me-2"></i>
        قائمة الدخل (الأرباح والخسائر)
    </h1>
    <div>
        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للتقارير
        </a>
        <a href="{{ url_for('reports.profit_loss', export='excel') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>
            تصدير Excel
        </a>
        <a href="{{ url_for('reports.profit_loss', export='pdf') }}" class="btn btn-danger">
            <i class="fas fa-file-pdf me-2"></i>
            تصدير PDF
        </a>
    </div>
</div>

<!-- Date Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-calendar me-2"></i>
            فترة التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="from_date" 
                       value="{{ request.args.get('from_date', '') }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="to_date" 
                       value="{{ request.args.get('to_date', '') }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calculator me-1"></i>
                        إعداد التقرير
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Profit & Loss Statement -->
<div class="card">
    <div class="card-header">
        <div class="text-center">
            <h4 class="mb-1">قائمة الدخل</h4>
            {% if from_date and to_date %}
                <p class="text-muted mb-0">للفترة من {{ from_date }} إلى {{ to_date }}</p>
            {% else %}
                <p class="text-muted mb-0">للفترة الحالية</p>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-borderless">
                <tbody>
                    <!-- Revenue Section -->
                    <tr class="table-primary">
                        <td colspan="2"><h5 class="mb-0">الإيرادات</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">إيرادات المبيعات</td>
                        <td class="text-end">{{ sales_revenue|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">خصومات المبيعات</td>
                        <td class="text-end text-danger">({{ sales_discounts|currency }})</td>
                    </tr>
                    <tr>
                        <td class="ps-4">مرتجعات المبيعات</td>
                        <td class="text-end text-danger">({{ sales_returns|currency }})</td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>صافي المبيعات</strong></td>
                        <td class="text-end"><strong>{{ net_sales|currency }}</strong></td>
                    </tr>
                    
                    <!-- Cost of Goods Sold -->
                    <tr class="table-warning">
                        <td colspan="2"><h5 class="mb-0 mt-3">تكلفة البضاعة المباعة</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">مخزون أول المدة</td>
                        <td class="text-end">{{ opening_inventory|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">المشتريات</td>
                        <td class="text-end">{{ purchases|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">مرتجعات المشتريات</td>
                        <td class="text-end text-success">({{ purchase_returns|currency }})</td>
                    </tr>
                    <tr>
                        <td class="ps-4">صافي المشتريات</td>
                        <td class="text-end">{{ net_purchases|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">البضاعة المتاحة للبيع</td>
                        <td class="text-end">{{ goods_available|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">مخزون آخر المدة</td>
                        <td class="text-end text-success">({{ closing_inventory|currency }})</td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>تكلفة البضاعة المباعة</strong></td>
                        <td class="text-end"><strong class="text-danger">{{ cost_of_goods_sold|currency }}</strong></td>
                    </tr>
                    
                    <!-- Gross Profit -->
                    <tr class="table-success">
                        <td><h5 class="mb-0 mt-3">إجمالي الربح</h5></td>
                        <td class="text-end"><h5 class="mb-0 mt-3 {% if gross_profit >= 0 %}text-success{% else %}text-danger{% endif %}">{{ gross_profit|currency }}</h5></td>
                    </tr>
                    
                    <!-- Operating Expenses -->
                    <tr class="table-danger">
                        <td colspan="2"><h5 class="mb-0 mt-3">المصروفات التشغيلية</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">مصروفات البيع والتسويق</td>
                        <td class="text-end">{{ selling_expenses|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">المصروفات الإدارية</td>
                        <td class="text-end">{{ admin_expenses|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">مصروفات عمومية</td>
                        <td class="text-end">{{ general_expenses|currency }}</td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>إجمالي المصروفات التشغيلية</strong></td>
                        <td class="text-end"><strong class="text-danger">{{ total_operating_expenses|currency }}</strong></td>
                    </tr>
                    
                    <!-- Operating Income -->
                    <tr class="table-info">
                        <td><h5 class="mb-0 mt-3">الدخل التشغيلي</h5></td>
                        <td class="text-end"><h5 class="mb-0 mt-3 {% if operating_income >= 0 %}text-success{% else %}text-danger{% endif %}">{{ operating_income|currency }}</h5></td>
                    </tr>
                    
                    <!-- Other Income/Expenses -->
                    <tr class="table-secondary">
                        <td colspan="2"><h5 class="mb-0 mt-3">الإيرادات والمصروفات الأخرى</h5></td>
                    </tr>
                    <tr>
                        <td class="ps-4">إيرادات أخرى</td>
                        <td class="text-end text-success">{{ other_income|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">مصروفات أخرى</td>
                        <td class="text-end text-danger">{{ other_expenses|currency }}</td>
                    </tr>
                    <tr>
                        <td class="ps-4">مصروفات فوائد</td>
                        <td class="text-end text-danger">{{ interest_expenses|currency }}</td>
                    </tr>
                    <tr class="border-bottom">
                        <td class="ps-4"><strong>صافي الإيرادات الأخرى</strong></td>
                        <td class="text-end"><strong>{{ net_other_income|currency }}</strong></td>
                    </tr>
                    
                    <!-- Net Income -->
                    <tr class="table-dark">
                        <td><h4 class="mb-0 mt-3 text-white">صافي الدخل</h4></td>
                        <td class="text-end">
                            <h4 class="mb-0 mt-3 {% if net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ net_income|currency }}
                            </h4>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Financial Ratios -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-percentage me-2"></i>
                    النسب المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-primary">{{ gross_profit_margin|round(2) }}%</h4>
                            <p class="text-muted mb-0">هامش الربح الإجمالي</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-success">{{ net_profit_margin|round(2) }}%</h4>
                            <p class="text-muted mb-0">هامش الربح الصافي</p>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-info">{{ operating_margin|round(2) }}%</h4>
                            <p class="text-muted mb-0">هامش التشغيل</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-warning">{{ expense_ratio|round(2) }}%</h4>
                            <p class="text-muted mb-0">نسبة المصروفات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    توزيع الإيرادات والمصروفات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="profitChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Comparison -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    مقارنة شهرية للأرباح
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Profit Chart
const profitCtx = document.getElementById('profitChart').getContext('2d');
new Chart(profitCtx, {
    type: 'doughnut',
    data: {
        labels: ['الإيرادات', 'تكلفة البضاعة', 'المصروفات التشغيلية', 'صافي الربح'],
        datasets: [{
            data: [{{ net_sales }}, {{ cost_of_goods_sold }}, {{ total_operating_expenses }}, {{ net_income }}],
            backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#17a2b8'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [{
            label: 'صافي الربح',
            data: {{ monthly_profits|tojson }},
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
{% endblock %}
