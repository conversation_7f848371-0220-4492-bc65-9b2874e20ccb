{% extends "base.html" %}

{% block title %}الميزانية العمومية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-balance-scale me-2"></i>
                        الميزانية العمومية
                    </h3>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <a href="{{ url_for('reports.balance_sheet', as_of_date=as_of_date, export='pdf') }}" 
                           class="btn btn-outline-danger">
                            <i class="fas fa-file-pdf"></i> PDF
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Date Filter -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <form method="GET" class="d-flex">
                                <div class="input-group">
                                    <span class="input-group-text">كما في تاريخ</span>
                                    <input type="date" name="as_of_date" class="form-control" 
                                           value="{{ as_of_date }}" onchange="this.form.submit()">
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Balance Sheet Content -->
                    <div class="row">
                        <!-- Assets Column -->
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">الأصول</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Current Assets -->
                                    <h6 class="text-primary">الأصول المتداولة</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            {% for item in asset_details %}
                                            <tr>
                                                <td>{{ item.account.name }}</td>
                                                <td class="text-end">{{ "{:,.2f}".format(item.balance) }}</td>
                                            </tr>
                                            {% endfor %}
                                            <tr class="table-info">
                                                <td><strong>إجمالي الأصول المتداولة</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(current_assets) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>

                                    <!-- Fixed Assets -->
                                    <h6 class="text-primary mt-3">الأصول الثابتة</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr>
                                                <td>الأصول الثابتة</td>
                                                <td class="text-end">{{ "{:,.2f}".format(fixed_assets) }}</td>
                                            </tr>
                                            <tr class="table-info">
                                                <td><strong>إجمالي الأصول الثابتة</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(fixed_assets) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>

                                    <!-- Total Assets -->
                                    <div class="table-responsive mt-3">
                                        <table class="table table-sm">
                                            <tr class="table-primary">
                                                <td><strong>إجمالي الأصول</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(total_assets) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Liabilities & Equity Column -->
                        <div class="col-md-6">
                            <!-- Liabilities -->
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">الخصوم</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Current Liabilities -->
                                    <h6 class="text-warning">الخصوم المتداولة</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            {% for item in liability_details %}
                                            <tr>
                                                <td>{{ item.account.name }}</td>
                                                <td class="text-end">{{ "{:,.2f}".format(item.balance) }}</td>
                                            </tr>
                                            {% endfor %}
                                            <tr class="table-warning">
                                                <td><strong>إجمالي الخصوم المتداولة</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(current_liabilities) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>

                                    <!-- Long-term Liabilities -->
                                    <h6 class="text-warning mt-3">الخصوم طويلة الأجل</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tr>
                                                <td>الخصوم طويلة الأجل</td>
                                                <td class="text-end">{{ "{:,.2f}".format(long_term_liabilities) }}</td>
                                            </tr>
                                            <tr class="table-warning">
                                                <td><strong>إجمالي الخصوم طويلة الأجل</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(long_term_liabilities) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>

                                    <!-- Total Liabilities -->
                                    <div class="table-responsive mt-3">
                                        <table class="table table-sm">
                                            <tr class="table-warning">
                                                <td><strong>إجمالي الخصوم</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(total_liabilities) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Equity -->
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">حقوق الملكية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            {% for item in equity_details %}
                                            <tr>
                                                <td>{{ item.account.name }}</td>
                                                <td class="text-end">{{ "{:,.2f}".format(item.balance) }}</td>
                                            </tr>
                                            {% endfor %}
                                            <tr>
                                                <td>الأرباح المحتجزة</td>
                                                <td class="text-end">{{ "{:,.2f}".format(retained_earnings) }}</td>
                                            </tr>
                                            <tr class="table-success">
                                                <td><strong>إجمالي حقوق الملكية</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(total_equity) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>

                                    <!-- Total Liabilities & Equity -->
                                    <div class="table-responsive mt-3">
                                        <table class="table table-sm">
                                            <tr class="table-primary">
                                                <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                                                <td class="text-end"><strong>{{ "{:,.2f}".format(total_liabilities_equity) }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Balance Check -->
                    {% if balance_difference != 0 %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        تحذير: الميزانية غير متوازنة. الفرق: {{ "{:,.2f}".format(balance_difference) }}
                    </div>
                    {% else %}
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle"></i>
                        الميزانية متوازنة بنجاح
                    </div>
                    {% endif %}

                    <!-- Financial Ratios -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>النسب المالية</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6>نسبة التداول</h6>
                                            <h4 class="text-primary">{{ "{:.2f}".format(current_ratio) }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6>نسبة الدين إلى الملكية</h6>
                                            <h4 class="text-warning">{{ "{:.2f}".format(debt_to_equity) }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6>نسبة الدين</h6>
                                            <h4 class="text-danger">{{ "{:.2%}".format(debt_ratio) }}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6>نسبة الملكية</h6>
                                            <h4 class="text-success">{{ "{:.2%}".format(equity_ratio) }}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
