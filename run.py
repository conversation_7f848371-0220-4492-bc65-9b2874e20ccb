# -*- coding: utf-8 -*-
"""
Quick run script for AutoParts Manager
سكريبت تشغيل سريع لنظام إدارة قطع الغيار
"""
import os
import sys
import webbrowser
import time
from threading import Timer

def open_browser():
    """Open browser after a delay"""
    time.sleep(2)  # Wait 2 seconds for server to start
    webbrowser.open('http://127.0.0.1:8888')

def main():
    print("=" * 60)
    print("🚗 AutoParts Manager V5.1")
    print("   نظام إدارة قطع الغيار المتكامل")
    print("=" * 60)
    print()

    # Check if required files exist
    required_files = ['app.py', 'models.py', 'requirements.txt']
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ الملفات التالية مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nتأكد من وجود جميع ملفات النظام")
        input("اضغط Enter للخروج...")
        return

    print("✅ جميع الملفات المطلوبة موجودة")
    print()

    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ يتطلب النظام Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        input("اضغط Enter للخروج...")
        return

    print(f"✅ إصدار Python: {sys.version.split()[0]}")
    print()

    # Check if virtual environment is activated
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ البيئة الافتراضية مفعلة")
    else:
        print("⚠️  البيئة الافتراضية غير مفعلة")
        print("يُنصح بتفعيل البيئة الافتراضية أولاً")

    print()

    # Install requirements if needed
    try:
        import flask
        print("✅ Flask مثبت")
    except ImportError:
        print("❌ Flask غير مثبت")
        print("جاري تثبيت المتطلبات...")
        os.system("pip install -r requirements.txt")

    print()
    print("🚀 جاري تشغيل النظام...")
    print()
    print("📋 بيانات الدخول الافتراضية:")
    print("   المدير: admin / admin123")
    print("   المحاسب: accountant / acc123")
    print("   الكاشير: cashier / cash123")
    print()
    print("🌐 سيتم فتح النظام في المتصفح على العنوان:")
    print("   http://localhost:5000")
    print()
    print("⏹️  لإيقاف النظام: اضغط Ctrl+C")
    print("=" * 60)
    print()

    # Open browser after delay
    Timer(3.0, open_browser).start()

    # Run the application
    try:
        from app import create_app
        app = create_app()
        app.run(debug=False, host='127.0.0.1', port=8888)
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
