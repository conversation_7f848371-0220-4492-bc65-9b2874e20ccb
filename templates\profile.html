{% extends "base.html" %}

{% block title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-user me-2"></i>الملف الشخصي</h2>
                <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="row mb-4">
        <div class="col-lg-8 col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>
                        معلومات المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <div class="user-avatar">
                                <i class="fas fa-user-circle fa-5x text-primary"></i>
                            </div>
                            <h5 class="mt-3">{{ user.full_name }}</h5>
                            <span class="badge bg-{% if user.role == 'manager' %}success{% elif user.role == 'accountant' %}info{% else %}warning{% endif %} fs-6">
                                {% if user.role == 'manager' %}
                                    <i class="fas fa-crown me-1"></i>مدير
                                {% elif user.role == 'accountant' %}
                                    <i class="fas fa-calculator me-1"></i>محاسب
                                {% elif user.role == 'cashier' %}
                                    <i class="fas fa-cash-register me-1"></i>كاشير
                                {% else %}
                                    {{ user.role }}
                                {% endif %}
                            </span>
                        </div>
                        
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-user me-2"></i>اسم المستخدم:</strong></td>
                                    <td>{{ user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-signature me-2"></i>الاسم الكامل:</strong></td>
                                    <td>{{ user.full_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-envelope me-2"></i>البريد الإلكتروني:</strong></td>
                                    <td>{{ user.email or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-phone me-2"></i>رقم الهاتف:</strong></td>
                                    <td>{{ user.phone or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-user-tag me-2"></i>الدور:</strong></td>
                                    <td>
                                        {% if user.role == 'manager' %}
                                            <span class="text-success">مدير النظام</span>
                                        {% elif user.role == 'accountant' %}
                                            <span class="text-info">محاسب</span>
                                        {% elif user.role == 'cashier' %}
                                            <span class="text-warning">كاشير</span>
                                        {% else %}
                                            {{ user.role }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-calendar-plus me-2"></i>تاريخ التسجيل:</strong></td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-clock me-2"></i>آخر تسجيل دخول:</strong></td>
                                    <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-toggle-on me-2"></i>حالة الحساب:</strong></td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4 col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a>
                        
                        {% if user.role == 'manager' %}
                        <a href="{{ url_for('settings.index') }}" class="btn btn-outline-success">
                            <i class="fas fa-cog me-2"></i>إعدادات النظام
                        </a>
                        
                        <a href="{{ url_for('settings.backup') }}" class="btn btn-outline-warning">
                            <i class="fas fa-download me-2"></i>نسخة احتياطية
                        </a>
                        {% endif %}
                        
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-info">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                        
                        <hr>
                        
                        <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Permissions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        صلاحيات المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if user.role == 'manager' %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>إدارة المنتجات</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>إدارة العملاء</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>إدارة الموردين</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>نقطة البيع</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>إدارة المخزون</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>النظام المحاسبي</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>التقارير</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>الإعدادات</span>
                            </div>
                        </div>
                        
                        {% elif user.role == 'accountant' %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>عرض المنتجات</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>إدارة العملاء</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>إدارة الموردين</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>النظام المحاسبي</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>التقارير</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                <span>إدارة المنتجات</span>
                            </div>
                        </div>
                        
                        {% elif user.role == 'cashier' %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>نقطة البيع</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>عرض المنتجات</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>عرض العملاء</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                <span>إدارة المنتجات</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                <span>النظام المحاسبي</span>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="permission-item">
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                <span>الإعدادات</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        ملخص النشاط
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="activity-stat">
                                <i class="fas fa-sign-in-alt fa-2x text-primary mb-2"></i>
                                <h6>مرات تسجيل الدخول</h6>
                                <h4 class="text-primary">{{ user.login_count or 0 }}</h4>
                            </div>
                        </div>
                        
                        {% if user.role in ['manager', 'cashier'] %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="activity-stat">
                                <i class="fas fa-receipt fa-2x text-success mb-2"></i>
                                <h6>الفواتير المنشأة</h6>
                                <h4 class="text-success">{{ user.sales_count or 0 }}</h4>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="activity-stat">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h6>ساعات العمل اليوم</h6>
                                <h4 class="text-info">8.5</h4>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="activity-stat">
                                <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                <h6>تقييم الأداء</h6>
                                <h4 class="text-warning">ممتاز</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    margin-bottom: 1rem;
}

.permission-item {
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    margin-bottom: 0.5rem;
}

.activity-stat {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
}

.activity-stat i {
    display: block;
}
</style>
{% endblock %}
