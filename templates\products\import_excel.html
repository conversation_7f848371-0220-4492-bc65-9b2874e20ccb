{% extends "base.html" %}

{% block title %}استيراد المنتجات من Excel{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-file-import me-2"></i>استيراد المنتجات من Excel</h2>
                <a href="{{ url_for('products.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                </a>
            </div>
        </div>
    </div>

    <!-- Instructions Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تعليمات الاستيراد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <h6><i class="fas fa-download me-2 text-primary"></i>الخطوة الأولى: تحميل القالب</h6>
                            <p class="mb-3">قم بتحميل قالب Excel الجاهز وملء البيانات فيه</p>
                            <a href="{{ url_for('products.download_template') }}" class="btn btn-primary mb-3">
                                <i class="fas fa-download me-2"></i>تحميل قالب Excel
                            </a>
                        </div>
                        <div class="col-lg-6">
                            <h6><i class="fas fa-upload me-2 text-success"></i>الخطوة الثانية: رفع الملف</h6>
                            <p class="mb-3">بعد ملء البيانات، قم برفع الملف لاستيراد المنتجات</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-lg-6">
                            <h6><i class="fas fa-exclamation-triangle me-2 text-warning"></i>الحقول المطلوبة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>الاسم*</li>
                                <li><i class="fas fa-check text-success me-2"></i>الباركود*</li>
                                <li><i class="fas fa-check text-success me-2"></i>الوحدة*</li>
                                <li><i class="fas fa-check text-success me-2"></i>الكمية*</li>
                                <li><i class="fas fa-check text-success me-2"></i>سعر التكلفة*</li>
                                <li><i class="fas fa-check text-success me-2"></i>سعر البيع*</li>
                                <li><i class="fas fa-check text-success me-2"></i>الحد الأدنى*</li>
                            </ul>
                        </div>
                        <div class="col-lg-6">
                            <h6><i class="fas fa-info-circle me-2 text-info"></i>الحقول الاختيارية:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-minus text-muted me-2"></i>الفئة</li>
                                <li><i class="fas fa-minus text-muted me-2"></i>الوصف</li>
                                <li><i class="fas fa-minus text-muted me-2"></i>الموقع</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="row mb-4">
        <div class="col-lg-8 col-md-12 mx-auto">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        رفع ملف Excel
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="importForm">
                        <div class="mb-4">
                            <label for="file" class="form-label">
                                <i class="fas fa-file-excel me-2"></i>اختر ملف Excel
                            </label>
                            <input type="file" class="form-control" id="file" name="file" 
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                يجب أن يكون الملف من نوع Excel (.xlsx أو .xls)
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmImport" required>
                                <label class="form-check-label" for="confirmImport">
                                    أؤكد أن البيانات في الملف صحيحة وأريد استيرادها
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="fas fa-upload me-2"></i>رفع واستيراد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Important Notes -->
    <div class="row">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ملاحظات مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <h6><i class="fas fa-shield-alt me-2 text-success"></i>نصائح للنجاح:</h6>
                            <ul>
                                <li>استخدم القالب المتوفر فقط</li>
                                <li>تأكد من عدم وجود باركود مكرر</li>
                                <li>تحقق من صحة الأسعار والكميات</li>
                                <li>لا تترك الحقول المطلوبة فارغة</li>
                                <li>استخدم أرقام صحيحة للكميات والأسعار</li>
                            </ul>
                        </div>
                        <div class="col-lg-6">
                            <h6><i class="fas fa-times-circle me-2 text-danger"></i>تجنب هذه الأخطاء:</h6>
                            <ul>
                                <li>لا تغير أسماء الأعمدة في القالب</li>
                                <li>لا تضع أحرف خاصة في الباركود</li>
                                <li>لا تستخدم أرقام سالبة للكميات</li>
                                <li>لا تترك خلايا فارغة في الأعمدة المطلوبة</li>
                                <li>لا تضع نصوص في خانات الأرقام</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" role="alert">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>نصيحة:</strong> ابدأ بملف صغير يحتوي على عدد قليل من المنتجات للتأكد من صحة البيانات قبل استيراد ملف كبير.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري استيراد المنتجات...</h5>
                <p class="text-muted">يرجى الانتظار، لا تغلق هذه النافذة</p>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('importForm').addEventListener('submit', function(e) {
    // Show loading modal
    var loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    // Disable submit button
    document.getElementById('submitBtn').disabled = true;
});

function resetForm() {
    document.getElementById('importForm').reset();
    document.getElementById('submitBtn').disabled = false;
}

// File validation
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileSize = file.size / 1024 / 1024; // MB
        const maxSize = 10; // 10 MB
        
        if (fileSize > maxSize) {
            alert('حجم الملف كبير جداً. الحد الأقصى هو ' + maxSize + ' ميجابايت');
            e.target.value = '';
            return;
        }
        
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى اختيار ملف Excel (.xlsx أو .xls)');
            e.target.value = '';
            return;
        }
    }
});

// Drag and drop functionality
const fileInput = document.getElementById('file');
const dropZone = fileInput.closest('.card-body');

dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    dropZone.classList.add('border-primary');
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-primary');
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-primary');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
    }
});
</script>

<style>
.card-body.border-primary {
    border: 2px dashed #0d6efd !important;
    background-color: #f8f9ff;
}

.list-unstyled li {
    padding: 0.25rem 0;
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.alert-info {
    border-left: 4px solid #0dcaf0;
}
</style>
{% endblock %}
