@echo off
chcp 65001 >nul
title تثبيت مبسط - نظام إدارة قطع الغيار

echo.
echo ============================================================
echo 📥 تثبيت مبسط - نظام إدارة قطع الغيار
echo    AutoParts Manager V5.1
echo ============================================================
echo.
echo 🎯 هذا البرنامج سيقوم بتثبيت النظام بطريقة مبسطة
echo    مناسب للمستخدمين العاديين
echo.
pause

echo.
echo ============================================================
echo 🔍 فحص النظام...
echo ============================================================

REM Check if Python is installed
echo 🐍 فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo    https://www.python.org/downloads/
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    echo 🔄 بعد تثبيت Python، شغل هذا الملف مرة أخرى
    pause
    exit /b 1
)

python --version
echo ✅ Python مثبت بنجاح

REM Check pip
echo.
echo 📦 فحص pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  pip غير متوفر، جاري التثبيت...
    python -m ensurepip --upgrade
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت pip
        pause
        exit /b 1
    )
)

pip --version
echo ✅ pip متوفر

echo.
echo ============================================================
echo 📚 تثبيت المتطلبات الأساسية...
echo ============================================================

echo 🔄 ترقية pip...
python -m pip install --upgrade pip

echo.
echo 🔄 تثبيت المتطلبات الأساسية...
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5 Flask-Login==0.6.3

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات الأساسية
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات الأساسية

echo.
echo 🔄 تثبيت متطلبات التقارير...
pip install reportlab==4.0.4 openpyxl==3.1.2

echo.
echo 🔄 تثبيت متطلبات إضافية...
pip install Pillow==10.0.1 python-barcode==0.15.1 pandas==2.1.1

echo ✅ تم تثبيت جميع المتطلبات

echo.
echo ============================================================
echo 🗄️ إعداد قاعدة البيانات...
echo ============================================================

REM Check if database exists
if exist "autoparts.db" (
    echo ✅ قاعدة البيانات موجودة مسبقاً
) else (
    echo 🔨 إنشاء قاعدة البيانات...
    
    if exist "init_db.py" (
        python init_db.py
    ) else (
        python -c "from app import app, db; app.app_context().push(); db.create_all(); print('تم إنشاء قاعدة البيانات')"
    )
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
    
    echo ✅ تم إنشاء قاعدة البيانات بنجاح
)

echo.
echo ============================================================
echo 📁 إنشاء المجلدات...
echo ============================================================

echo 🔨 إنشاء المجلدات المطلوبة...

if not exist "exports" mkdir exports
if not exist "exports\reports" mkdir exports\reports
if not exist "exports\excel" mkdir exports\excel
if not exist "backups" mkdir backups
if not exist "uploads" mkdir uploads
if not exist "static\uploads" mkdir static\uploads
if not exist "logs" mkdir logs

echo ✅ تم إنشاء جميع المجلدات

echo.
echo ============================================================
echo 🧪 اختبار النظام...
echo ============================================================

echo 🔄 اختبار تشغيل النظام...

python -c "from app import app; print('✅ النظام جاهز للتشغيل')" 2>nul

if %errorlevel% neq 0 (
    echo ⚠️  تحذير: قد تكون هناك مشاكل في النظام
    echo 🔍 جاري فحص مفصل...
    
    if exist "check_system.py" (
        python check_system.py
    ) else (
        echo 💡 شغل النظام وتحقق من عمله يدوياً
    )
) else (
    echo ✅ اختبار النظام ناجح
)

echo.
echo ============================================================
echo 🎉 تم التثبيت بنجاح!
echo ============================================================
echo.
echo 📋 معلومات النظام:
echo    📁 مجلد النظام: %CD%
echo    🌐 عنوان النظام: http://127.0.0.1:8888
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo    👤 المدير: admin / admin123
echo    👤 المحاسب: accountant / acc123
echo    👤 الكاشير: cashier / cash123
echo.
echo 🚀 لتشغيل النظام:
echo    📄 انقر نقراً مزدوجاً على: start.bat
echo    أو
echo    📄 انقر نقراً مزدوجاً على: run_system.bat
echo.
echo ============================================================
echo 💡 نصائح مهمة:
echo ============================================================
echo.
echo 🔒 الأمان:
echo    • غيّر كلمة مرور المدير بعد أول تسجيل دخول
echo    • لا تشارك بيانات الدخول مع غير المخولين
echo.
echo 💾 النسخ الاحتياطية:
echo    • قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
echo    • استخدم ميزة النسخ الاحتياطي في الإعدادات
echo.
echo 🔧 الصيانة:
echo    • استخدم أدوات إدارة قاعدة البيانات في الإعدادات
echo    • راقب مساحة القرص الصلب
echo.
echo 📖 المساعدة:
echo    • راجع ملف README.md للتفاصيل الكاملة
echo    • راجع ملف INSTALLATION_GUIDE.md لحل المشاكل
echo.
echo ============================================================

echo.
set /p choice="هل تريد تشغيل النظام الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 جاري تشغيل النظام...
    echo.
    if exist "start.bat" (
        call start.bat
    ) else (
        python app.py
    )
) else (
    echo.
    echo 💡 يمكنك تشغيل النظام لاحقاً بالنقر على start.bat
)

echo.
echo 🎊 شكراً لاستخدام نظام إدارة قطع الغيار!
pause
