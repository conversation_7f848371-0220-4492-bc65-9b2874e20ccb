@echo off
title AutoParts Manager V5.1 - Installation

echo.
echo ============================================================
echo AutoParts Manager V5.1 - Installation
echo ============================================================
echo.
echo This program will install the complete system on your computer
echo including all requirements and necessary configurations
echo.
echo Make sure you have internet connection before continuing
echo.
pause

echo.
echo ============================================================
echo Checking system requirements...
echo ============================================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed on the system
    echo.
    echo Please download and install Python from:
    echo    https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Python is installed
python --version

REM Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: pip is not available
    echo Installing pip...
    python -m ensurepip --upgrade
)

echo SUCCESS: pip is available
pip --version

echo.
echo ============================================================
echo Creating virtual environment...
echo ============================================================

REM Create virtual environment
if exist "venv" (
    echo Removing existing virtual environment...
    rmdir /s /q venv
)

echo Creating new virtual environment...
python -m venv venv

if %errorlevel% neq 0 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

echo SUCCESS: Virtual environment created

echo.
echo ============================================================
echo Activating virtual environment and installing requirements...
echo ============================================================

REM Activate virtual environment and install requirements
call venv\Scripts\activate.bat

echo Upgrading pip to latest version...
python -m pip install --upgrade pip

echo Installing basic requirements...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo WARNING: Failed to install from requirements.txt
    echo Trying to install packages individually...
    
    pip install Flask==2.3.3
    pip install Flask-SQLAlchemy==3.0.5
    pip install Flask-Login==0.6.3
    pip install Werkzeug==2.3.7
    pip install SQLAlchemy==2.0.21
    pip install Jinja2==3.1.2
    pip install MarkupSafe==2.1.3
    pip install itsdangerous==2.1.2
    pip install click==8.1.7
    pip install blinker==1.6.3
    pip install python-barcode==0.15.1
    pip install Pillow==10.0.1
    pip install reportlab==4.0.4
    pip install openpyxl==3.1.2
    pip install pandas==2.1.1
    pip install PyInstaller==6.0.0
    pip install hijri-converter==2.3.1
    pip install python-dateutil==2.8.2
    pip install requests==2.31.0
)

echo SUCCESS: All requirements installed

echo.
echo ============================================================
echo Setting up database...
echo ============================================================

REM Initialize database if it doesn't exist
if not exist "autoparts.db" (
    echo Creating initial database...
    python init_db.py
    
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create database
        echo Trying manual database creation...
        python -c "from app import app, db; app.app_context().push(); db.create_all(); print('Database created successfully')"
    )
    
    echo SUCCESS: Database created
) else (
    echo SUCCESS: Database already exists
)

echo.
echo ============================================================
echo Creating required directories...
echo ============================================================

REM Create required directories
if not exist "exports" mkdir exports
if not exist "exports\reports" mkdir exports\reports
if not exist "exports\excel" mkdir exports\excel
if not exist "backups" mkdir backups
if not exist "uploads" mkdir uploads
if not exist "static\uploads" mkdir static\uploads
if not exist "logs" mkdir logs

echo SUCCESS: All required directories created

echo.
echo ============================================================
echo Testing system...
echo ============================================================

echo Testing system startup...
timeout /t 2 /nobreak >nul

REM Test if the system can start
python -c "from app import app; print('SUCCESS: System is ready to run')" 2>nul

if %errorlevel% neq 0 (
    echo WARNING: System test failed
    echo Checking for errors...
    python -c "from app import app; print('System works')"
    pause
) else (
    echo SUCCESS: System test passed
)

echo.
echo ============================================================
echo Installation completed successfully!
echo ============================================================
echo.
echo System Information:
echo    System Directory: %CD%
echo    Python Version: 
python --version
echo    System URL: http://127.0.0.1:8888
echo    Default Admin User: admin
echo    Default Admin Password: admin123
echo.
echo To start the system:
echo    1. Double-click on "start.bat"
echo    or
echo    2. Open command prompt in this directory and run:
echo       venv\Scripts\activate
echo       python app.py
echo.
echo For more information, check README.md
echo.
echo ============================================================
echo Important Tips:
echo ============================================================
echo.
echo For best performance:
echo    - Close antivirus temporarily during operation
echo    - Use Chrome or Firefox for best experience
echo    - Ensure sufficient disk space (at least 1GB)
echo.
echo For security:
echo    - Change admin password after first login
echo    - Create regular database backups
echo    - Do not share login credentials with unauthorized persons
echo.
echo For maintenance:
echo    - Use database management tools in settings
echo    - Monitor log files and report sizes
echo    - Update system when new versions are available
echo.
echo ============================================================

pause
echo.
echo Thank you for using AutoParts Manager!
echo    We hope you have a great experience with the system
echo ============================================================
pause
