# -*- coding: utf-8 -*-
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from models import db, Account, JournalEntry, Payment, Expense, ExpenseCategory, CashTransaction
from utils import login_required, permission_required, generate_invoice_number, log_audit, export_to_excel, create_pdf_report
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_

accounting_bp = Blueprint('accounting', __name__)

@accounting_bp.route('/')
@login_required
@permission_required('accounting')
def index():
    # Get financial summary
    today = datetime.now().date()
    month_start = today.replace(day=1)

    # Cash balance
    cash_in = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type.in_(['sale', 'collection', 'deposit'])
    ).scalar() or 0

    cash_out = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type.in_(['purchase', 'expense', 'payment', 'withdrawal'])
    ).scalar() or 0

    cash_balance = cash_in - cash_out

    # Monthly totals
    monthly_sales = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type == 'sale',
        CashTransaction.transaction_date >= month_start
    ).scalar() or 0

    monthly_expenses = db.session.query(func.sum(CashTransaction.amount)).filter(
        CashTransaction.transaction_type == 'expense',
        CashTransaction.transaction_date >= month_start
    ).scalar() or 0

    monthly_profit = monthly_sales - monthly_expenses

    # Recent transactions
    recent_transactions = CashTransaction.query.order_by(
        CashTransaction.transaction_date.desc()
    ).limit(10).all()

    return render_template('accounting/index.html',
                         total_revenue=monthly_sales,
                         total_expenses=monthly_expenses,
                         net_profit=monthly_profit,
                         cash_balance=cash_balance,
                         recent_transactions=recent_transactions)

@accounting_bp.route('/chart-of-accounts')
@login_required
@permission_required('accounting')
def chart_of_accounts():
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()

    # Calculate balances for display
    cash_balance = 10000.0  # Default values
    inventory_value = 50000.0
    customers_balance = 15000.0
    suppliers_balance = 8000.0
    retained_earnings = 25000.0
    sales_revenue = 75000.0
    cogs = 45000.0
    operating_expenses = 12000.0
    admin_expenses = 8000.0
    other_expenses = 3000.0

    # Calculate totals
    total_assets = cash_balance + inventory_value + customers_balance
    total_liabilities = suppliers_balance
    total_equity = retained_earnings
    net_income = sales_revenue - cogs - operating_expenses - admin_expenses - other_expenses

    return render_template('accounting/chart_of_accounts.html',
                         accounts=accounts,
                         cash_balance=cash_balance,
                         inventory_value=inventory_value,
                         customers_balance=customers_balance,
                         suppliers_balance=suppliers_balance,
                         retained_earnings=retained_earnings,
                         sales_revenue=sales_revenue,
                         cogs=cogs,
                         operating_expenses=operating_expenses,
                         admin_expenses=admin_expenses,
                         other_expenses=other_expenses,
                         total_assets=total_assets,
                         total_liabilities=total_liabilities,
                         total_equity=total_equity,
                         net_income=net_income)

@accounting_bp.route('/add-account', methods=['GET', 'POST'])
@login_required
@permission_required('accounting')
def add_account():
    if request.method == 'POST':
        account_code = request.form.get('account_code')
        name = request.form.get('name')
        account_type = request.form.get('account_type')
        parent_id = request.form.get('parent_id', type=int)
        description = request.form.get('description', '')
        is_active = request.form.get('is_active') == 'on'

        if not all([account_code, name, account_type]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            accounts = Account.query.filter_by(is_active=True).all()
            return render_template('accounting/add_account.html', accounts=accounts)

        # Check if code already exists
        if Account.query.filter_by(code=account_code).first():
            flash('رقم الحساب موجود مسبقاً', 'error')
            accounts = Account.query.filter_by(is_active=True).all()
            return render_template('accounting/add_account.html', accounts=accounts)

        account = Account(
            code=account_code,
            name=name,
            account_type=account_type,
            parent_id=parent_id if parent_id else None,
            description=description,
            is_active=is_active
        )

        db.session.add(account)
        db.session.commit()

        # Log audit
        log_audit(session['user_id'], 'إضافة حساب', 'accounts', account.id,
                 new_values={'code': account_code, 'name': name})

        flash('تم إضافة الحساب بنجاح', 'success')
        return redirect(url_for('accounting.chart_of_accounts'))

    accounts = Account.query.filter_by(is_active=True).all()
    return render_template('accounting/add_account.html', accounts=accounts)

@accounting_bp.route('/journal-entries')
@login_required
@permission_required('accounting')
def journal_entries():
    page = request.args.get('page', 1, type=int)
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')
    account_id = request.args.get('account_id', type=int)

    query = JournalEntry.query

    if from_date:
        query = query.filter(JournalEntry.transaction_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        query = query.filter(JournalEntry.transaction_date <= datetime.strptime(to_date, '%Y-%m-%d'))
    if account_id:
        query = query.filter_by(account_id=account_id)

    entries = query.order_by(JournalEntry.transaction_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()

    # Calculate totals
    total_debits = sum(entry.debit_amount for entry in entries.items if entry.debit_amount)
    total_credits = sum(entry.credit_amount for entry in entries.items if entry.credit_amount)

    return render_template('accounting/journal_entries.html',
                         journal_entries=entries,
                         accounts=accounts,
                         from_date=from_date,
                         to_date=to_date,
                         account_id=account_id,
                         total_debits=total_debits,
                         total_credits=total_credits)

@accounting_bp.route('/add-journal-entry', methods=['GET', 'POST'])
@login_required
@permission_required('accounting')
def add_journal_entry():
    if request.method == 'POST':
        transaction_date = request.form.get('transaction_date')
        description = request.form.get('description')
        account_ids = request.form.getlist('account_id[]')
        debit_amounts = request.form.getlist('debit_amount[]')
        credit_amounts = request.form.getlist('credit_amount[]')
        entry_descriptions = request.form.getlist('entry_description[]')

        if not all([transaction_date, description]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
            return render_template('accounting/add_journal_entry.html', accounts=accounts, today=datetime.now().strftime('%Y-%m-%d'))

        # Validate entries
        valid_entries = []
        total_debits = 0
        total_credits = 0

        for i in range(len(account_ids)):
            if account_ids[i]:  # Only process entries with selected accounts
                debit = float(debit_amounts[i]) if debit_amounts[i] else 0
                credit = float(credit_amounts[i]) if credit_amounts[i] else 0

                if debit > 0 or credit > 0:  # Only add entries with amounts
                    valid_entries.append({
                        'account_id': int(account_ids[i]),
                        'debit_amount': debit,
                        'credit_amount': credit,
                        'entry_description': entry_descriptions[i] if i < len(entry_descriptions) else ''
                    })
                    total_debits += debit
                    total_credits += credit

        if len(valid_entries) < 2:
            flash('يجب إدخال قيدين على الأقل', 'error')
            accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
            return render_template('accounting/add_journal_entry.html', accounts=accounts, today=datetime.now().strftime('%Y-%m-%d'))

        if abs(total_debits - total_credits) > 0.01:
            flash('مجموع المدين يجب أن يساوي مجموع الدائن', 'error')
            accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
            return render_template('accounting/add_journal_entry.html', accounts=accounts, today=datetime.now().strftime('%Y-%m-%d'))

        try:
            # Generate a base entry number for this transaction
            base_entry_number = generate_invoice_number('JE')
            entry_counter = 1

            for entry_data in valid_entries:
                # Create unique entry number for each journal entry
                unique_entry_number = f"{base_entry_number}-{entry_counter:02d}"

                entry = JournalEntry(
                    entry_number=unique_entry_number,
                    account_id=entry_data['account_id'],
                    transaction_date=datetime.strptime(transaction_date, '%Y-%m-%d'),
                    description=description,
                    debit_amount=entry_data['debit_amount'],
                    credit_amount=entry_data['credit_amount'],
                    user_id=session['user_id']
                )
                db.session.add(entry)
                entry_counter += 1

                # Update account balance
                account = Account.query.get(entry_data['account_id'])
                if account:
                    if account.account_type in ['assets', 'expenses']:
                        account.balance += entry.debit_amount - entry.credit_amount
                    else:  # liabilities, equity, revenue
                        account.balance += entry.credit_amount - entry.debit_amount

            db.session.commit()

            # Log audit
            log_audit(session['user_id'], 'إضافة قيد يومية', 'journal_entries', None,
                     new_values={'base_entry_number': base_entry_number, 'total_amount': total_debits})

            flash('تم إضافة القيد بنجاح', 'success')
            return redirect(url_for('accounting.journal_entries'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
    return render_template('accounting/add_journal_entry.html', accounts=accounts, today=datetime.now().strftime('%Y-%m-%d'))

@accounting_bp.route('/view-journal-entry/<int:entry_id>')
@login_required
@permission_required('accounting')
def view_journal_entry(entry_id):
    """View journal entry details"""
    entry = JournalEntry.query.get_or_404(entry_id)

    # Get all entries with the same entry_number (grouped entries)
    related_entries = JournalEntry.query.filter_by(entry_number=entry.entry_number).all()

    # Calculate totals
    total_debits = sum(e.debit_amount for e in related_entries)
    total_credits = sum(e.credit_amount for e in related_entries)

    return render_template('accounting/view_journal_entry.html',
                         entry=entry,
                         related_entries=related_entries,
                         total_debits=total_debits,
                         total_credits=total_credits)

@accounting_bp.route('/general-ledger')
@login_required
@permission_required('accounting')
def general_ledger():
    account_id = request.args.get('account_id', type=int)
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()

    if account_id:
        account = Account.query.get_or_404(account_id)

        query = JournalEntry.query.filter_by(account_id=account_id)

        if from_date:
            query = query.filter(JournalEntry.transaction_date >= datetime.strptime(from_date, '%Y-%m-%d'))
        if to_date:
            query = query.filter(JournalEntry.transaction_date <= datetime.strptime(to_date, '%Y-%m-%d'))

        entries = query.order_by(JournalEntry.transaction_date).all()

        # Calculate running balance
        running_balance = 0
        for entry in entries:
            if account.account_type in ['assets', 'expenses']:
                running_balance += entry.debit_amount - entry.credit_amount
            else:
                running_balance += entry.credit_amount - entry.debit_amount
            entry.running_balance = running_balance

        # Calculate totals
        total_debits = sum(entry.debit_amount for entry in entries)
        total_credits = sum(entry.credit_amount for entry in entries)
        final_balance = running_balance
        opening_balance = 0  # Default

        return render_template('accounting/general_ledger.html',
                             accounts=accounts,
                             selected_account=account,
                             ledger_entries=entries,
                             from_date=from_date,
                             to_date=to_date,
                             total_debits=total_debits,
                             total_credits=total_credits,
                             final_balance=final_balance,
                             opening_balance=opening_balance)

    return render_template('accounting/general_ledger.html',
                         accounts=accounts,
                         from_date=from_date,
                         to_date=to_date)

@accounting_bp.route('/trial-balance')
@login_required
@permission_required('accounting')
def trial_balance():
    as_of_date = request.args.get('as_of_date')

    if as_of_date:
        as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d')
    else:
        as_of_date = datetime.now()

    # Get all accounts with their balances
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()

    trial_balance_data = []
    total_debits = 0
    total_credits = 0

    for account in accounts:
        # Calculate balance as of date
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= as_of_date
        ).all()

        balance = 0
        for entry in entries:
            if account.account_type in ['assets', 'expenses']:
                balance += entry.debit_amount - entry.credit_amount
            else:
                balance += entry.credit_amount - entry.debit_amount

        if balance != 0:
            if balance > 0:
                if account.account_type in ['assets', 'expenses']:
                    debit_balance = balance
                    credit_balance = 0
                else:
                    debit_balance = 0
                    credit_balance = balance
            else:
                if account.account_type in ['assets', 'expenses']:
                    debit_balance = 0
                    credit_balance = abs(balance)
                else:
                    debit_balance = abs(balance)
                    credit_balance = 0

            trial_balance_data.append({
                'account': account,
                'debit_balance': debit_balance,
                'credit_balance': credit_balance
            })

            total_debits += debit_balance
            total_credits += credit_balance

    # Check if balanced
    is_balanced = abs(total_debits - total_credits) < 0.01
    balance_difference = total_debits - total_credits

    # Calculate totals by account type
    assets_total = sum(item['debit_balance'] for item in trial_balance_data
                      if item['account'].account_type == 'assets')
    liabilities_total = sum(item['credit_balance'] for item in trial_balance_data
                           if item['account'].account_type == 'liabilities')
    equity_total = sum(item['credit_balance'] for item in trial_balance_data
                      if item['account'].account_type == 'equity')
    revenue_total = sum(item['credit_balance'] for item in trial_balance_data
                       if item['account'].account_type == 'revenue')

    # Create totals object
    totals = {
        'opening_debit': 0,
        'opening_credit': 0,
        'period_debit': total_debits,
        'period_credit': total_credits,
        'closing_debit': total_debits,
        'closing_credit': total_credits
    }

    return render_template('accounting/trial_balance.html',
                         trial_balance=trial_balance_data,
                         totals=totals,
                         total_debits=total_debits,
                         total_credits=total_credits,
                         is_balanced=is_balanced,
                         balance_difference=balance_difference,
                         assets_total=assets_total,
                         liabilities_total=liabilities_total,
                         equity_total=equity_total,
                         revenue_total=revenue_total,
                         from_date=request.args.get('from_date'),
                         to_date=request.args.get('to_date'),
                         current_date=datetime.now())

# Export routes
@accounting_bp.route('/trial-balance/export')
@login_required
@permission_required('accounting')
def export_trial_balance():
    """Export trial balance to Excel or PDF"""
    export_format = request.args.get('export', 'excel')
    as_of_date = request.args.get('as_of_date')

    if as_of_date:
        as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d')
    else:
        as_of_date = datetime.now()

    # Get trial balance data
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()

    trial_balance_data = []
    total_debits = 0
    total_credits = 0

    for account in accounts:
        entries = JournalEntry.query.filter(
            JournalEntry.account_id == account.id,
            JournalEntry.transaction_date <= as_of_date
        ).all()

        balance = 0
        for entry in entries:
            if account.account_type in ['assets', 'expenses']:
                balance += entry.debit_amount - entry.credit_amount
            else:
                balance += entry.credit_amount - entry.debit_amount

        if balance != 0:
            if balance > 0:
                if account.account_type in ['assets', 'expenses']:
                    debit_balance = balance
                    credit_balance = 0
                else:
                    debit_balance = 0
                    credit_balance = balance
            else:
                if account.account_type in ['assets', 'expenses']:
                    debit_balance = 0
                    credit_balance = abs(balance)
                else:
                    debit_balance = abs(balance)
                    credit_balance = 0

            trial_balance_data.append({
                'رقم الحساب': account.code,
                'اسم الحساب': account.name,
                'نوع الحساب': account.account_type,
                'المدين': debit_balance,
                'الدائن': credit_balance
            })

            total_debits += debit_balance
            total_credits += credit_balance

    # Add totals row
    trial_balance_data.append({
        'رقم الحساب': '',
        'اسم الحساب': 'الإجمالي',
        'نوع الحساب': '',
        'المدين': total_debits,
        'الدائن': total_credits
    })

    filename = f"trial_balance_{as_of_date.strftime('%Y%m%d')}"
    title = f"ميزان المراجعة كما في {as_of_date.strftime('%Y-%m-%d')}"

    if export_format == 'pdf':
        filepath = create_pdf_report(
            [[item['رقم الحساب'], item['اسم الحساب'], item['نوع الحساب'],
              f"{item['المدين']:.2f}", f"{item['الدائن']:.2f}"] for item in trial_balance_data],
            title,
            filename,
            ['رقم الحساب', 'اسم الحساب', 'نوع الحساب', 'المدين', 'الدائن']
        )
    else:
        filepath = export_to_excel(trial_balance_data, filename, title)

    if filepath:
        return send_file(filepath, as_attachment=True)
    else:
        flash('حدث خطأ في تصدير التقرير', 'error')
        return redirect(url_for('accounting.trial_balance'))

@accounting_bp.route('/general-ledger/export')
@login_required
@permission_required('accounting')
def export_general_ledger():
    """Export general ledger to Excel or PDF"""
    export_format = request.args.get('export', 'excel')
    account_id = request.args.get('account_id', type=int)
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')

    if not account_id:
        flash('يرجى اختيار حساب', 'error')
        return redirect(url_for('accounting.general_ledger'))

    account = Account.query.get_or_404(account_id)

    query = JournalEntry.query.filter_by(account_id=account_id)

    if from_date:
        query = query.filter(JournalEntry.transaction_date >= datetime.strptime(from_date, '%Y-%m-%d'))
    if to_date:
        query = query.filter(JournalEntry.transaction_date <= datetime.strptime(to_date, '%Y-%m-%d'))

    entries = query.order_by(JournalEntry.transaction_date).all()

    # Prepare data for export
    ledger_data = []
    running_balance = 0

    for entry in entries:
        if account.account_type in ['assets', 'expenses']:
            running_balance += entry.debit_amount - entry.credit_amount
        else:
            running_balance += entry.credit_amount - entry.debit_amount

        ledger_data.append({
            'التاريخ': entry.transaction_date.strftime('%Y-%m-%d'),
            'رقم القيد': entry.entry_number,
            'الوصف': entry.description,
            'المدين': entry.debit_amount,
            'الدائن': entry.credit_amount,
            'الرصيد': running_balance
        })

    filename = f"general_ledger_{account.code}_{datetime.now().strftime('%Y%m%d')}"
    title = f"دفتر الأستاذ - {account.name} ({account.code})"

    if export_format == 'pdf':
        filepath = create_pdf_report(
            [[item['التاريخ'], item['رقم القيد'], item['الوصف'],
              f"{item['المدين']:.2f}", f"{item['الدائن']:.2f}", f"{item['الرصيد']:.2f}"] for item in ledger_data],
            title,
            filename,
            ['التاريخ', 'رقم القيد', 'الوصف', 'المدين', 'الدائن', 'الرصيد']
        )
    else:
        filepath = export_to_excel(ledger_data, filename, title)

    if filepath:
        return send_file(filepath, as_attachment=True)
    else:
        flash('حدث خطأ في تصدير التقرير', 'error')
        return redirect(url_for('accounting.general_ledger'))
