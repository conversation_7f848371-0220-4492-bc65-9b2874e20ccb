#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير Excel للمنتجات
Test Excel Export for Products
"""

import requests
import time
import os
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class ExcelExportTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_products_page(self):
        """اختبار صفحة المنتجات"""
        print("\n🔄 اختبار صفحة المنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/")
            if response.status_code == 200:
                print("✅ صفحة المنتجات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في صفحة المنتجات: {e}")
            return False
    
    def test_excel_export(self):
        """اختبار تصدير Excel للمنتجات"""
        print("\n🔄 اختبار تصدير Excel للمنتجات...")
        try:
            response = self.session.get(f"{BASE_URL}/products/export")
            
            if response.status_code == 200:
                # تحقق من نوع المحتوى
                content_type = response.headers.get('content-type', '')
                content_disposition = response.headers.get('content-disposition', '')
                
                print(f"📄 نوع المحتوى: {content_type}")
                print(f"📄 Content-Disposition: {content_disposition}")
                
                # تحقق من أن الملف هو Excel
                if ('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type or
                    'application/octet-stream' in content_type):
                    
                    # تحقق من حجم الملف
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:  # ملف Excel يجب أن يكون أكبر من 1KB
                        # حفظ الملف للتحقق
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        test_filename = f'test_products_export_{timestamp}.xlsx'
                        
                        with open(test_filename, 'wb') as f:
                            f.write(response.content)
                        
                        print(f"✅ تم تصدير Excel بنجاح")
                        print(f"📁 تم حفظ الملف: {test_filename}")
                        
                        # تنظيف ملف الاختبار
                        try:
                            os.remove(test_filename)
                            print(f"🗑️ تم حذف ملف الاختبار")
                        except:
                            pass
                        
                        return True
                    else:
                        print("❌ حجم الملف صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح للـ Excel: {content_type}")
                    # طباعة جزء من المحتوى للتشخيص
                    print(f"📄 بداية المحتوى: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ فشل في تصدير Excel: {response.status_code}")
                print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تصدير Excel: {e}")
            return False
    
    def test_template_download(self):
        """اختبار تحميل قالب الاستيراد"""
        print("\n🔄 اختبار تحميل قالب الاستيراد...")
        try:
            response = self.session.get(f"{BASE_URL}/products/download-template")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if ('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type or
                    'application/octet-stream' in content_type):
                    
                    file_size = len(response.content)
                    if file_size > 1000:
                        print("✅ تحميل قالب الاستيراد يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم قالب الاستيراد صغير جداً")
                        return False
                else:
                    print(f"❌ نوع محتوى قالب الاستيراد غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في تحميل قالب الاستيراد: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحميل قالب الاستيراد: {e}")
            return False
    
    def test_export_with_no_products(self):
        """اختبار التصدير عند عدم وجود منتجات"""
        print("\n🔄 اختبار التصدير مع عدم وجود منتجات...")
        try:
            # هذا الاختبار افتراضي - في الواقع يجب أن تكون هناك منتجات
            # لكن يمكننا اختبار السلوك
            response = self.session.get(f"{BASE_URL}/products/export")
            
            if response.status_code == 200:
                print("✅ التصدير يعمل حتى مع وجود منتجات")
                return True
            elif response.status_code == 302:
                print("✅ إعادة توجيه صحيحة عند عدم وجود منتجات")
                return True
            else:
                print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار عدم وجود منتجات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات تصدير Excel"""
        print("📊 بدء اختبار تصدير Excel للمنتجات")
        print("=" * 50)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("صفحة المنتجات", self.test_products_page),
            ("تصدير Excel للمنتجات", self.test_excel_export),
            ("تحميل قالب الاستيراد", self.test_template_download),
            ("التصدير مع عدم وجود منتجات", self.test_export_with_no_products),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار تصدير Excel:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف تصدير Excel تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة في تصدير Excel")
            return False

def main():
    """الدالة الرئيسية"""
    print("📊 اختبار تصدير Excel للمنتجات - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = ExcelExportTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
