@echo off
chcp 65001 >nul
title نظام إدارة قطع الغيار - AutoParts Manager V5.1

echo.
echo ============================================================
echo 🚀 نظام إدارة قطع الغيار - AutoParts Manager V5.1
echo ============================================================
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ❌ البيئة الافتراضية غير موجودة
    echo 🔧 يرجى تشغيل ملف install.bat أولاً لتثبيت النظام
    echo.
    pause
    exit /b 1
)

REM Check if database exists
if not exist "autoparts.db" (
    echo ⚠️  قاعدة البيانات غير موجودة
    echo 🔨 جاري إنشاء قاعدة البيانات...

    call venv\Scripts\activate.bat
    python -c "from app import app, db; app.app_context().push(); db.create_all(); print('✅ تم إنشاء قاعدة البيانات بنجاح')"

    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)

echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo 🔍 فحص المتطلبات...
python -c "import flask, flask_sqlalchemy, reportlab, openpyxl; print('✅ جميع المتطلبات متوفرة')" 2>nul

if %errorlevel% neq 0 (
    echo ❌ بعض المتطلبات مفقودة
    echo 🔧 جاري تثبيت المتطلبات المفقودة...
    pip install -r requirements.txt
)

echo.
echo ============================================================
echo 🌐 بدء تشغيل النظام...
echo ============================================================
echo.
echo 💡 يمكن الوصول للنظام من العناوين التالية:
echo    📍 المحلي: http://127.0.0.1:8888
echo    📍 الشبكة المحلية: http://192.168.x.x:8888
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo ⚠️  لإيقاف النظام: اضغط Ctrl+C
echo.
echo ============================================================

REM Start the application
python app.py

echo.
echo ============================================================
echo 🛑 تم إيقاف النظام
echo ============================================================
pause
