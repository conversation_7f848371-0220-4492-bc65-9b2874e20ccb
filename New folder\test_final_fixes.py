#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للإصلاحات النهائية
Final Comprehensive Test for All Fixes
"""

import requests
import time
import os
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class FinalTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_product_edit(self):
        """اختبار تعديل المنتج (تم إصلاحه)"""
        print("\n🔄 اختبار تعديل المنتج...")
        try:
            # Test GET request to edit page
            response = self.session.get(f"{BASE_URL}/products/edit/1")
            if response.status_code == 200:
                print("✅ صفحة تعديل المنتج تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في صفحة تعديل المنتج: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تعديل المنتج: {e}")
            return False
    
    def test_pdf_export(self):
        """اختبار تصدير PDF (تم إصلاحه)"""
        print("\n🔄 اختبار تصدير PDF...")
        try:
            response = self.session.get(f"{BASE_URL}/reports/sales-report?from_date=2025-05-01&to_date=2025-05-24&export=pdf")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'application/pdf' in content_type:
                    print("✅ تصدير PDF يعمل بنجاح")
                    return True
                elif 'application/octet-stream' in content_type:
                    # Check if it's actually a PDF by content
                    if response.content.startswith(b'%PDF'):
                        print("✅ تصدير PDF يعمل بنجاح (PDF content detected)")
                        return True
                    else:
                        print("❌ تصدير PDF يُرجع ملف غير PDF")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في تصدير PDF: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تصدير PDF: {e}")
            return False
    
    def test_barcode_print_page(self):
        """اختبار صفحة طباعة الباركود (جديد)"""
        print("\n🔄 اختبار صفحة طباعة الباركود...")
        try:
            response = self.session.get(f"{BASE_URL}/products/print-barcode/1?count=5")
            if response.status_code == 200:
                if 'طباعة الباركود' in response.text:
                    print("✅ صفحة طباعة الباركود تعمل بنجاح")
                    return True
                else:
                    print("❌ صفحة طباعة الباركود لا تحتوي على المحتوى المتوقع")
                    return False
            else:
                print(f"❌ فشل في صفحة طباعة الباركود: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة طباعة الباركود: {e}")
            return False
    
    def test_database_operations(self):
        """اختبار عمليات قاعدة البيانات (تم إصلاحها)"""
        print("\n🔄 اختبار عمليات قاعدة البيانات...")
        
        operations = [
            ('analyze-database', 'تحليل قاعدة البيانات'),
            ('check-integrity', 'فحص سلامة البيانات'),
        ]
        
        success_count = 0
        
        for operation, name in operations:
            try:
                response = self.session.post(f"{BASE_URL}/settings/{operation}")
                if response.status_code == 302:  # Redirect after success
                    print(f"✅ {name} يعمل بنجاح")
                    success_count += 1
                else:
                    print(f"❌ فشل في {name}: {response.status_code}")
            except Exception as e:
                print(f"❌ خطأ في {name}: {e}")
        
        if success_count == len(operations):
            print("✅ جميع عمليات قاعدة البيانات تعمل بنجاح")
            return True
        else:
            print(f"⚠️ {success_count}/{len(operations)} عمليات تعمل")
            return success_count > 0
    
    def test_backup_functionality(self):
        """اختبار النسخ الاحتياطي (تم إصلاحه)"""
        print("\n🔄 اختبار النسخ الاحتياطي...")
        try:
            response = self.session.get(f"{BASE_URL}/settings/backup")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'application/octet-stream' in content_type or 'application/x-sqlite3' in content_type:
                    file_size = len(response.content)
                    if file_size > 1000:
                        print("✅ النسخ الاحتياطي يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم النسخة الاحتياطية صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    return False
            else:
                print(f"❌ فشل في النسخ الاحتياطي: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في النسخ الاحتياطي: {e}")
            return False
    
    def test_product_add(self):
        """اختبار إضافة منتج (تم إصلاحه)"""
        print("\n🔄 اختبار إضافة منتج...")
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            product_data = {
                'name': f'منتج اختبار نهائي {timestamp}',
                'description': 'وصف منتج اختبار نهائي',
                'category_id': 1,
                'cost_price': 15.50,
                'selling_price': 25.75,
                'quantity': 50,
                'min_quantity': 5,
                'unit': 'قطعة',
                'location': 'A2-01'
            }
            
            response = self.session.post(f"{BASE_URL}/products/add", data=product_data)
            
            if response.status_code == 302 or (response.status_code == 200 and 'تم إضافة المنتج بنجاح' in response.text):
                print("✅ إضافة المنتج تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في إضافة المنتج: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إضافة المنتج: {e}")
            return False
    
    def test_barcode_generation(self):
        """اختبار إنشاء الباركود (تم إصلاحه)"""
        print("\n🔄 اختبار إنشاء الباركود...")
        try:
            # Test barcode generation function directly
            import sys
            sys.path.append('.')
            from utils import generate_barcode
            
            barcode_number, barcode_path = generate_barcode(999, "Final Test Product")
            
            if barcode_number and barcode_path:
                # Check if barcode file exists
                if os.path.exists(barcode_path):
                    print("✅ إنشاء الباركود يعمل بنجاح")
                    print(f"📁 ملف الباركود: {barcode_path}")
                    return True
                else:
                    print(f"❌ ملف الباركود غير موجود: {barcode_path}")
                    return False
            else:
                print("❌ فشل في إنشاء الباركود")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء الباركود: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات النهائية"""
        print("🛠️ بدء الاختبار الشامل النهائي لجميع الإصلاحات")
        print("=" * 70)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("تعديل المنتج", self.test_product_edit),
            ("تصدير PDF", self.test_pdf_export),
            ("صفحة طباعة الباركود", self.test_barcode_print_page),
            ("عمليات قاعدة البيانات", self.test_database_operations),
            ("النسخ الاحتياطي", self.test_backup_functionality),
            ("إضافة منتج", self.test_product_add),
            ("إنشاء الباركود", self.test_barcode_generation),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(1)  # انتظار ثانية بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 70)
        print("📊 نتائج الاختبار الشامل النهائي:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع الإصلاحات تعمل بنجاح 100%!")
            print("🚀 النظام جاهز للاستخدام الكامل!")
            return True
        elif passed > failed:
            print(f"\n✅ معظم الإصلاحات تعمل بنجاح ({passed}/{passed+failed})")
            print("⚠️ يوجد بعض المشاكل البسيطة")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة تحتاج إلى مراجعة")
            return False

def main():
    """الدالة الرئيسية"""
    print("🛠️ الاختبار الشامل النهائي - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = FinalTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
