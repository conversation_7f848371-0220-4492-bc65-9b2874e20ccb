{% extends "base.html" %}

{% block title %}إدارة الموردين - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-truck me-2"></i>
        إدارة الموردين
    </h1>
    <div>
        {% if has_permission('all') %}
        <a href="{{ url_for('suppliers.add') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة مورد جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" 
                       value="{{ request.args.get('search', '') }}" 
                       placeholder="اسم المورد أو رقم الهاتف">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع المورد</label>
                <select class="form-select" name="supplier_type">
                    <option value="">جميع الأنواع</option>
                    <option value="individual" {% if request.args.get('supplier_type') == 'individual' %}selected{% endif %}>
                        فردي
                    </option>
                    <option value="company" {% if request.args.get('supplier_type') == 'company' %}selected{% endif %}>
                        شركة
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة الرصيد</label>
                <select class="form-select" name="balance_status">
                    <option value="">جميع الحالات</option>
                    <option value="positive" {% if request.args.get('balance_status') == 'positive' %}selected{% endif %}>
                        رصيد موجب
                    </option>
                    <option value="negative" {% if request.args.get('balance_status') == 'negative' %}selected{% endif %}>
                        رصيد سالب
                    </option>
                    <option value="zero" {% if request.args.get('balance_status') == 'zero' %}selected{% endif %}>
                        رصيد صفر
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Suppliers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة الموردين ({{ suppliers.total if suppliers.total else 0 }})</h5>
    </div>
    <div class="card-body">
        {% if suppliers and (suppliers.items if suppliers.items else suppliers) %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>نوع المورد</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>العنوان</th>
                        <th>الرصيد</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for supplier in (suppliers.items if suppliers.items else suppliers) %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div>
                                    <strong>{{ supplier.name }}</strong>
                                    {% if supplier.company_name %}
                                    <br><small class="text-muted">{{ supplier.company_name }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if supplier.supplier_type == 'individual' %}
                                <span class="badge bg-info">فردي</span>
                            {% else %}
                                <span class="badge bg-success">شركة</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.phone %}
                                <a href="tel:{{ supplier.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ supplier.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.email %}
                                <a href="mailto:{{ supplier.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ supplier.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.address %}
                                <small>{{ supplier.address[:30] }}{% if supplier.address|length > 30 %}...{% endif %}</small>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.balance > 0 %}
                                <span class="text-success fw-bold">{{ supplier.balance|currency }}</span>
                            {% elif supplier.balance < 0 %}
                                <span class="text-danger fw-bold">{{ supplier.balance|currency }}</span>
                            {% else %}
                                <span class="text-muted">{{ supplier.balance|currency }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد' }}</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('suppliers.view', supplier_id=supplier.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                {% if has_permission('all') %}
                                <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteSupplier({{ supplier.id }}, '{{ supplier.name }}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Summary -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ suppliers.total if suppliers.total else 0 }}</h4>
                        <p class="mb-0">إجمالي الموردين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ (suppliers.items if suppliers.items else suppliers)|selectattr('balance', 'greaterthan', 0)|list|length }}</h4>
                        <p class="mb-0">رصيد موجب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>{{ (suppliers.items if suppliers.items else suppliers)|selectattr('balance', 'lessthan', 0)|list|length }}</h4>
                        <p class="mb-0">رصيد سالب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ ((suppliers.items if suppliers.items else suppliers)|sum(attribute='balance'))|currency }}</h4>
                        <p class="mb-0">إجمالي الأرصدة</p>
                    </div>
                </div>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد موردين</h5>
            <p class="text-muted">لم يتم العثور على موردين مطابقين لمعايير البحث</p>
            {% if has_permission('all') %}
            <a href="{{ url_for('suppliers.add') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة مورد جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المورد <strong id="supplierName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteSupplier(supplierId, supplierName) {
    document.getElementById('supplierName').textContent = supplierName;
    document.getElementById('deleteForm').action = `/suppliers/${supplierId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Auto-hide alerts
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>
{% endblock %}
