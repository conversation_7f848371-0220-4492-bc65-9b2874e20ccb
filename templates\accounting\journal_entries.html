{% extends "base.html" %}

{% block title %}القيود اليومية - النظام المحاسبي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-book me-2"></i>
        القيود اليومية
    </h1>
    <div>
        <a href="{{ url_for('accounting.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمحاسبة
        </a>
        {% if has_permission('all') %}
        <a href="{{ url_for('accounting.add_journal_entry') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة قيد جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            فلترة القيود
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="from_date"
                       value="{{ request.args.get('from_date', '') }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="to_date"
                       value="{{ request.args.get('to_date', '') }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع القيد</label>
                <select class="form-select" name="entry_type">
                    <option value="">جميع الأنواع</option>
                    <option value="sale" {% if request.args.get('entry_type') == 'sale' %}selected{% endif %}>
                        مبيعات
                    </option>
                    <option value="purchase" {% if request.args.get('entry_type') == 'purchase' %}selected{% endif %}>
                        مشتريات
                    </option>
                    <option value="expense" {% if request.args.get('entry_type') == 'expense' %}selected{% endif %}>
                        مصروفات
                    </option>
                    <option value="payment" {% if request.args.get('entry_type') == 'payment' %}selected{% endif %}>
                        مدفوعات
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Journal Entries -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">سجل القيود اليومية</h5>
    </div>
    <div class="card-body">
        {% if journal_entries %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم القيد</th>
                        <th>التاريخ</th>
                        <th>الوصف</th>
                        <th>النوع</th>
                        <th>المدين</th>
                        <th>الدائن</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in journal_entries %}
                    <tr>
                        <td><strong>{{ entry.id }}</strong></td>
                        <td>{{ entry.date.strftime('%Y-%m-%d') if entry.date else 'غير محدد' }}</td>
                        <td>{{ entry.description or 'غير محدد' }}</td>
                        <td>
                            {% if entry.type == 'sale' %}
                                <span class="badge bg-success">مبيعات</span>
                            {% elif entry.type == 'purchase' %}
                                <span class="badge bg-primary">مشتريات</span>
                            {% elif entry.type == 'expense' %}
                                <span class="badge bg-danger">مصروفات</span>
                            {% elif entry.type == 'payment' %}
                                <span class="badge bg-info">مدفوعات</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ entry.type }}</span>
                            {% endif %}
                        </td>
                        <td class="text-success">{{ entry.debit_amount|currency if entry.debit_amount else '-' }}</td>
                        <td class="text-danger">{{ entry.credit_amount|currency if entry.credit_amount else '-' }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('accounting.view_journal_entry', entry_id=entry.id) }}"
                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if has_permission('all') %}
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="deleteEntry({{ entry.id }})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="4">الإجمالي</th>
                        <th class="text-success">{{ total_debits|currency }}</th>
                        <th class="text-danger">{{ total_credits|currency }}</th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Pagination -->
        {% if journal_entries.pages > 1 %}
        <nav aria-label="صفحات القيود">
            <ul class="pagination justify-content-center">
                {% if journal_entries.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('accounting.journal_entries', page=journal_entries.prev_num) }}">السابق</a>
                </li>
                {% endif %}

                {% for page_num in journal_entries.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != journal_entries.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('accounting.journal_entries', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if journal_entries.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('accounting.journal_entries', page=journal_entries.next_num) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قيود</h5>
            <p class="text-muted">لم يتم تسجيل أي قيود يومية بعد</p>
            {% if has_permission('all') %}
            <a href="{{ url_for('accounting.add_journal_entry') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ journal_entries.total if journal_entries.total else 0 }}</h4>
                <p class="mb-0">إجمالي القيود</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ total_debits|currency }}</h4>
                <p class="mb-0">إجمالي المدين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4>{{ total_credits|currency }}</h4>
                <p class="mb-0">إجمالي الدائن</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ (total_debits - total_credits)|currency }}</h4>
                <p class="mb-0">الفرق</p>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا القيد؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteEntry(entryId) {
    document.getElementById('deleteForm').action = `/accounting/journal-entries/${entryId}/delete`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
