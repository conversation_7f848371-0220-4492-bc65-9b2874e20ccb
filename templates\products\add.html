{% extends "base.html" %}

{% block title %}إضافة منتج جديد - نظام إدارة قطع الغيار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus me-2"></i>
        إضافة منتج جديد
    </h1>
    <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">بيانات المنتج</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="category_id" class="form-label">التصنيف <span class="text-danger">*</span></label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">اختر التصنيف</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cost_price" class="form-label">سعر التكلفة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                       step="0.01" min="0">
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="selling_price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="quantity" class="form-label">الكمية الحالية</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   min="0" value="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="min_quantity" class="form-label">الحد الأدنى للكمية</label>
                            <input type="number" class="form-control" id="min_quantity" name="min_quantity" 
                                   min="0" value="5">
                            <div class="form-text">سيتم التنبيه عند الوصول لهذا الحد</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">الوحدة</label>
                            <select class="form-select" id="unit" name="unit">
                                <option value="قطعة">قطعة</option>
                                <option value="كيلو">كيلو</option>
                                <option value="لتر">لتر</option>
                                <option value="متر">متر</option>
                                <option value="علبة">علبة</option>
                                <option value="كرتون">كرتون</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label">موقع التخزين</label>
                        <input type="text" class="form-control" id="location" name="location" 
                               placeholder="مثال: رف A - مستوى 2">
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    نصائح مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        سيتم توليد باركود تلقائياً للمنتج
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكن تعديل جميع البيانات لاحقاً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        تأكد من دقة سعر البيع
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-info text-info me-2"></i>
                        الحد الأدنى للكمية يساعد في التنبيه المبكر
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('products.categories') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-tags me-1"></i>
                        إدارة التصنيفات
                    </a>
                    <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>
                        عرض جميع المنتجات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate profit margin
document.getElementById('cost_price').addEventListener('input', calculateMargin);
document.getElementById('selling_price').addEventListener('input', calculateMargin);

function calculateMargin() {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (costPrice > 0 && sellingPrice > 0) {
        const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
        const profit = (sellingPrice - costPrice).toFixed(2);
        
        // You can add a margin display element here if needed
        console.log(`Profit: ${profit} SAR, Margin: ${margin}%`);
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (costPrice > 0 && sellingPrice <= costPrice) {
        e.preventDefault();
        alert('سعر البيع يجب أن يكون أكبر من سعر التكلفة');
        return false;
    }
});
</script>
{% endblock %}
