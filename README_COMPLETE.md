# 🚀 نظام إدارة قطع الغيار المتكامل - AutoParts Manager V5.1

## 📋 نظرة عامة

نظام إدارة قطع الغيار المتكامل هو حل شامل لإدارة أعمال قطع الغيار والسيارات. يوفر النظام جميع الأدوات اللازمة لإدارة المخزون، المبيعات، العملاء، الموردين، والحسابات في واجهة واحدة سهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🏪 نقطة البيع (POS)
- واجهة بيع سريعة وسهلة الاستخدام
- دعم الباركود والبحث السريع
- حساب الضرائب تلقائياً
- طباعة الفواتير
- إدارة طرق الدفع المختلفة

### 📦 إدارة المخزون
- تتبع المخزون في الوقت الفعلي
- تنبيهات المخزون المنخفض
- تعديلات المخزون مع سجل كامل
- تحويلات المخزون بين المواقع
- تقارير حركة المخزون

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء والموردين
- تتبع تاريخ المعاملات
- إدارة الحسابات الجارية
- استيراد وتصدير البيانات من/إلى Excel

### 💰 النظام المحاسبي
- دليل حسابات متكامل
- القيود اليومية التلقائية
- دفتر الأستاذ العام
- ميزان المراجعة
- القوائم المالية (قائمة الدخل، الميزانية العمومية)

### 📊 التقارير والتحليلات
- تقارير المبيعات اليومية والشهرية
- تقارير المخزون والحركة
- تقارير الأرباح والخسائر
- تقارير التدفق النقدي
- تصدير التقارير إلى Excel وPDF

### 🔐 إدارة المستخدمين والصلاحيات
- نظام أدوار متقدم (مدير، محاسب، كاشير)
- صلاحيات مخصصة لكل مستخدم
- سجل مراجعة شامل للعمليات
- تغيير كلمات المرور

### 🌐 دعم الشبكة المحلية
- الوصول من أي جهاز في الشبكة
- دعم عدة عناوين IP
- واجهة متجاوبة للأجهزة المحمولة

## 🛠️ التقنيات المستخدمة

- **Backend**: Python Flask
- **Database**: SQLite
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **UI Framework**: Bootstrap 5 مع دعم RTL
- **Charts**: Chart.js
- **Icons**: Font Awesome
- **Export**: pandas, openpyxl, reportlab

## 📋 متطلبات النظام

### الحد الأدنى:
- نظام التشغيل: Windows 10 أو أحدث
- المعالج: Intel Core i3 أو معادل
- الذاكرة: 4 جيجابايت RAM
- مساحة القرص: 2 جيجابايت
- الشبكة: اتصال بالإنترنت (للتثبيت الأولي)

### المستحسن:
- نظام التشغيل: Windows 11
- المعالج: Intel Core i5 أو أحدث
- الذاكرة: 8 جيجابايت RAM أو أكثر
- مساحة القرص: 5 جيجابايت أو أكثر
- الشبكة: شبكة محلية سريعة

## 🚀 طرق التثبيت

### الطريقة الأولى: التثبيت التلقائي (مستحسن)
1. حمل ملف `AutoParts_Manager_Complete.zip`
2. استخرج الملفات إلى مجلد على سطح المكتب
3. انقر نقراً مزدوجاً على `تثبيت_سريع.bat`
4. اتبع التعليمات على الشاشة
5. بعد انتهاء التثبيت، انقر على `تشغيل_النظام.bat`

### الطريقة الثانية: التثبيت اليدوي
1. تثبيت Python 3.8 أو أحدث
2. استنساخ المشروع أو تحميل الملفات
3. تثبيت المتطلبات: `pip install -r requirements.txt`
4. تشغيل النظام: `python app.py`

### الطريقة الثالثة: النسخة المحمولة (EXE)
1. حمل ملف `AutoParts_Portable.zip`
2. استخرج الملفات
3. انقر نقراً مزدوجاً على `تشغيل_النظام.bat`

## 🔧 التشغيل والاستخدام

### تشغيل النظام:
```bash
python app.py
```

### الوصول للنظام:
- **المحلي**: http://127.0.0.1:8888
- **الشبكة المحلية**: http://[IP_ADDRESS]:8888

### بيانات تسجيل الدخول الافتراضية:

#### المدير (صلاحيات كاملة):
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

#### المحاسب (الحسابات والتقارير):
- **اسم المستخدم**: accountant  
- **كلمة المرور**: acc123

#### الكاشير (نقطة البيع فقط):
- **اسم المستخدم**: cashier
- **كلمة المرور**: cash123

## 📁 هيكل المشروع

```
AutoParts Manager V5.1/
├── app.py                 # التطبيق الرئيسي
├── auth.py               # نظام المصادقة
├── models.py             # نماذج قاعدة البيانات
├── utils.py              # الوظائف المساعدة
├── requirements.txt      # متطلبات Python
├── installer.py          # برنامج التثبيت
├── build_exe.py         # بناء الملف التنفيذي
├── routes/              # ملفات التوجيه
│   ├── main.py          # الصفحة الرئيسية
│   ├── pos.py           # نقطة البيع
│   ├── products.py      # إدارة المنتجات
│   ├── customers.py     # إدارة العملاء
│   ├── suppliers.py     # إدارة الموردين
│   ├── inventory.py     # إدارة المخزون
│   ├── accounting.py    # النظام المحاسبي
│   ├── reports.py       # التقارير
│   └── settings.py      # الإعدادات
├── templates/           # قوالب HTML
├── static/             # الملفات الثابتة
├── instance/           # قاعدة البيانات
└── backups/           # النسخ الاحتياطية
```

## 🔒 الأمان والنسخ الاحتياطي

### إعدادات الأمان:
- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- تسجيل العمليات

### النسخ الاحتياطي:
- نسخ احتياطية تلقائية يومية
- استعادة سريعة للبيانات
- تصدير البيانات إلى Excel
- حفظ النسخ في مجلد منفصل

## 🌐 دعم الشبكة المحلية

النظام يدعم الوصول من عدة أجهزة في الشبكة المحلية:

### إعداد الشبكة:
1. تأكد من تشغيل النظام على الخادم الرئيسي
2. اعرف عنوان IP للخادم
3. افتح المتصفح على أي جهاز في الشبكة
4. اذهب إلى: `http://[SERVER_IP]:8888`

### مثال:
إذا كان IP الخادم `*************`:
```
http://*************:8888
```

## 📱 دعم الأجهزة المحمولة

النظام متوافق تماماً مع الأجهزة المحمولة:
- واجهة متجاوبة
- دعم اللمس
- تحسين للشاشات الصغيرة
- سرعة في التحميل

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### النظام لا يعمل:
- تأكد من تثبيت Python
- تحقق من تثبيت المتطلبات
- شغل البرنامج كمدير

#### لا يمكن الوصول من الشبكة:
- تحقق من إعدادات جدار الحماية
- تأكد من أن المنفذ 8888 مفتوح
- تحقق من عنوان IP

#### بطء في الأداء:
- أغلق البرامج غير الضرورية
- تأكد من وجود مساحة كافية
- اعمل نسخة احتياطية وأعد التشغيل

## 📞 الدعم الفني

للحصول على الدعم الفني:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567
- **الموقع**: www.autoparts.com

## 📄 الترخيص

هذا النظام محمي بحقوق الطبع والنشر.
جميع الحقوق محفوظة © 2024 AutoParts Manager

## 🔄 التحديثات

### الإصدار 5.1 (الحالي):
- إضافة نظام إدارة المخزون المتقدم
- تحسين واجهة نقطة البيع
- دعم الشبكة المحلية (Multi-IP)
- تحسين النظام المحاسبي
- إضافة تقارير جديدة

### التحديثات القادمة:
- دعم قواعد البيانات السحابية
- تطبيق الهاتف المحمول
- نظام إشعارات متقدم
- تكامل مع أنظمة خارجية

---

**تم تطوير النظام بواسطة فريق AutoParts Manager**  
**الإصدار**: 5.1  
**تاريخ الإصدار**: مايو 2024
