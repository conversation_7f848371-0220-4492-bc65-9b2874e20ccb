{% extends "base.html" %}

{% block title %}إضافة قيد يومي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة قيد يومي جديد
                    </h3>
                    <a href="{{ url_for('accounting.journal_entries') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقيود اليومية
                    </a>
                </div>

                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate id="journalForm">
                        <!-- Transaction Info -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="transaction_date" class="form-label">تاريخ المعاملة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="transaction_date" name="transaction_date"
                                       value="{{ today }}" required>
                                <div class="invalid-feedback">يرجى إدخال تاريخ المعاملة</div>
                            </div>
                            <div class="col-md-8">
                                <label for="description" class="form-label">وصف المعاملة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="description" name="description"
                                       placeholder="مثال: بيع بضاعة نقداً" required>
                                <div class="invalid-feedback">يرجى إدخال وصف المعاملة</div>
                            </div>
                        </div>

                        <!-- Journal Entries -->
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">تفاصيل القيد</h5>
                                <button type="button" class="btn btn-light btn-sm" onclick="addEntry()">
                                    <i class="fas fa-plus"></i> إضافة سطر
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-bordered mb-0" id="entriesTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="40%">الحساب</th>
                                                <th width="20%">مدين</th>
                                                <th width="20%">دائن</th>
                                                <th width="15%">البيان</th>
                                                <th width="5%">حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="entriesBody">
                                            <!-- Initial entry rows will be added by JavaScript -->
                                        </tbody>
                                        <tfoot class="table-secondary">
                                            <tr>
                                                <th>الإجمالي</th>
                                                <th id="totalDebit">0.00</th>
                                                <th id="totalCredit">0.00</th>
                                                <th colspan="2">
                                                    <span id="balanceStatus" class="badge bg-warning">غير متوازن</span>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Balance Alert -->
                        <div class="alert alert-warning mt-3" id="balanceAlert">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تنبيه:</strong> يجب أن يكون مجموع المدين مساوياً لمجموع الدائن لحفظ القيد.
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ url_for('accounting.journal_entries') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                <i class="fas fa-save"></i> حفظ القيد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let entryCounter = 0;
const accounts = [
    {% for account in accounts %}
    {
        id: {{ account.id }},
        name: "{{ account.name }}",
        code: "{{ account.code }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
];

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    addEntry(); // Add first entry
    addEntry(); // Add second entry
});

function addEntry() {
    entryCounter++;
    const tbody = document.getElementById('entriesBody');
    const row = document.createElement('tr');
    row.id = `entry-${entryCounter}`;

    row.innerHTML = `
        <td>
            <select class="form-select" name="account_id[]" required onchange="updateTotals()">
                <option value="">اختر الحساب</option>
                ${accounts.map(account =>
                    `<option value="${account.id}">${account.account_code} - ${account.name}</option>`
                ).join('')}
            </select>
        </td>
        <td>
            <input type="number" class="form-control" name="debit_amount[]"
                   step="0.01" min="0" placeholder="0.00"
                   onchange="handleAmountChange(this, 'debit')" oninput="updateTotals()">
        </td>
        <td>
            <input type="number" class="form-control" name="credit_amount[]"
                   step="0.01" min="0" placeholder="0.00"
                   onchange="handleAmountChange(this, 'credit')" oninput="updateTotals()">
        </td>
        <td>
            <input type="text" class="form-control" name="entry_description[]"
                   placeholder="بيان اختياري">
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeEntry(${entryCounter})"
                    ${entryCounter <= 2 ? 'disabled' : ''}>
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);
    updateTotals();
}

function removeEntry(id) {
    const row = document.getElementById(`entry-${id}`);
    if (row && document.querySelectorAll('#entriesBody tr').length > 2) {
        row.remove();
        updateTotals();
    }
}

function handleAmountChange(input, type) {
    const row = input.closest('tr');
    const debitInput = row.querySelector('input[name="debit_amount[]"]');
    const creditInput = row.querySelector('input[name="credit_amount[]"]');

    if (type === 'debit' && input.value) {
        creditInput.value = '';
    } else if (type === 'credit' && input.value) {
        debitInput.value = '';
    }

    updateTotals();
}

function updateTotals() {
    const debitInputs = document.querySelectorAll('input[name="debit_amount[]"]');
    const creditInputs = document.querySelectorAll('input[name="credit_amount[]"]');

    let totalDebit = 0;
    let totalCredit = 0;

    debitInputs.forEach(input => {
        if (input.value) totalDebit += parseFloat(input.value);
    });

    creditInputs.forEach(input => {
        if (input.value) totalCredit += parseFloat(input.value);
    });

    document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
    document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);

    const isBalanced = totalDebit === totalCredit && totalDebit > 0;
    const balanceStatus = document.getElementById('balanceStatus');
    const balanceAlert = document.getElementById('balanceAlert');
    const submitBtn = document.getElementById('submitBtn');

    if (isBalanced) {
        balanceStatus.className = 'badge bg-success';
        balanceStatus.textContent = 'متوازن';
        balanceAlert.style.display = 'none';
        submitBtn.disabled = false;
    } else {
        balanceStatus.className = 'badge bg-warning';
        balanceStatus.textContent = 'غير متوازن';
        balanceAlert.style.display = 'block';
        submitBtn.disabled = true;
    }
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
