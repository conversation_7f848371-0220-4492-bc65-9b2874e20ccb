#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير PDF للفاتورة
Test Invoice PDF Export
"""

import requests
import time

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:8888"
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"

class InvoicePDFTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للنظام"""
        try:
            login_data = {
                'username': TEST_USERNAME,
                'password': TEST_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/auth/login", data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                self.logged_in = True
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_invoice_pdf_export(self, invoice_id=6):
        """اختبار تصدير PDF للفاتورة"""
        print(f"\n🔄 اختبار تصدير PDF للفاتورة رقم {invoice_id}...")
        try:
            response = self.session.get(f"{BASE_URL}/pos/export-invoice-pdf/{invoice_id}")
            
            print(f"📄 كود الاستجابة: {response.status_code}")
            print(f"📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                
                if 'application/pdf' in content_type or 'application/octet-stream' in content_type:
                    file_size = len(response.content)
                    print(f"📁 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:
                        print("✅ تصدير PDF للفاتورة يعمل بنجاح")
                        return True
                    else:
                        print("❌ حجم ملف PDF صغير جداً")
                        return False
                else:
                    print(f"❌ نوع المحتوى غير صحيح: {content_type}")
                    # طباعة جزء من المحتوى للتشخيص
                    print(f"📄 بداية المحتوى: {response.text[:300]}...")
                    return False
            else:
                print(f"❌ فشل في تصدير PDF: {response.status_code}")
                print(f"📄 محتوى الخطأ: {response.text[:300]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تصدير PDF: {e}")
            return False
    
    def test_invoice_page(self, invoice_id=6):
        """اختبار صفحة الفاتورة"""
        print(f"\n🔄 اختبار صفحة الفاتورة رقم {invoice_id}...")
        try:
            response = self.session.get(f"{BASE_URL}/pos/invoice/{invoice_id}")
            
            if response.status_code == 200:
                print("✅ صفحة الفاتورة تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة الفاتورة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة الفاتورة: {e}")
            return False
    
    def test_sales_history(self):
        """اختبار صفحة تاريخ المبيعات"""
        print("\n🔄 اختبار صفحة تاريخ المبيعات...")
        try:
            response = self.session.get(f"{BASE_URL}/pos/sales-history")
            
            if response.status_code == 200:
                print("✅ صفحة تاريخ المبيعات تعمل بنجاح")
                return True
            else:
                print(f"❌ فشل في الوصول لصفحة تاريخ المبيعات: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في صفحة تاريخ المبيعات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات PDF للفاتورة"""
        print("📊 بدء اختبار تصدير PDF للفاتورة")
        print("=" * 50)
        
        if not self.login():
            print("❌ فشل في تسجيل الدخول. توقف الاختبار.")
            return False
        
        tests = [
            ("صفحة تاريخ المبيعات", self.test_sales_history),
            ("صفحة الفاتورة", self.test_invoice_page),
            ("تصدير PDF للفاتورة", self.test_invoice_pdf_export),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n📋 {test_name}...")
                if test_func():
                    passed += 1
                else:
                    failed += 1
                time.sleep(2)  # انتظار ثانيتين بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل {test_name}: {e}")
                failed += 1
        
        print("\n" + "=" * 50)
        print("📊 نتائج اختبار PDF للفاتورة:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 جميع وظائف PDF للفاتورة تعمل بنجاح!")
            return True
        else:
            print(f"\n⚠️ يوجد {failed} مشكلة في PDF للفاتورة")
            return False

def main():
    """الدالة الرئيسية"""
    print("📊 اختبار تصدير PDF للفاتورة - نظام إدارة قطع الغيار")
    print(f"🌐 عنوان النظام: {BASE_URL}")
    print(f"👤 المستخدم: {TEST_USERNAME}")
    print()
    
    tester = InvoicePDFTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
