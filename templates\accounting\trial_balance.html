{% extends "base.html" %}

{% block title %}ميزان المراجعة - النظام المحاسبي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-balance-scale me-2"></i>
        ميزان المراجعة
    </h1>
    <div>
        <a href="{{ url_for('accounting.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمحاسبة
        </a>
        <a href="{{ url_for('accounting.trial_balance', export='excel') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>
            تصدير Excel
        </a>
        <a href="{{ url_for('accounting.trial_balance', export='pdf') }}" class="btn btn-danger">
            <i class="fas fa-file-pdf me-2"></i>
            تصدير PDF
        </a>
    </div>
</div>

<!-- Date Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-calendar me-2"></i>
            فترة التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="from_date"
                       value="{{ request.args.get('from_date', '') }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="to_date"
                       value="{{ request.args.get('to_date', '') }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calculator me-1"></i>
                        إعداد الميزان
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Trial Balance -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                ميزان المراجعة
                {% if from_date and to_date %}
                    <small class="text-muted">من {{ from_date }} إلى {{ to_date }}</small>
                {% endif %}
            </h5>
            <div class="text-muted">
                <small>تاريخ الإعداد: {{ current_date.strftime('%Y-%m-%d') }}</small>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if trial_balance %}
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th rowspan="2" class="align-middle text-center">كود الحساب</th>
                        <th rowspan="2" class="align-middle text-center">اسم الحساب</th>
                        <th colspan="2" class="text-center">الأرصدة الافتتاحية</th>
                        <th colspan="2" class="text-center">حركة الفترة</th>
                        <th colspan="2" class="text-center">الأرصدة الختامية</th>
                    </tr>
                    <tr>
                        <th class="text-center text-success">مدين</th>
                        <th class="text-center text-danger">دائن</th>
                        <th class="text-center text-success">مدين</th>
                        <th class="text-center text-danger">دائن</th>
                        <th class="text-center text-success">مدين</th>
                        <th class="text-center text-danger">دائن</th>
                    </tr>
                </thead>
                <tbody>
                    {% for account in trial_balance %}
                    <tr>
                        <td class="text-center"><strong>{{ account.code }}</strong></td>
                        <td>{{ account.name }}</td>
                        <td class="text-end text-success">
                            {% if account.debit_balance > 0 %}
                                {{ account.debit_balance|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end text-danger">
                            {% if account.credit_balance > 0 %}
                                {{ account.credit_balance|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end text-success">
                            {% if account.debit_balance > 0 %}
                                {{ account.debit_balance|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end text-danger">
                            {% if account.credit_balance > 0 %}
                                {{ account.credit_balance|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end text-success">
                            {% if account.debit_balance > 0 %}
                                <strong>{{ account.debit_balance|currency }}</strong>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end text-danger">
                            {% if account.credit_balance > 0 %}
                                <strong>{{ account.credit_balance|currency }}</strong>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="2" class="text-center">الإجمالي</th>
                        <th class="text-end text-success">{{ total_debits|currency }}</th>
                        <th class="text-end text-danger">{{ total_credits|currency }}</th>
                        <th class="text-end text-success">{{ total_debits|currency }}</th>
                        <th class="text-end text-danger">{{ total_credits|currency }}</th>
                        <th class="text-end text-success">{{ total_debits|currency }}</th>
                        <th class="text-end text-danger">{{ total_credits|currency }}</th>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Balance Check -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="card {% if is_balanced %}border-success{% else %}border-danger{% endif %}">
                    <div class="card-header {% if is_balanced %}bg-success text-white{% else %}bg-danger text-white{% endif %}">
                        <h6 class="mb-0">
                            <i class="fas {% if is_balanced %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %} me-2"></i>
                            حالة التوازن
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if is_balanced %}
                            <p class="text-success mb-0">
                                <i class="fas fa-check me-2"></i>
                                الميزان متوازن - إجمالي المدين يساوي إجمالي الدائن
                            </p>
                        {% else %}
                            <p class="text-danger mb-0">
                                <i class="fas fa-times me-2"></i>
                                الميزان غير متوازن - يوجد فرق بقيمة {{ balance_difference|currency }}
                            </p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><strong>عدد الحسابات:</strong> {{ trial_balance|length }}</li>
                            <li><strong>إجمالي المدين:</strong> <span class="text-success">{{ total_debits|currency }}</span></li>
                            <li><strong>إجمالي الدائن:</strong> <span class="text-danger">{{ total_credits|currency }}</span></li>
                            <li><strong>الفرق:</strong>
                                <span class="{% if balance_difference == 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ balance_difference|currency }}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات</h5>
            <p class="text-muted">لم يتم العثور على حسابات للفترة المحددة</p>
            <p class="text-muted">يرجى تحديد فترة زمنية أو التأكد من وجود قيود محاسبية</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Account Types Summary -->
{% if trial_balance %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4>{{ assets_total|currency }}</h4>
                <p class="mb-0">الأصول</p>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4>{{ liabilities_total|currency }}</h4>
                <p class="mb-0">الخصوم</p>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ equity_total|currency }}</h4>
                <p class="mb-0">حقوق الملكية</p>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>{{ revenue_total|currency }}</h4>
                <p class="mb-0">الإيرادات</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.table th, .table td {
    vertical-align: middle;
}

.table-bordered th, .table-bordered td {
    border: 1px solid #dee2e6;
}

@media print {
    .btn, .card-header .text-muted {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
